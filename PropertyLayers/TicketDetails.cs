﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using MongoDB.Bson.Serialization.Attributes;
namespace PropertyLayers
{
    [DataContract]
    public class TicketDetail
    {
        [DataMember(EmitDefaultValue = false)]
        public string id
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public long CustID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string CustName
        {
            get;
            set;
        }
        [BsonIgnoreIfNull]
        [DataMember(EmitDefaultValue = false)]
        public byte Status
        {
            get;
            set;
        }
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public Int16 PStatus
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string Description
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int64 Assgnto
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Source
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 SourceID
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 RefID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public enumTicketType TicketType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Category { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 CategoryID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Priority { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 PriorityID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 CreatedBy
        {
            get;
            set;
        }
        [DataMember]
        public bool IsActive
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime UpdatedON
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string Comment
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool hasFile
        {
            get;
            set;
        }
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public List<File_Attachment> Files
        {
            get;
            set;
        }
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public enumHistoryType hType
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string EmailID { get; set; }
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public string UID { get; set; }
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public string EmpCode { get; set; }
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public string EmpName { get; set; }
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public string ProductName { get; set; }

        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public string statusName
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public string AssgnToName
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public string CreatedByName
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime? AssignedOn
        {
            get;
            set;
        }
    }
    [DataContract]
    public enum enumHistoryType
    {
        COMMENTS = 0,
        ASSIGNMENT = 1,
        STATUS = 2
    }
    [DataContract]
    public enum enumTicketType
    {
        BLANK = 0,
        SALES = 1,
        SERVICE = 2
    }
}
