﻿using System;
using MongoDB.Bson.Serialization.Attributes;
using System.Runtime.Serialization;

namespace PropertyLayers
{
    [DataContract]
    public class EasyDialSoftPhoneResponse
    {
        [BsonIgnoreIfDefault]
        [DataMember]
        public long LeadID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string campaign { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string DialerCode { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string empId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public Int64 userId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string uid { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string Status { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string Phone { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string SF { get; set; }

        [DataMember]
        public bool IsUserLogin { get; set; }

        [DataMember]
        public bool IsThirdParty { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string CountryCode { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string Message { get; set; }

    }

    [DataContract]
    [Serializable]
    public class CallRequest
    {

        [DataMember(EmitDefaultValue = false)]
        public int roleId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool attempt { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool redial { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime apiCallTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string source { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short PriorityReasonId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool? callOnAswat { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EncryptedMobileNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CountryCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short? CountryId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AsteriskIP { get; set; }

        [DataMember(EmitDefaultValue =false)]
        public string VirtualNumber { get; set; }
    }

    [DataContract]
    [Serializable]
    public class CallRequestObj : CallRequest
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long userId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long customerId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool CallOnAswat { get; set; }
    }

    [DataContract]
    [Serializable]
    public class CallStatus
    {
        [DataMember(EmitDefaultValue = false)]
        public long leadid { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long userid { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string status { get; set; }
    }
}

