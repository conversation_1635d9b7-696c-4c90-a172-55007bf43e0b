﻿using System;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.Runtime.Serialization;

namespace PropertyLayers
{
    public class RetainerPredictiveDialing
    {
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime LeadCreateOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime LeadAssignOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime PreviousPolicyExpiryDate { get; set; }
        
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]        
        [BsonIgnoreIfDefault]
        public Int64 LeadID { get; set; }

        public int ProductId { get; set; }

        public string MobileNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int64 CustomerID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime ts { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public bool IsActive { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public bool IsDeleted { get; set; }
        [DataMember(EmitDefaultValue = false)]
        
        public int RetryCount { get; set; }
        
        [BsonIgnoreIfDefault]
        public string Status { get; set; }
        [BsonIgnoreIfDefault]
        public DateTime LastRetryTime { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string MakeName { get; set; }
        public string Context { get; set; }
        public Int16 LeadRank { get; set; }
        public Int64 C2CID { get; set; }
        public string Country { get; set; }
    }

    public class IdealPredictiveAgents
    {
        public string AgentCode { get; set; }
        public string DIDNo { get; set; }
        public Int16 Grade { get; set; }
        public string AgentIP { get; set; }
        public Int32 Counter { get; set; }
        public Int32 BucketSize { get; set; }
        public Int32 OpenLeadCount { get; set; }
        public string Status { get; set; }
        public byte IsPicked { get; set; }
    }
}
