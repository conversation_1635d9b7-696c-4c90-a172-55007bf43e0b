﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;

namespace PropertyLayers
{

    [DataContract]
    public class KafkaConfig
    {
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string BootstrapServers { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string UserName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Password { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Topic { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public int Timeout { get; set; }

    }
    [DataContract]
    public class LoggingRequest
    {
        [DataMember(EmitDefaultValue = false)]
        public string CollectionName
        {
            get;
            set;
        }


        [DataMember(EmitDefaultValue = false)]

        public string DbName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]

        public string ConnectionName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]

        public string RequestPayload
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]

        public string Method
        {
            get;
            set;
        }


    }

    [DataContract]
    public class KafkaRequestModel
    {
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Request { get; set; }
    }
}