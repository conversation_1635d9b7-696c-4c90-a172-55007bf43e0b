﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using MongoDB.Bson.Serialization.Attributes;

namespace PropertyLayers
{
    [DataContract]
    public class DialerDispDetails : Disposition
    {
        [BsonIgnoreIfDefault]
        [DataMember]
        public string CallTrackingID { get; set; }

        [BsonElement("_id")]
        [BsonIgnoreIfDefault]
        [DataMember]
        public string CallId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public long ParentID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public int ProductID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string AgentCode { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public DateTime callDate { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public bool IsBMS { get; set; }

        [DataMember]
        public string Context { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string dst { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string AsteriskIP { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public int CountryCode { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public int IsService { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public long LeadID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public Int32 Duration { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public Int32 talktime { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string CallType { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string recfile { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember]
        public string t_type { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public long CallingNo { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string rectype { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public int C2CID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public byte InsertCheckFlag { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string Disposition { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string Status { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember]
        public DateTime HangUpTime
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public string Action
        { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember]
        public string Channel
        {
            get;
            set;
        }
        [BsonIgnore]
        [DataMember]
        public bool NewMethod
        { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember]
        public Int64 NewLeadID
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public short TryCount
        { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember]
        public string CallingTime
        { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember]
        public string LeadSource
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public string MobileNo
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public bool IsFailover
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public Int16 ReasonID
        {
            get;set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public Int64 CustomerID
        {
            get; set;
        }

    }

    [DataContract]
    public class ASWATResponse
    {
        [DataMember(EmitDefaultValue = false)]
        public bool result { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Content content { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Info info { get; set; }

        [DataContract]
        public class Content
        {
            [DataMember(EmitDefaultValue = false)]
            public string callID { get; set; }
        }

        [DataContract]
        public class Info
        {
        }
    }

    [DataContract]
    public class Disposition
    {
        [BsonIgnoreIfDefault]
        [DataMember]
        public string Status
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public DateTime CreatedOn
        {
            get;
            set;
        }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string Name
        {
            get;
            set;
        }
    }
    [DataContract]
    public class RecordingSQS
    {
        [DataMember(EmitDefaultValue = false)]
        public string CallDataID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CallID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime CallDate { get; set; }

        [DataMember]
        public bool is_internal { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string company { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LeadID { get; set; }
    }


}

