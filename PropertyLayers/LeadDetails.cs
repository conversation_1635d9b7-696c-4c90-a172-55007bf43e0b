﻿using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace PropertyLayers
{
    [DataContract]
    public class LeadDetailResponse
    {
        [DataMember(EmitDefaultValue = false)]
        public LeadData LeadData
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public ClaimData ClaimData
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public PolicyDetails PolicyDetails
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public HealthRenewalBenefit HealthRenewalBenefit
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public BookingPolicyDetails BookingPolicyDetails
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public List<AppointmentDetail> AppointmentDetails
        {
            get;
            set;
        }
        [DataMember]
        public CompanyContact CompanyContactInfo
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public TicketDetail TicketDetail { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public SupplierDocUploadInfo SupplierDocUploadInfo
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        public HEXSelectionDetails HEXSelectionDetails
        {
            get;
            set;
        }

    }
    [DataContract]
    public class KVPair
    {
        [DataMember(EmitDefaultValue = false)]
        public string Key { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Value { get; set; }
    }
    [DataContract]
    public class ClaimData
    {
        [DataMember(EmitDefaultValue = false)]
        public string RegistrationNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CustomerName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ContactNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<KVPair> AttachmentsLinks { get; set; }
    }
    [DataContract]
    public class LeadData
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public long ParentLeadID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public long EnquiryId
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string EncrypEnquiryId
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        public int CoreLeadID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public int NeedID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public int EnquiryID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public long CustID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string CustomerName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public int GroupID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public DateTime LeadCreatedDateTime
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string ProductName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public Int32 LeadCreationDays
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string LeadSource
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string AgentID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string AgentName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string TLName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string TLID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string MobileNo
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public Int16 LeadStatusID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public Int16 SubStatusID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string TriggerName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string LeadStatusName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public DateTime DOB
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string Gender
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string Address
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string State { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string City { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PostCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AltPhoneNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmailId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MaritalStatus { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ContinueJourneyLink { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CallBackDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CallBackTime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ShortMobileNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Remarks { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MFlink { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string GM_CompanyName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ChatDepartmentID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 CustomerAge { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string InvestmentType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Country { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public byte invflag { get; set; }
        public string AnnualIncome
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string welcomemessage { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public byte cb { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MyAccBaseLink { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string HotLine { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EncryptLeadId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EncryptMobileNo
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Footer { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LogoUrl { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MyAccShortLink { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string TicketID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MyAccDocShortLink { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MyAccEndoShortLink { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public bool isexpired { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UtmSource
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        public bool newcar { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte SubProductId
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string SubProductName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string GroupType
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string Make { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Model { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string RegistrationNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string LastYearPolicyNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string LastYearPolicyExpiryDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 LeadRank { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public string VariantName { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string GroupName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MyAccEncData { get; set; }
    }

    [DataContract]
    public class PolicyDetails : commonPolicyDetails
    {
        [DataMember]
        public string PlanSelectionDate
        {
            get;
            set;
        }
        [DataMember]
        public string PolicyExpiredDate
        {
            get;
            set;
        }
        [DataMember]
        public Int32 PolicyExpiryDays
        {
            get;
            set;
        }
        [DataMember]
        public string PlanReviewURL { get; set; }
        [DataMember]
        public string PaymentLink { get; set; }
        [DataMember]
        public string FamilyType { get; set; }
        [DataMember]
        public string TwoYearPremium { get; set; }
    }

    [DataContract]
    public class CompanyContact
    {
        [DataMember]
        public string WebSiteLink
        {
            get;
            set;
        }
        [DataMember]
        public string TelPhoneNo
        {
            get;
            set;
        }
        [DataMember]
        public string TollFreeNo
        {
            get;
            set;
        }
        [DataMember]
        public string EmailTo
        {
            get;
            set;
        }
        [DataMember]
        public string Fax
        {
            get;
            set;
        }
        [DataMember]
        public string CustCareEmailTo { get; set; }
        [DataMember]
        public string MobileWebSiteLink { get; set; }
        [DataMember]
        public string ImageLogoURL { get; set; }
        [DataMember]
        public string ContactEmailTo { get; set; }
        [DataMember]
        public string TicketUrl { get; set; }
        [DataMember]
        public string WhatsApNo { get; set; }
        [DataMember]
        public string FooterEmail { get; set; }
        [DataMember]
        public string FooterContactNo { get; set; }

        [DataMember]
        public string Address { get; set; }

        [DataMember]
        public string ClaimTollFreeNo { get; set; }
    }

    [DataContract]
    public class BookingPolicyDetails : commonPolicyDetails
    {
        //[DataMember]
        //public string SumInsured { get; set; }
        [DataMember]
        public DateTime BookingDate { get; set; }
        [DataMember]
        public string PolicyTypeName { get; set; }
        [DataMember]
        public string ApplicationNo { get; set; }
        [DataMember]
        public string PolicyType { get; set; }
        [DataMember]
        public string UploadDocLink { get; set; }
        //[DataMember]
        //public string RegistrationNo { get; set; }
        [DataMember]
        public string MedicalScheduleLink { get; set; }
        [DataMember]
        public string SurveyURL { get; set; }
        [DataMember]
        public string SurveyLink { get; set; }
        [DataMember]
        public string DiscountVoucher { get; set; }
        [DataMember]
        public string BookingType { get; set; }
        [DataMember]
        public List<MailAttachments> MailAttachmentList { get; set; }
        [DataMember]
        public string CourierName { get; set; }
        [DataMember]
        public string TrackingNumber { get; set; }
        [DataMember]
        public string RefundCourierName { get; set; }
        [DataMember]
        public string RefundTrackingNumber { get; set; }
        [DataMember]
        public string MyAccLink
        { get; set; }
        [DataMember]
        public string MyAccLinkForDocUpload
        { get; set; }
        [DataMember]
        public string MyAccLinkForTracking
        { get; set; }
        [DataMember]
        public string RefundAmount
        { get; set; }
        [DataMember]
        public string RefundMode
        { get; set; }
        [DataMember]
        public short RefundModeKey { get; set; }

        [DataMember]
        public List<Document> DocumentDetails
        {
            get;
            set;
        }
        [DataMember]
        public bool IsMedRequired
        {
            get;
            set;
        }
        [DataMember]
        public string IHOCourierName { get; set; }
        [DataMember]
        public string IHOTrackingNumber { get; set; }
        [DataMember]
        public string SmsShortUrl { get; set; }

    }

    [DataContract]
    public class AppointmentDetail
    {
        [DataMember]
        public string SupplierName
        {
            get;
            set;
        }
        [DataMember]
        public Int64 SupplierId
        {
            get;
            set;
        }
        [DataMember]
        public string SupplierShortName
        {
            get;
            set;
        }
        [DataMember]
        public string AppointmentDate
        {
            get;
            set;
        }
        [DataMember]
        public string AppointmentTime
        {
            get;
            set;
        }
        [DataMember]
        public string CreatedBy
        {
            get;
            set;
        }
        [DataMember]
        public string AddressOption { get; set; }
        [DataMember]
        public int StatusID { get; set; }
        [DataMember]
        public string CityName { get; set; }
        [DataMember]
        public string Address { get; set; }
        [DataMember]
        public string AddressLine2 { get; set; }
        [DataMember]
        public string AddressLine3 { get; set; }
        [DataMember]
        public string Pincode { get; set; }
    }

    [DataContract]
    public class commonPolicyDetails
    {
        [DataMember]
        public string SupplierName
        {
            get;
            set;
        }
        [DataMember]
        public string PlanName
        {
            get;
            set;
        }
        [DataMember]
        public Int64 SupplierId
        {
            get;
            set;
        }
        [DataMember]
        public Int64 PlanId
        {
            get;
            set;
        }
        [DataMember]
        public string SupplierShortName
        {
            get;
            set;
        }
        [DataMember]
        public string PlanShortName
        {
            get;
            set;
        }
        [DataMember]
        public string Premium { get; set; }
        [DataMember]
        public string PolicyNo { get; set; }
        [DataMember]
        public string SumInsured { get; set; }
        [DataMember]
        public string Make { get; set; }
        [DataMember]
        public string Model { get; set; }
        [DataMember]
        public string RegistrationNo { get; set; }

    }

    [DataContract]
    public class SupplierDocUploadInfo
    {
        [DataMember]
        public string SupplierDocUpLoadLink
        {
            get;
            set;
        }
        [DataMember]
        public string SupplierEmailId
        {
            get;
            set;
        }
        [DataMember]
        public byte SupplierDocSize
        {
            get;
            set;
        }
    }


    [DataContract]
    public class HEXSelectionDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string InsurerName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public int InsurerID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public int PlanID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string EncryptPlanID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string PlanName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public bool IsDiseaseExlusion
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public bool IsDiseaseExlusionPerm
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string DiseaseExclusionPerm
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public bool IsDiseaseExlusionTemp
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string DiseaseExclusionTemp
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public bool IsLoadPremium
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string FinalPremium
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        public bool IsCoPay
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string CoPay
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string SumInsured
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public bool IsMemberExclusion
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public List<MemberExclusion> Members
        {
            get;
            set;
        }

    }

    [DataContract]
    public class MemberExclusion
    {
        [DataMember(EmitDefaultValue = false)]
        public string MemberName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string ExclusionRemarks
        {
            get;
            set;
        }
    }
    [DataContract]
    public class HealthRenewalBenefit
    {
        [DataMember(EmitDefaultValue = false)]
        public string PEDCoverage { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string HealthCheckUP { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SpecificDisease { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Bonus { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string RewardLoyaltyPoints { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AdditionalBenefits { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Others { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string NetworkHospitalsCovered { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string HospitalRoomEligibility { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PreHospitalization { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PostHospitalization { get; set; }

    }
    public enum MobileStatusEnum
    {
        Default = 0,
        Valid = 1,
        Suspicious = 2,
        InValid = 3
    }
    public enum EnumAppEvents
    {
        statusUpdate = 49,
        AddressUpdate = 50,
        custCommunication = 51,
        Calling = 52,
    }
    public class LeadCallDetailsDTO
    {
        public long ParentId;
        public int Totaltalktime;
        public int lastNtalktime;
        public int productId;
        public Int64 userID;
        public string LeadSource;
        public int TotalDuration;
        public MobileStatusEnum MobileStatus;
        public int TodayTalktime;
        public DateTime LastCallTime;
        public Int16 NANC_Attempts;
        public Int64 CustomerID;
    }
    [DataContract]
    public class File_Attachment
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string FileName
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 FileSize
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string AttachemntContent
        {
            get;
            set;
        }
    }

    [DataContract]
    [Serializable]
    public class MailAttachments : File_Attachment
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string FileID
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string AttachmentURL
        {
            get;
            set;
        }
    }

    [DataContract]
    public class AddLeadValidation
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string message { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int status { get; set; }
    }
    public class Result
    {
        public bool status { get; set; }
        public string message { get; set; }
    }

    [DataContract]
    public class DialerLeadDetailModel
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 ProductId
        {
            get;
            set;
        }
    }
    public class ResponseData<T>
    {
        [DataMember]
        public bool Status { get; set; }
        [DataMember]
        public string Message { get; set; }
        [DataMember]
        public T Data { get; set; }
        [DataMember]
        public Int16 Code { get; set; }
    }
}
