﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace PropertyLayers
{
    [DataContract]
    public class CallingData
    {
        [DataMember]
        public short CountryID { get; set; }

        [DataMember]
        public string MobileNumber { get; set; }

        [DataMember]
        public string CountryCode { get; set; }
    }

    [DataContract]
    public class CallingDataFields
    {
        [DataMember]
        public string MobileNo { get; set; }

        [DataMember]
        public string Country { get; set; }

        [DataMember]
        public int CountryCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int CountryId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsPrimary { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsCallable { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CreatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int CustMobId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EncryptedMobileNo { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public bool IsViewed { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsEmergencyContact { get; set; }
    }

    public class GetCallingNumberResult
    {
        public List<CallingDataFields> Data { get; set; }
    }

    public class CallingNoModel
    {
        public GetCallingNumberResult GetCallingNumberResult { get; set; }
    }

}

