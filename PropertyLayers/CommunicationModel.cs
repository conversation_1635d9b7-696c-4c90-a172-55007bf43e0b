﻿using System;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace PropertyLayers
{
    [DataContract]
    [Serializable]
    [BsonIgnoreExtraElements]
    public class CommunicationModel
    {

        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public bool Redial { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime apiCallTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string callingSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short PriorityReasonId { get; set; }

        [BsonIgnore]
        [DataMember]
        public string AsteriskIP { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short ProductID { get; set; }

        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public string LeadSource { get; set; }

        [BsonIgnore]
        [DataMember]
        public long CallTrackingID { get; set; }

        [DataMember]
        public List<Conversations> Conversations { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long UserID { get; set; }

        [DataMember(EmitDefaultValue =false)]
        public short SubProductId { get; set; } 
        [DataMember(EmitDefaultValue = false)]
        public string VirtualNumber { get; set; }
    }

    [DataContract]
    [Serializable]
    public class Conversations
    {
        [DataMember(EmitDefaultValue = false)]
        public List<string> ToReceipent { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public long UserID { get; set; }

        [BsonIgnoreIfNull]
        [DataMember(EmitDefaultValue = false)]
        public string AgentID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string CountryCode { get; set; }

        [BsonIgnoreIfNull]
        [DataMember(EmitDefaultValue = false)]
        public string Context { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string From { get; set; }
    }

    public class PreferenceStatus
    {
        public long LeadId { get; set; }
        public long MobileNo { get; set; }
        public long CustomerId { get; set; }
        public byte CommType { get; set; }
        public byte CategoryId { get; set; }
    }

    public enum CommunicationType
    {
        ALL = 0,
        Email = 1,
        SMS = 2,
        Call = 3,
        Chat = 4,
        OTP = 5,
        IBCall = 6,
        InboundCall = 7,
        WhatsApp = 8,
        VIDEOMEET = 9,
        SCREENSHARE = 10
    }
}