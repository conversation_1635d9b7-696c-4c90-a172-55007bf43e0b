﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using MongoDB.Bson;
namespace PropertyLayers
{
    [DataContract]
    public class Document
    {
        [DataMember]
        public int DocCategoryID
        {
            get;
            set;
        }
        [DataMember]
        public string DocCategoryName
        {
            get;
            set;
        }
        [DataMember]
        public int DocumentID
        {
            get;
            set;
        }
        [DataMember]
        public string DocumentName
        {
            get;
            set;
        }
        [DataMember]
        public string DocURL
        {
            get;
            set;
        }

        [DataMember]
        public string DocComment
        {
            get;
            set;
        }

        [DataMember]
        public List<Category> CategoryList
        {
            get;
            set;
        }

        [DataMember]
        public int[] DocumentIdList
        {
            get;
            set;
        }
    }
    [DataContract]
    public class Category
    {
        [DataMember]
        public int DocCategoryID
        {
            get;
            set;
        }


        [DataMember]
        public string DocCategoryName
        {
            get;
            set;
        }
        [DataMember]
        public int IsDisplay
        {
            get;
            set;
        }

        [DataMember]
        public int IsChecked
        {
            get;
            set;
        }

    }
}