﻿using System.Data;
using System.Dynamic;
using DataAccessLayer;
using DataAccessLibrary;
using Helper;
using PropertyLayers;
using ReadXmlProject;

namespace OBConnectCall;

public class ConnectCall
{
    public static EasyDialSoftPhoneResponse DialOBCall(CommunicationModel objConnectCall, bool isUAE, ref bool isCallViaDialerApp, bool callOnAswat = false, Int16 reasonId = 0)
    {
        string strException = string.Empty;
        string dataToPost = string.Empty;
        string result = string.Empty;
        DateTime RequestDatetime = DateTime.Now;
        EasyDialSoftPhoneResponse obj = new EasyDialSoftPhoneResponse();
        try
        {
            bool simPanelGroupsExp = Convert.ToBoolean("simPanelGroupsExp".AppSettings());
            List<int> simPanelGroups = "simPanelGroups".AppSettings().Split(',').Select(Int32.Parse).ToList();
            string context = "", IP = "", DIDNo = "", CallingCompany = "", VirtualNo = "", AswatPosition = "";
            int groupId = 0;
            bool iSWFH = false;
            short AgentCountry = 0;
            int countrycode = 0;
            int dialercode = 0;
            long callFromNumber = 0;
            string CallFromIP = string.Empty;
            string ResultContext = GetContextQueue(objConnectCall, ref context, ref IP, ref DIDNo, ref CallingCompany, ref groupId, ref VirtualNo, ref iSWFH, ref AgentCountry, ref AswatPosition);

            if(!string.IsNullOrEmpty(ResultContext) && ResultContext.ToLower().Contains("uae_sim"))
            {
                isUAE = true;
            }

            if (objConnectCall.AsteriskIP == null || objConnectCall.AsteriskIP == "")
            {
                objConnectCall.AsteriskIP = IP;
            }
            else
            {
                IP = objConnectCall.AsteriskIP;
            }

            PriorityModel _PriorityModel = LeadPrioritizationDLL.GetPriorityModelMongo(objConnectCall.LeadID);

            try
            {
                if (_PriorityModel != null
                    && !string.IsNullOrEmpty(_PriorityModel.LeadSource) && _PriorityModel.LeadSource.ToLower() == "renewal"
                    && _PriorityModel.CustEmergencyNo != null && _PriorityModel.CustEmergencyNo.CustMobId > 0)
                {

                    // Dial on emergency callable number for renewals, if marked callable
                    string? EncCustNumber = _PriorityModel.CustEmergencyNo.EncNumber;
                    int CountryCode = _PriorityModel.CustEmergencyNo.CountryCode;

                    if (!string.IsNullOrEmpty(EncCustNumber))
                    {
                        objConnectCall.Conversations[0].ToReceipent[0] = CryptoHelper.Decrytion_Payment_AES(EncCustNumber, "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings());
                    }

                    if (CountryCode > 0)
                    {
                        objConnectCall.Conversations[0].CountryCode = CountryCode.ToString();
                    }
                }

            }
            catch (Exception ex)
            {
                string strEx = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(null, objConnectCall.LeadID, strEx, "ConnectCall", "Onelead", "GetEmergencyCallableNum", dataToPost, string.Empty, RequestDatetime, DateTime.Now);

            }
            // dial call from the same number as last call
            if (_PriorityModel != null && _PriorityModel.Call != null && _PriorityModel.Call.calltime.Date == DateTime.Now.Date && _PriorityModel.CallBack != null && _PriorityModel.Call.TalkTime > 0 && _PriorityModel.Call.TotalTT > 120)
            {
                try
                {
                    callFromNumber = _PriorityModel.Call.CallingNo;
                    var ds = CallCommunicationDLL.GetDIDDetails(callFromNumber);
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        CallFromIP = ds.Tables[0].Rows[0]["ServerIp"] != null && ds.Tables[0].Rows[0]["ServerIp"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["ServerIp"]) : string.Empty;
                    }
                }
                catch {}

            }
            else if (simPanelGroupsExp && simPanelGroups.Contains(groupId))
            {                
                if (_PriorityModel != null && _PriorityModel.Call != null && _PriorityModel.Call.TodaysNANCAttempt == 3 && _PriorityModel.Call.TalkTime == 0)
                {
                    ResultContext = "nrisimpanel";
                }
            }
            else if (objConnectCall.Conversations[0].CountryCode == "1" && objConnectCall.ProductID==115 && _PriorityModel != null && _PriorityModel.Call == null) //first call of us- HIYA exp
            {             
                    ResultContext = "hiyaus";             
            }
            else if (objConnectCall.Conversations[0].CountryCode == "44" && objConnectCall.ProductID == 115 && _PriorityModel != null && _PriorityModel.Call == null) //first call of uk- HIYA exp
            {
                ResultContext = "hiyauk";
            }
            else if (objConnectCall.Conversations[0].CountryCode == "61" && objConnectCall.ProductID == 115 && _PriorityModel != null && _PriorityModel.Call == null) //first call of uk- HIYA exp
            {
                ResultContext = "hiyaaus";
            }
            else if (objConnectCall.Conversations[0].CountryCode == "64" && objConnectCall.ProductID == 115 && _PriorityModel != null && _PriorityModel.Call == null) //first call of uk- HIYA exp
            {
                ResultContext = "hiyanewz";
            }

            objConnectCall.Conversations[0].Context = ResultContext;

            objConnectCall.Conversations[0].From = GetDialerCode(objConnectCall);
            String URL = string.Empty;

            if (isUAE)
                URL = "WFHURL_UAE".AppSettings();
            else
                URL = "WFHURL".AppSettings();

            objConnectCall.CallTrackingID = 0;
            DialerDispDetails _DispositionUpdate = new()
            {
                CallId = "0",
                ParentID = objConnectCall.LeadID,
                ProductID = objConnectCall.ProductID,
                AgentCode = objConnectCall.Conversations[0].AgentID,
                callDate = DateTime.Now,
                IsBMS = false,
                ReasonID = reasonId
            };

            obj = GetSpecialCampigan(objConnectCall, ref ResultContext, ref CallingCompany, DIDNo, _DispositionUpdate, ref iSWFH);
            
            var DNContext = "";
            // if (_PriorityModel == null || _PriorityModel.Call == null || _PriorityModel.Call.TotalTT  < 180){
            DNContext = CallCommunicationDLL.DN_Context(Convert.ToInt64(objConnectCall.Conversations[0].ToReceipent[0]), objConnectCall.LeadID);
            // }
            
            if (callFromNumber > 0 && !string.IsNullOrEmpty(CallFromIP))
            {
                _DispositionUpdate.Context = "CallFromLastDID";
            }
            if (DNContext == "spvoda")
            {
                ResultContext = DNContext;
                _DispositionUpdate.Context = DNContext;
                objConnectCall.Conversations[0].Context = DNContext;
            }
            if (CallingCompany != null && CallingCompany.ToUpper() == "DIALER_APP")
            {
                if (DNContext == "spvoda" && objConnectCall.Conversations[0].CountryCode == "91")
                {
                    ResultContext = "DIALER_APP";
                    _DispositionUpdate.Context = "DIALER_APP";
                }
                else
                {
                    CallingCompany = "WEBPHONE";
                }
            }

            if (!string.IsNullOrEmpty(DIDNo) && !string.IsNullOrEmpty(CallingCompany)
                    && !string.IsNullOrEmpty(objConnectCall.Conversations[0].CountryCode) && objConnectCall.Conversations[0].CountryCode == "91")
                _DispositionUpdate.dst = objConnectCall.Conversations[0].ToReceipent[0];
            else
                _DispositionUpdate.dst = objConnectCall.Conversations[0].ToReceipent[0];
            _DispositionUpdate.AsteriskIP = objConnectCall.AsteriskIP;
            bool v = int.TryParse(objConnectCall.Conversations[0].CountryCode, out countrycode);
            v = int.TryParse(objConnectCall.Conversations[0].From, out dialercode);

            if (!string.IsNullOrEmpty(objConnectCall.Conversations[0].CountryCode) && objConnectCall.Conversations[0].CountryCode != "91")
                v = int.TryParse(Convert.ToString(dialercode), out countrycode);
            else
                v = int.TryParse(Convert.ToString(dialercode) + Convert.ToString(countrycode), out countrycode);

            _DispositionUpdate.CountryCode = countrycode;

            if(objConnectCall.ProductID==131)
            {
                _DispositionUpdate.Context = GetSmeContextForCalling(objConnectCall, _DispositionUpdate.Context);
            }

            objConnectCall.CallTrackingID = CallCommunicationDLL.InsertCallData(_DispositionUpdate);

            if (objConnectCall.CallTrackingID > 0 && (string.IsNullOrEmpty(obj.Status) || obj.Status.ToUpper() != "FAILED"))
            {
                obj.Status = "SUCCESS";

                if (CallingCompany == "ASWATINDIA")
                {
                    if (callOnAswat && string.IsNullOrEmpty(AswatPosition) == false)
                    {
                        CallingCompany = "ASWATINDIA";
                    }
                    else
                    {
                        CallingCompany = "WEBPHONE";
                    }
                }

                PrepareCallResponse(obj, objConnectCall);

                //if (CallingCompany != null && CallingCompany.ToUpper() == "DIALER_APP" &&) // for app calling
                //{
                //    obj.IsThirdParty = true;
                //}
                //else
                //{
                bool isBlocked = false;
                obj.IsThirdParty = ThirdPartyCalling(ref dataToPost, ref result, ref strException, ResultContext, DIDNo, CallingCompany, objConnectCall, IP, true, ref isBlocked, VirtualNo, ref iSWFH, isUAE, groupId, AswatPosition, callFromNumber, CallFromIP, ref isCallViaDialerApp);
                if (isBlocked)
                    obj.Status = "BLOCKED";
                //


                //skip lead for 5 mins
                LeadPrioritizationDLL.SkipLead(obj.LeadID, 5);

                //update calltime in mongo
                LeadPrioritizationDLL.UpdateCallFlagInMongo(obj.LeadID, Convert.ToInt64(_DispositionUpdate.CallTrackingID), objConnectCall.PriorityReasonId);
            }


        }
        catch (Exception ex)
        {
            obj.Status = "Failed";
            obj.Message = "Error Occurried";
            strException = ex.ToString();
        }
        finally
        {
            LoggingHelper.LoggingHelper.AddloginQueue(null, objConnectCall.LeadID, strException, "ConnectCall", "CommAPI", "", dataToPost, result, RequestDatetime, DateTime.Now);
        }

        return obj;
    }

    private static string GetContextQueue(CommunicationModel objConnectCall, ref string context, ref string IP, ref string DIDNo, ref string CallingCompany, ref int groupId, ref string VirtualNo, ref bool iSWFH, ref Int16 AgentCountry, ref string AswatPosition)
    {

        var agentContext = CallCommunicationDLL.GetContexQueue(objConnectCall.Conversations[0].AgentID, ref context, ref IP, ref DIDNo, ref CallingCompany, ref groupId, ref VirtualNo, ref iSWFH, ref AgentCountry, ref AswatPosition);
        return agentContext;
    }
    private static string GetSmeContextForCalling(CommunicationModel objConnectCall, string oldContext )
    {
        string context = oldContext;

        if (CoreCommonMethods.IsValidString(objConnectCall.VirtualNumber) &&
            !string.IsNullOrEmpty(objConnectCall.callingSource) &&
            objConnectCall.callingSource.Equals("matrix", StringComparison.OrdinalIgnoreCase))
        {
            context = "virtualNumberCalling";
        }
        else if (!string.IsNullOrEmpty(objConnectCall.callingSource) &&
                 objConnectCall.callingSource.Equals("fosapp", StringComparison.OrdinalIgnoreCase) &&
                 objConnectCall.SubProductId > 0)
        {
            context = "matrixgo";
        }

        return context;
    }
    public static string GetDialerCode(CommunicationModel onjCommDialer)
    {
        if (!string.IsNullOrEmpty(onjCommDialer.Conversations[0].CountryCode) && onjCommDialer.Conversations[0].CountryCode != "91")
        {
            return "NRIDialerCode".AppSettings() + onjCommDialer.Conversations[0].CountryCode;
        }
        else
            return "DialerCode".AppSettings();
    }

    public static EasyDialSoftPhoneResponse GetSpecialCampigan(CommunicationModel objConnectCall, ref string ResultContext, ref string CallingCompany, string DIDNo, DialerDispDetails _DispositionUpdate, ref bool iSWFH)
    {
        //check appointment
        PriorityModel respPriorityModel = LeadPrioritizationDLL.GetPriorityModelMongo(objConnectCall.LeadID);
        EasyDialSoftPhoneResponse obj = new() { Status = "Success", Message = "Success" };

        if (objConnectCall.PriorityReasonId == 27)
            ResultContext = "carpayment1";

        else if (!string.IsNullOrEmpty(objConnectCall.callingSource) && objConnectCall.callingSource.ToLower() == "fosapp")
        {
            var Companies = new List<string>() { "AIRTEL", "TATAMOBILESIP" };

            if (!Companies.Contains(CallingCompany.ToUpper()))
            {
                Int64 n;
                if (!Int64.TryParse(DIDNo, out n))
                {
                    obj.Status = "Failed";
                    obj.Message = "Agent Mobile not found.";
                    //return obj;
                }
                else if (respPriorityModel != null && respPriorityModel.Appointment != null && respPriorityModel.Appointment.AppointmentId > 0)
                {
                    CallingCompany = "WFH";
                    ResultContext = "fosapp";
                    _DispositionUpdate.Context = "FOSAPP";
                }
                else
                {
                    CallingCompany = "WFH";
                    ResultContext = "applead";
                    _DispositionUpdate.Context = "applead";
                }
            }
            else
            {
                ResultContext = "fosapp";
                _DispositionUpdate.Context = "FOSAPP";
            }
        }
        else if (!string.IsNullOrEmpty(objConnectCall.callingSource) && objConnectCall.callingSource.ToLower() == "appprogressive")
        {
            Int64 n;
            if (!Int64.TryParse(DIDNo, out n))
            {
                obj.Status = "Failed";
                obj.Message = "Agent Mobile not found.";
                //return obj;
            }
            else if (respPriorityModel != null && respPriorityModel.Appointment != null && respPriorityModel.Appointment.AppointmentId > 0)
            {
                CallingCompany = "WFH";
                ResultContext = "fosapp";
                _DispositionUpdate.Context = "appprogressive";
            }
            else
            {
                CallingCompany = "WFH";
                ResultContext = "applead";
                _DispositionUpdate.Context = "appprogressivelead";
            }
        }
        else if (respPriorityModel != null && respPriorityModel.Appointment != null && respPriorityModel.Appointment.AppointmentId > 0)
        {
            ResultContext = "fosmatrix";
            _DispositionUpdate.Context = "fosmatrix";
        }
        else if (iSWFH == true)
        {
            if (!String.IsNullOrEmpty(CallingCompany) && CallingCompany.ToUpper() == "WFH_NEW")
                _DispositionUpdate.Context = "WFH_NEW";
            else
                _DispositionUpdate.Context = "WFH";
        }
        else if (!string.IsNullOrEmpty(ResultContext))
            _DispositionUpdate.Context = ResultContext;
        else
            _DispositionUpdate.Context = "carpdagent";

        return obj;
    }

    private static bool ThirdPartyCalling(ref string dataToPost, ref string result, ref string strException, string ResultContext, string DIDNo, string CallingCompany, CommunicationModel objConnectCall, string IP, Boolean isProgressive, ref Boolean isBlocked, string VirtualNo, ref bool iSWFH, bool isUAE, int groupId, string AswatPosition, long callFromNumber, string CallFromIP, ref bool isCallViaDialerApp)
    {
        bool IsThirdParty = false;
        string url = string.Empty;
        try
        {
            if (isUAE)
                url = "WFHURL_UAE".AppSettings();
            else
                url = "WFHURL".AppSettings();

            DateTime ct = DateTime.Now;
            DateTime st = Convert.ToDateTime("ThirdPartyStartTime".AppSettings());
            DateTime et = Convert.ToDateTime("ThirdPartyEndTime".AppSettings());
            List<string> Companies = new List<string>() { "KNOWLARITY", "VODA", "KNOWLARITY_NEW", "WEBPHONE_NEW", "AIRTEL", "TATAMOBILESIP", "SMARTPING" };
            bool virtualNoCall = false;
            byte _isWP = 1;

            if (!string.IsNullOrEmpty(CallingCompany) &&
                (!string.IsNullOrEmpty(VirtualNo) || !string.IsNullOrEmpty(DIDNo)) &&
                (
                    Companies.Contains(CallingCompany.ToUpper()) ||
                    (objConnectCall.ProductID == 131 && !string.IsNullOrEmpty(objConnectCall.callingSource) && (objConnectCall.callingSource.ToLower() == "fosapp" ))
                )
               )
            {
                virtualNoCall = true;                

                if (!string.IsNullOrEmpty(VirtualNo) && objConnectCall.ProductID == 131 && !string.IsNullOrEmpty(objConnectCall.callingSource) && objConnectCall.callingSource.ToLower() == "fosapp" && objConnectCall.SubProductId > 0)
                {
                    int[] SubProductIds = { 16, 17, 18 };
                    if (
                            (objConnectCall.SubProductId == 13 && "AllowdSmeMarineVirtualNumbers".AppSettings().Contains(VirtualNo)) ||
                            (objConnectCall.SubProductId == 19 && "AllowedSmeWorkmenVirtualNumbers".AppSettings().Contains(VirtualNo)) ||
                            (SubProductIds.Contains(objConnectCall.SubProductId) && "AllowedSmeEnggVirtualNumbers".AppSettings().Contains(VirtualNo))
                       )
                    {
                        CallingCompany = "AIRTEL";
                        _isWP = 0;
                    }
                    else
                    {
                        // Do not allow calling for FOS agent from app if above conditions are not matched for SME only.
                        return false;
                    }
                }
            }

            bool isSmeSV_VNCall = false;
            if (CoreCommonMethods.IsValidString(objConnectCall.VirtualNumber) && objConnectCall.ProductID == 131 &&
                !string.IsNullOrEmpty(objConnectCall.callingSource) && (objConnectCall.callingSource.ToLower() == "matrix"))
            {
                virtualNoCall = true;
                DIDNo = iSWFH ? DIDNo : string.Empty;
                _isWP = 1;
                VirtualNo = objConnectCall.VirtualNumber;
                callFromNumber = Convert.ToInt64(VirtualNo);
                CallingCompany = LeadPrioritizationDLL.GetVNCallingCompany(VirtualNo, out CallFromIP);
                isSmeSV_VNCall = true;
            }

            if (virtualNoCall)
            {
                VirtualNo = (!string.IsNullOrEmpty(DIDNo)) && DIDNo.ToUpper() == "DYNAMIC" ? "DYNAMIC" : VirtualNo;
                IsThirdParty = true;
                byte iswebphone = 0;
                if (iSWFH == false && _isWP == 1)
                    iswebphone = 1;

                if(isSmeSV_VNCall && CallingCompany.ToUpper() != "AIRTEL")
                    dataToPost = url + "phone=" + objConnectCall.Conversations[0].From + objConnectCall.Conversations[0].ToReceipent[0] + "&leadid=" + objConnectCall.LeadID + "&campaign=" + ResultContext + "&emp=" + objConnectCall.Conversations[0].AgentID + "&uid=" + objConnectCall.CallTrackingID + "&agentphone=" + DIDNo + "&server_ip=" + IP + "&callingCompany=" + CallingCompany.ToUpper() + "&cliNumbar=" + VirtualNo + "&iswebphone=" + iswebphone + "&countryID=" + objConnectCall.Conversations[0].CountryCode + "&callFromNumber=" + callFromNumber + "&CallFromIP=" + CallFromIP;
                else
                    dataToPost = url + "phone=" + objConnectCall.Conversations[0].From + objConnectCall.Conversations[0].ToReceipent[0] + "&leadid=" + objConnectCall.LeadID + "&campaign=" + ResultContext + "&emp=" + objConnectCall.Conversations[0].AgentID + "&uid=" + objConnectCall.CallTrackingID + "&agentphone=" + DIDNo + "&server_ip=" + IP + "&callingCompany=" + CallingCompany.ToUpper() + "&cliNumbar=" + VirtualNo + "&iswebphone=" + iswebphone + "&countryID=" + objConnectCall.Conversations[0].CountryCode;

                result = CommonAPICall.CallAPI(dataToPost, "", "GET", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", null);
            }
            else if (!string.IsNullOrEmpty(DIDNo) && !string.IsNullOrEmpty(CallingCompany) && (CallingCompany.ToUpper() == "WFH" || CallingCompany.ToUpper() == "WFH_NEW"))
            {
                IsThirdParty = true;
                dataToPost = url + "phone=" + objConnectCall.Conversations[0].From + objConnectCall.Conversations[0].ToReceipent[0] + "&leadid=" + objConnectCall.LeadID + "&campaign=" + ResultContext + "&emp=" + objConnectCall.Conversations[0].AgentID + "&uid=" + objConnectCall.CallTrackingID + "&agentphone=" + DIDNo + "&server_ip=" + IP + "&callingCompany=" + CallingCompany.ToUpper() + "&countryID=" + objConnectCall.Conversations[0].CountryCode + "&callFromNumber="+ callFromNumber + "&CallFromIP=" + CallFromIP;

                CommonAPICall.getAPICallAsync(dataToPost, Convert.ToInt32("DialerAPITimeout".AppSettings()));
            }
            else if (!string.IsNullOrEmpty(DIDNo) && !string.IsNullOrEmpty(CallingCompany)
                && CallingCompany.ToUpper() == "DIALER_APP"
                && objConnectCall.Conversations[0].CountryCode == "91")
            {
                IsThirdParty = true;
                isCallViaDialerApp = true;
                //CallViaDialerApp(objConnectCall);
            }
            else if (!string.IsNullOrEmpty(DIDNo) && !string.IsNullOrEmpty(CallingCompany) && CallingCompany.ToUpper() == "WEBPHONE")
            {
                IsThirdParty = true;
                dataToPost = url + "phone=" + objConnectCall.Conversations[0].From + objConnectCall.Conversations[0].ToReceipent[0] + "&leadid=" + objConnectCall.LeadID + "&campaign=" + ResultContext + "&emp=" + objConnectCall.Conversations[0].AgentID + "&uid=" + objConnectCall.CallTrackingID + "&server_ip=" + IP + "&countryID=" + objConnectCall.Conversations[0].CountryCode + "&callFromNumber=" + callFromNumber + "&CallFromIP=" + CallFromIP;
                CommonAPICall.getAPICallAsync(dataToPost, Convert.ToInt32("DialerAPITimeout".AppSettings()));
            }          
            else if (!string.IsNullOrEmpty(AswatPosition) && !string.IsNullOrEmpty(CallingCompany)
                    && CallingCompany.ToUpper() == "ASWATINDIA"
                    && !string.IsNullOrEmpty(objConnectCall.Conversations[0].CountryCode))
            {
                IsThirdParty = true;
                string apiKey = "aswat_india_api_key".AppSettings();
                var CustomerMobileNo = "00" + objConnectCall.Conversations[0].CountryCode + objConnectCall.Conversations[0].ToReceipent[0];
                dataToPost = "aswatindiacallingurl".AppSettings() + "integrations/cti/agents/" + AswatPosition + "/call/" + CustomerMobileNo;
                string json = "";
                Dictionary<string, string> headers = new Dictionary<string, string>() { { "api_key", apiKey } };
                result = CommonAPICall.PostAPICall_Aswat(dataToPost, Convert.ToInt32("DialerAPITimeout".AppSettings()), json, headers);
                UpdateAswatCallId(result, Convert.ToInt64(objConnectCall.CallTrackingID), ref strException);
            }
            else
            {
                IsThirdParty = true;
                dataToPost = url + "phone=" + objConnectCall.Conversations[0].From + objConnectCall.Conversations[0].ToReceipent[0] + "&leadid=" + objConnectCall.LeadID + "&campaign=" + ResultContext + "&emp=" + objConnectCall.Conversations[0].AgentID + "&uid=" + objConnectCall.CallTrackingID + "&server_ip=" + IP + "&countryID=" + objConnectCall.Conversations[0].CountryCode + "&callFromNumber=" + callFromNumber + "&CallFromIP=" + CallFromIP;
                CommonAPICall.getAPICallAsync(dataToPost, Convert.ToInt32("DialerAPITimeout".AppSettings()));
            }
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue(null, objConnectCall.LeadID, ex.ToString(), "ThirdPartyCalling", "CommAPI", "ConnectCall", "", "", DateTime.Now, DateTime.Now);
        }

        return IsThirdParty;
    }

    public static void CallViaDialerApp(CommunicationModel objConnectCall)
    {
        try
        {

            string URL = "MatrixApiUrl".AppSettings() + "api/dialerappnotification/TriggerCallNotification";
            Dictionary<string, string> headers = new() {
                { "source", "MatrixSrc".AppSettings() },
                { "clientKey", "MatrixClientKey".AppSettings() },
                { "authKey", "MatrixAuthKey".AppSettings() }
            };

            dynamic dobj = new ExpandoObject();
            dobj.userId = objConnectCall.UserID;
            dobj.title = "MakeCallViaApp";
            dynamic body = new ExpandoObject();
            body.LeadId = objConnectCall.LeadID;
            body.Name = objConnectCall.LeadID.ToString();
            body.MobileNo = $"{objConnectCall.Conversations[0].ToReceipent[0]}";
            body.CallDataId = objConnectCall.CallTrackingID;

            dobj.body = Newtonsoft.Json.JsonConvert.SerializeObject(body);

            var response = CommonAPICall.PostAPICallWithResult(URL, 1000, Newtonsoft.Json.JsonConvert.SerializeObject(dobj), string.Empty, headers);
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue(null, objConnectCall.LeadID, ex.ToString(), "CallViaDialerAppAPI", "CommAPI", "ConnectCall", "", "", DateTime.Now, DateTime.Now);
        }

    }

    public static void UpdateAswatCallId(string result, long CallDataId, ref string strException)
    {
        try
        {
            if(result=="PreconditionFailed")
            {
                return;
            }
            ASWATResponse? obj = Newtonsoft.Json.JsonConvert.DeserializeObject<ASWATResponse>(result);
            if (obj != null && obj.content != null && obj.content.callID != null)
            {
                CallCommunicationDLL.ASWATUpdateCallId(obj.content.callID, CallDataId);
            }
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue(null, CallDataId, ex.ToString(), "UpdateAswatCallId", "CommAPI", "ConnectCall", "", result, DateTime.Now, DateTime.Now);
        }
    }

    private static void PrepareCallResponse(EasyDialSoftPhoneResponse obj, CommunicationModel objConnectCall)
    {
        obj.LeadID = objConnectCall.LeadID;
        obj.campaign = objConnectCall.Conversations[0].Context;
        obj.DialerCode = objConnectCall.Conversations[0].From;
        obj.empId = objConnectCall.Conversations[0].AgentID;
        obj.uid = Convert.ToString(objConnectCall.CallTrackingID);
        obj.CountryCode = objConnectCall.Conversations[0].CountryCode;
        obj.Phone = "##########";
        obj.SF = CryptoHelper.Encrypt_AES256(objConnectCall.Conversations[0].From + objConnectCall.Conversations[0].ToReceipent[0]);
    }
}

