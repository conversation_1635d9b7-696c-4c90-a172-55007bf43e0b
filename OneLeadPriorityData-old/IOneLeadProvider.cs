﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PropertyLayers;
namespace OneLeadPriorityData
{
    public interface IOneLead
    {
        List<PriorityModel> getOneLead(Int16 productID,Int64 UserID,Int16 priorityleadCount, bool productCheck=false);
        List<PriorityModel> getBookedLead(Int16 productID, Int64 UserID, Int16 priorityleadCount);
        List<PriorityModel> getPriorityQueue(List<PriorityModel> lstPriorityModel, List<PriorityModel> lstAgentAssignedLeads, List<PriorityModel> lstRest1stAttemptLeads, Int16 PriorityQueueSize, Int16 ProductID = 0, Int64 UserID=0);
    }
}
