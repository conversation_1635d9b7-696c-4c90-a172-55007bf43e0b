﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PropertyLayers;
using Helper;
using DataAccessLibrary;
namespace OneLeadPriorityData
{
    public class OneLead_CarIBRenewalChat : IOneLead
    {
        productPriorityconstant _LeadPriorityConstants;
        Int16 _productID = 0;
        private short productID;
        OneLeadData _OneLeadData;
        Int16 counter;
        Int16 _GroupID = 0;
        public OneLead_CarIBRenewalChat(short productID, Int16 GroupID)
        {
            // TODO: Complete member initialization
            this._productID = productID;
            this._GroupID = GroupID;
            _LeadPriorityConstants = getPriorityConfigByProduct(productID);
            _OneLeadData = new OneLeadData(productID, _LeadPriorityConstants, counter);
        }
        // Getting Top PriorityLead say 5Leads
        public List<PriorityModel> getOneLead(Int16 ProductId, Int64 UserId, Int16 priorityleadCount, bool productCheck = false)
        {
            List<PriorityModel> lstPriorityModel = new List<PriorityModel>();
            //Int16 PriorityQueueSize = _LeadPriorityConstants.PriorityQueueSize;
            //if (priorityleadCount == 1)
            //    PriorityQueueSize = 1;
            //var priorityQueuelist=_LeadPriorityConstants
            List<PriorityModel> lstAgentAssignedLeads = LeadPrioritizationDLL.GetAgentActiveLeads(UserId, ProductId, true, productCheck,_GroupID);// getting Agent all Active Leads
            
            List<PriorityModel> lstRest1stAttemptLeads = new List<PriorityModel>();

            lstPriorityModel = getPriorityQueue(lstPriorityModel, lstAgentAssignedLeads, lstRest1stAttemptLeads, priorityleadCount);

            return lstPriorityModel;
        }
        // Get all active Leads of an agent   

        public List<PriorityModel> getBookedLead(Int16 ProductId, Int64 UserId, Int16 priorityleadCount)
        {
            return null;
        }

        // get agent priority queue
        public List<PriorityModel> getPriorityQueue(List<PriorityModel> lstPriorityModel, List<PriorityModel> lstAgentAssignedLeads, List<PriorityModel> lstRest1stAttemptLeads, Int16 PriorityQueueSize, Int16 ProductID = 0, Int64 UserID = 0)
        {
            try
            {
                foreach (var prioritySequence in _LeadPriorityConstants.prioritySequence)
                {
                    List<PriorityModel> _filteredleads = null;
                    switch (prioritySequence.ToUpper())
                    {
                        case "ACTIVEREVISIT": // active revisit
                            _filteredleads = _OneLeadData.GetActiveRevisitLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTACTIVEREVISIT": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptActiveRevisit(lstAgentAssignedLeads, counter); // 2nd attempt act revisit 
                            break;
                        case "ACTIVENEW": // active revisit
                            _filteredleads = _OneLeadData.GetActiveNewLead(lstAgentAssignedLeads, counter);  // active new
                            break;
                        case "2NDATTEMPTACTIVENEW": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptActiveNew(lstAgentAssignedLeads, counter);  // 2nd attempt active new
                            break;
                        case "PAYMENTCB": // active revisit
                            _filteredleads = GetPaymentCBLead(lstAgentAssignedLeads, counter);  // payment cb
                            break;
                        case "2NDATTEMPTPCB": // active revisit
                            _filteredleads = Get2ndAttemptPCBLeads(lstAgentAssignedLeads, counter);  // 2nd attempt payment cb
                            break;
                        case "EMAILREVISIT":
                            _filteredleads = _OneLeadData.GetEmailRevisitLeads(lstAgentAssignedLeads, counter);  // Email Revisit
                            break;
                        case "2NDATTEMPTEMAILREVISIT":
                            _filteredleads = _OneLeadData.Get2ndAttemptEmailRevisit(lstAgentAssignedLeads, counter);  // 2nd attempt Email Revisit
                            break;
                        case "PAYMENTFAILURE":
                            _filteredleads = _OneLeadData.GetPaymentFailureLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTPAYMENTFAILURE":
                            _filteredleads = _OneLeadData.Get2ndAttemptPaymentFailureLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "ACTIVECB": // active revisit
                            _filteredleads = GetActiveCBLeads(lstAgentAssignedLeads, counter);  // active call back
                            break;
                        case "2NDATTEMPTACTIVECB": // active revisit
                            _filteredleads = Get2ndAttemptActiveCB(lstAgentAssignedLeads, counter);  // 2nd atmpactive call back
                            break;
                        case "PASSIVENEW": // active revisit
                            _filteredleads = GetPassiveNewLead(lstAgentAssignedLeads, counter); // passive new
                            break;
                        case "2NDATTEMPTPASSIVENEW": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptPassiveNew(lstAgentAssignedLeads, counter); // 2nd atmp passive new
                            break;
                        case "PASSIVEREVISIT": // active revisit
                            _filteredleads = _OneLeadData.GetPassiveRevisitLead(lstAgentAssignedLeads, counter); // passive revisit
                            break;
                        case "2NDATTEMPTPASSIVEREVISIT": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptPassiveRevisit(lstAgentAssignedLeads, counter); // 2nd atmp passive new
                            break;
                        case "PASSIVECB": // active revisit
                            _filteredleads = GetPassiveCBLead(lstAgentAssignedLeads, counter); // passive CALL BACK)
                            break;
                        case "2NDATTEMPTPASSIVECB": // active revisit
                            _filteredleads = Get2ndAttemptPassiveCB(lstAgentAssignedLeads, counter); // 2nd atmp passive CALL BACK)
                            break;
                        case "EXPIRY": // active revisit
                            _filteredleads = GetExpiryDateLeads(lstAgentAssignedLeads, counter);// expiryLeads
                            break;
                        case "CALLRELEASE": // active revisit
                            _filteredleads = CallReleaseLeads(lstAgentAssignedLeads, counter);   // call released
                            break;
                        case "UNANSWERED": // active revisit
                            _filteredleads = _OneLeadData.GetUnansweredLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "UNANSWEREDRECENT": // active revisit
                            _filteredleads = GetUnansweredRecentLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "REVISITCTC":
                            _filteredleads = _OneLeadData.GetCTCRevisitLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTREVISITCTC":
                            _filteredleads = _OneLeadData.Get2ndAttemptCTCRevisit(lstAgentAssignedLeads, counter);
                            break;
                        case "MISSEDCB":
                            _filteredleads = GetMissedCBLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "TODAYEXPIRY":
                            _filteredleads = _OneLeadData.GetTodayExpiryLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTTODAYEXPIRY":
                            _filteredleads = _OneLeadData.Get2ndAttemptTodayExpiryLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "EXPIRY0107DAYS":
                            _filteredleads = _OneLeadData.GetExpiryLeads(lstAgentAssignedLeads, counter, 0, 7, true, 0);
                            break;
                        case "EXPIRY0715DAYS":
                            _filteredleads = _OneLeadData.GetExpiryLeads(lstAgentAssignedLeads, counter, 7, 15, true, 1);
                            break;
                        case "EXPIRY1530DAYS":
                            _filteredleads = _OneLeadData.GetExpiryLeads(lstAgentAssignedLeads, counter, 15, 30, true, 2);
                            break;
                        case "EXPIRY3045DAYS":
                            _filteredleads = _OneLeadData.GetExpiryLeads(lstAgentAssignedLeads, counter, 30, 45, true, 4);
                            break;
                        case "SERVICECB":
                            _filteredleads = _OneLeadData.GetServiceCBLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "REST": // active revisit
                            _filteredleads = GetRest_1Leads(lstAgentAssignedLeads, counter);
                            lstRest1stAttemptLeads.AddRange(_filteredleads.Where(p => !lstPriorityModel.Exists(X => X.LeadID == p.LeadID)));
                            break;
                    }

                    if (_filteredleads != null && _filteredleads.Count > 0)
                        lstPriorityModel.AddRange(_filteredleads.Where(p => !lstPriorityModel.Exists(X => X.LeadID == p.LeadID)));
                    if (lstPriorityModel.Count >= PriorityQueueSize)
                        return lstPriorityModel;

                    counter += 1;
                }

            }
            catch (Exception ex)
            {
                

            }

            finally
            {
                LeadPrioritizationDLL.UpdateRestFlag(lstRest1stAttemptLeads);
            }

            return lstPriorityModel;
        }
                
        // GetAllPassiveLeads
        public List<PriorityModel> GetPassiveLeads(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lstPassiveLeads = new List<PriorityModel>();
            try
            {
                var lstPassiveCBLead = GetPassiveCBLead(lstPriorityModel, counter);
                var lstPassiveRevisitLead = GetPassiveRevisitLead(lstPriorityModel);
                var lstPassiveNewLead = GetPassiveNewLead(lstPriorityModel, counter);
                List<SortingPriorityModel> pasivList = new List<SortingPriorityModel>();// list of all PassiveLeads
                foreach (var pasivCBLd in lstPassiveCBLead)
                {
                    SortingPriorityModel oPassivCBLead = new SortingPriorityModel();
                    oPassivCBLead.PriorityModel = pasivCBLd;
                    oPassivCBLead.SortingDate = pasivCBLd.CallBack.CBtime;
                    pasivList.Add(oPassivCBLead);
                }
                foreach (var pasivRevisitLd in lstPassiveRevisitLead)
                {
                    SortingPriorityModel oPassivRevisitLead = new SortingPriorityModel();
                    oPassivRevisitLead.PriorityModel = pasivRevisitLd;
                    oPassivRevisitLead.SortingDate = pasivRevisitLd.Revisit.ts;
                    pasivList.Add(oPassivRevisitLead);
                }
                foreach (var pasivNewLd in lstPassiveNewLead)
                {
                    SortingPriorityModel oPassivNewLead = new SortingPriorityModel();
                    oPassivNewLead.PriorityModel = pasivNewLd;
                    oPassivNewLead.SortingDate = pasivNewLd.LeadCreatedOn;
                    pasivList.Add(oPassivNewLead);
                }

                lstPassiveLeads = pasivList.OrderByDescending(x => x.SortingDate).Select(x => x.PriorityModel).ToList();// sorting the PassiveList
            }
            catch (Exception ex)
            {

            }
            return lstPassiveLeads;
        }

        //Get ALL PaymentCB Lead in next 15mints
        private List<PriorityModel> GetPaymentCBLead(List<PriorityModel> lstAgentActiveLeads, Int16 counter)
        {
            List<PriorityModel> lstPaymentCBLead = new List<PriorityModel>();
            try
            {
                List<PriorityModel> lstAgentLeads = new List<PriorityModel>(lstAgentActiveLeads);
                Int16 PaymentCBShowTime = _LeadPriorityConstants.PaymentCBShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstAgentActiveLeads != null && lstAgentActiveLeads.Count > 0)
                {
                    lstPaymentCBLead = lstAgentLeads
                                       .Where(x => 
                                           (x.CallBack != null) 
                                           && (x.CallBack.IsPaymentCB == true)// PaymentCallBack
                                           && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes > PaymentCBShowTime)// -5 mints to Till Time
                                           && (x.Call == null || x.Call.TalkTime > 0 || DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// Not visible if NANC in last 30 mints
                                           && (x.Call == null 
                                                || (
                                                        (x.Call.calltime.AddSeconds(TimeDiffDailer) - (x.CallBack.ts > x.CallBack.CBtime.AddMinutes(PaymentCBShowTime) ?
                                                            x.CallBack.ts : x.CallBack.CBtime.AddMinutes(PaymentCBShowTime))).TotalMinutes < 0) // no  call after Max(CBSetTime,CBTime-15)
                                                    )
                                            )
                                    .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.PaymentCB, counter)).OrderByDescending(x => x.CallBack.CBtime)
                                       .ToList();
                    //lstPaymentCBLead.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.PaymentCB);// setting the leadCategory

                }
            }
            catch (Exception ex)
            {

            }
            return lstPaymentCBLead;
        }

        //Get ALL ActiveNew Lead in last 30 mints
        private List<PriorityModel> GetActiveNewLead(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lstActiveNewLead = new List<PriorityModel>();
            try
            {
                List<PriorityModel> lstAgentLeads = new List<PriorityModel>();
                lstAgentLeads.AddRange(lstPriorityModel);
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstActiveNewLead = lstAgentLeads
                                       .Where(x => DateTime.Now.Subtract(x.LeadCreatedOn).TotalMinutes < ActivNewLeadShowTime// visible for 30 mints after leadCreation
                                             && (x.Call == null))// means lead is not talked yet i.e lead is new 
                                       .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.ActiveNew, counter))
                                       .OrderByDescending(x => x.LeadCreatedOn).ToList();
                }
                //lstActiveNewLead.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.ActiveNew);// setting the leadCategory
            }
            catch (Exception ex)
            {

            }
            return lstActiveNewLead;
        }

        //Get ALL active callBackLeads in next and before 15 mints apart from PaymentCB
        private List<PriorityModel> GetActiveCBLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstActiveCBLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                Int16 ActiveCBEndMints = _LeadPriorityConstants.ActiveCBEndTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstActiveCBLeads = lstPriorityModel.Where(x => (x.CallBack != null)
                                                             && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes >= ActiveCBShowTime * -1) //  show before 5 mints till after 15 mints 
                                                             && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes <= ActiveCBEndMints) //  show before 5 mints till after 15 mints 
                                                             && (x.Call == null || x.Call.TalkTime != 0 || DateTime.Now.Subtract(x.Call.calltime.AddMinutes(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// Not visible if NANC in last 30 mints
                                                             && (x.Call == null ||
                                                                    (x.Call.calltime.AddSeconds(TimeDiffDailer) < (x.CallBack.ts > x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1) ?
                                                                    x.CallBack.ts : x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1)))))// no  call after Max(CBSetTime,CBTime-15) 
                  .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.ActiveCB, counter))
                  .OrderByDescending(x => Math.Abs(x.CallBack.CBtime.Subtract(DateTime.Now).Seconds)).ToList();// sorting by nearest callback
                }
            }
            catch (Exception ex)
            {

            }

            return lstActiveCBLeads;
        }

        //yesterday missed callbacks
        private List<PriorityModel> GetMissedCBLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstMissedCBLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstMissedCBLeads = lstPriorityModel.Where(x => 
                            (x.CallBack != null)
                        && (DateTime.Now.Date > x.CallBack.CBtime.Date) //  yesterday callbacks 
                        && (x.Call == null || x.Call.calltime < x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1))) //no call after callback
                  .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.MissedCB, counter))
                  .OrderByDescending(x => Math.Abs(x.CallBack.CBtime.Subtract(DateTime.Now).Seconds)).ToList();// sorting by nearest callback
                }
            }
            catch (Exception ex)
            {

            }

            return lstMissedCBLeads;
        }

        //Get ALL active Revisit in last 30 mintso
        private List<PriorityModel> GetActiveRevisitLeads(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lstActiveRevisitLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstActiveRevisitLeads = lstPriorityModel
                                      .Where(x => (x.Revisit != null) && (DateTime.Now.Subtract(x.Revisit.ts).TotalMinutes < ActiveRevisitShowTime)// revisit in last 30 min
                                          // && (x.Call == null ? true : (x.Call.TalkTime == 0 ? DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC : true))// no NANC In last 30 min
                                            && (x.Call == null || (x.Revisit.ts > x.Call.calltime.AddSeconds(TimeDiffDailer + x.Call.TalkTime))) // No call attempt after revisitTime
                                            && (x.SkippingTime == null || x.SkippingTime < x.Revisit.ts))
                  .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.ActiveRevisit, counter))
                  .OrderByDescending(x => x.Revisit.ts).ToList();
                }
                //lstActiveRevisitLeads.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.ActiveRevisit);// setting the leadCategory
            }
            catch (Exception ex)
            {

            }
            return lstActiveRevisitLeads;
        }

        //Get PassiveNewLead
        public List<PriorityModel> GetPassiveNewLead(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstPassiveNewLead = new List<PriorityModel>();
            try
            {
                Int16 ActiveNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstPassiveNewLead = lstPriorityModel.Where(x => ((DateTime.Now.Subtract(x.LeadCreatedOn).TotalMinutes > ActiveNewLeadShowTime)// after 30 mints
                                                               && (x.Call == null))
                                                               ||
                                                               (
                                                               x.User.Reassigned
                                                               && (DateTime.Now.Subtract(x.User.AssignedOn).TotalMinutes > ActiveNewLeadShowTime)
                                                               && (x.Call == null || (x.Call.calltime < x.User.AssignedOn && x.User.AssignedOn.Date == DateTime.Now.Date))
                                                               )
                                                               )// no call attempts
                  .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.PassiveNew, counter))
                  .OrderBy(x => (x.PrevPolicyExpDate != null && x.PrevPolicyExpDate != DateTime.MinValue) ? x.PrevPolicyExpDate : x.LeadCreatedOn).ToList();
                }
            }
            catch (Exception ex)
            {

            }
            return lstPassiveNewLead;
        }

        // GetPassiveRevistLead
        private List<PriorityModel> GetPassiveRevisitLead(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lstPassiveRevisitLead = new List<PriorityModel>();
            try
            {
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short PassivEnd = _LeadPriorityConstants.PassiveRevisitEndMints;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstPassiveRevisitLead = lstPriorityModel.Where(x => (x.Revisit != null)
                                                               && (DateTime.Now.Subtract(x.Revisit.ts).TotalMinutes > ActiveRevisitShowTime)// revisit Time over by 30 mints
                                                               && (DateTime.Now.Subtract(x.Revisit.ts).TotalMinutes < PassivEnd)  //  before 24 hrs
                                                               && (x.Call == null || (x.Call.calltime.AddSeconds(TimeDiffDailer + x.Call.TalkTime) < x.Revisit.ts))) // no call after revisitTime
                                                                  .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.PassiveRevisit, counter))
                                           .OrderByDescending(x => x.Revisit.ts).ToList();
                }
                //lstPassiveRevisitLead.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.PassiveRevisit);// setting the leadCategory
            }
            catch (Exception ex)
            {

            }
            return lstPassiveRevisitLead;
        }

        // GetPassiveCB
        private List<PriorityModel> GetPassiveCBLead(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstPassiveCBLead = new List<PriorityModel>();
            try
            {
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                Int16 ActiveCBEndTime = _LeadPriorityConstants.ActiveCBEndTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short PassivEnd = _LeadPriorityConstants.PassiveCBEndMints;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstPassiveCBLead = lstPriorityModel.Where(x => (x.CallBack != null)
                                                              && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes > ActiveCBEndTime) //  will be visible after 15mints of CB
                                                              && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes < PassivEnd) //  will be visible between 15mints  to 24hrs of CB
                                                              && (x.Call == null || (x.Call.calltime.AddSeconds(TimeDiffDailer) < (x.CallBack.ts > x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1) ?
                                                                                   x.CallBack.ts : x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1)))
                                                                                   ))// no call Attempt after Max(CBSetTime,CBTime-15) 
                                                    .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.PassiveCB, counter))
                                                    .OrderByDescending(x => x.CallBack.CBtime).ToList();// sorting by nearest callback
                }
                //lstPassiveCBLead.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.PassiveCB);// setting the leadCategory
            }
            catch (Exception ex)
            {

            }
            return lstPassiveCBLead;
        }

        //GetUnansweredLeads
        private List<PriorityModel> GetUnansweredLeads(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lstUnansweredLeads = new List<PriorityModel>();
            try
            {
                DateTime CurrentTime = DateTime.Now;
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(DateTime.Now);
                byte NANCMaxAttemInDiffShifts = _LeadPriorityConstants.NANCMaxAttemInDiffShifts;
                byte NANCLastCallDayGap = _LeadPriorityConstants.NANCLastCallDayGap;
                byte NANCMax1DayAttempt = _LeadPriorityConstants.NANCMax1DayAttempt;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                byte UnAnsweredDaysGap = _LeadPriorityConstants.UnAnsweredDaysGap;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstUnansweredLeads = lstPriorityModel.Where(x => (x.Call != null)
                                                          && (x.Call.TotalTT == 0)
                                                          && (x.PrevPolicyExpDate == null || x.PrevPolicyExpDate == DateTime.MinValue || x.PrevPolicyExpDate.Subtract(CurrentTime).TotalDays <= 45)
                                                          && (x.CallBack == null || DateTime.Now.Subtract(x.CallBack.CBtime).TotalSeconds > 0)// no future CB ..To handle existing CB Lead with tt =0
                                                          && (x.Call.TalkTime == 0 && DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// Not visible if NANC in last 30 mints
                                                          && (CurrentTime.Subtract(x.Call.calltime).TotalDays > UnAnsweredDaysGap ||  //no shift check for lead called more than 2 days earlier 
                                                                                      (  
                                                                                      ((x.Call.NANC_Attempts < NANCMaxAttemInDiffShifts) && ((x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < NANCMax1DayAttempt) && (x.Call.Shifts == null || !x.Call.Shifts.Contains(CurrentCallShift)))
                                                                                       || (x.Call.NANC_Attempts == NANCMaxAttemInDiffShifts && DateTime.Now.Date > x.Call.calltime.Date)// 7th Call on next day                                                                                       
                                                                                       || (x.Call.NANC_Attempts > NANCMaxAttemInDiffShifts && DateTime.Now.Date.Subtract(x.Call.calltime.Date).TotalDays >= NANCLastCallDayGap)// 8th and 9th Call on alternate days
                                                                                       )
                                                                                      )
                                                          )
                                                         .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.UnansweredLeads, counter)).OrderByDescending(x => x.LeadCreatedOn).ToList();//neverAnsweredCall
                }
                //lstUnansweredLeads.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.UnansweredLeads);// setting the leadCategory
            }
            catch (Exception ex)
            {

            }
            return lstUnansweredLeads;
        }

        //Get Payment Call Leadsfor2ndAttempt whose first attempt was NANC
        private List<PriorityModel> Get2ndAttemptPCBLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lst2ndAttemptPCBLeads = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short PCBTimeRange = _LeadPriorityConstants.PaymentCBShowTime;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptPCBLeads = lstPriorityModel.Where(x => (x.CallBack != null) && (x.CallBack.IsPaymentCB == true)   // checking for paymentCB
                                                                   && (x.Call != null) && (x.Call.TalkTime == 0 && x.Call.Duration > 0)// last call NANC
                                                                   //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt
                                                                   && (x.Call.NANC_Attempts % 2 == 1)// odd attempts
                                                                   && (x.IsLastBucketRest == false) // lastly not in RestBucket
                                                                   && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// lst NANC call was before 30 mints
                                                                   && (x.CallBack.ts < x.Call.calltime.AddSeconds(TimeDiffDailer))// NANC Attempt done after the the setting time of PCB
                                                                   && x.Call.calltime > x.CallBack.CBtime.AddMinutes(PCBTimeRange))// last attempt was done after PCB time -15 mints time
                        // .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptPCB)).OrderByDescending(x => x.CallBack.ts).ToList();
                                                                   .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptPCB, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception ex)
            {

            }
            return lst2ndAttemptPCBLeads;
        }

        // Get ActiveRevisit for 2nd Attempt
        private List<PriorityModel> Get2ndAttemptActiveRevisit(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lst2ndAttemptActiveRevisit = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_Revisit;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptActiveRevisit = lstPriorityModel.Where(x => (x.Revisit != null && x.Call != null) && (x.Call.TalkTime == 0)    // last Attempt NANC
                                                                       //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt
                                                                       && (x.Revisit.ts <= x.Call.calltime.AddSeconds(TimeDiffDailer) && x.Call.calltime.AddSeconds(TimeDiffDailer) <= x.Revisit.ts.AddMinutes(ActiveRevisitShowTime)) // last NANC Attempt was within 30 mints of  the revisit time
                                                                       && (x.Call.NANC_Attempts % 2 == 1)// odd attempts
                                                                       && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC))// last NANC call was before 30 mints
                        //.Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptActvRevisit)).OrderByDescending(x => x.Revisit.ts).ToList();
                                                             .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptActvRevisit, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception ex)
            {

            }

            return lst2ndAttemptActiveRevisit;
        }

        // Get ActiveNew for 2nd Attempt
        private List<PriorityModel> Get2ndAttemptActiveNew(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lst2ndAttemptActiveNew = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_New;
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptActiveNew = lstPriorityModel.Where(x => (x.Call != null) && (x.Call.TalkTime == 0)    // last Attempt NANC
                                                                    //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt
                                                                    && (x.Call.calltime.AddSeconds(TimeDiffDailer) >= x.LeadCreatedOn && x.Call.calltime.AddSeconds(TimeDiffDailer) <= x.LeadCreatedOn.AddMinutes(ActivNewLeadShowTime)) // last  NANC attempt was within 30 mints of leadCreation
                                                                    && (x.Call.NANC_Attempts == 1 && x.Call.CallAttempts == 1)
                                                                    && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC))// last NANC call was before 30 mints  
                        //.Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptActvNew)).OrderByDescending(x => x.LeadCreatedOn).ToList();
                                                            .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptActvNew, counter)).OrderBy(x => x.Call.calltime).ToList();
                }

            }
            catch (Exception ex)
            {

            }
            return lst2ndAttemptActiveNew;
        }

        // Get ActiveCB for 2nd Attempt
        private List<PriorityModel> Get2ndAttemptActiveCB(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lst2ndAttemptActiveCB = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                Int16 ActiveCBEndTime = _LeadPriorityConstants.ActiveCBEndTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptActiveCB = lstPriorityModel.Where(x => (x.Call != null && x.CallBack != null) && (x.Call.TalkTime == 0)    // last Attempt NANC
                                                                   //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt   
                                                                    && (x.Call.NANC_Attempts % 2 == 1)// odd attempts
                                                                    && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC call was before 30 mints  
                                                                    && (x.Call.calltime.AddSeconds(TimeDiffDailer).Subtract(x.CallBack.CBtime).TotalMinutes <= ActiveCBEndTime)
                                                                    && (x.Call.calltime.AddSeconds(TimeDiffDailer).Subtract(x.CallBack.CBtime).TotalMinutes >= ActiveCBShowTime * -1)
                                                                    )//last NANC Attempt was before or after 15 mints from CB time
                                                           .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptActvCB, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception ex)
            {

            }
            return lst2ndAttemptActiveCB;
        }

        //Get Passive Revisit for 2nd Attempt
        private List<PriorityModel> Get2ndAttemptPassiveRevisit(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lst2ndAttemptPassiveRevisit = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short PassEnd = _LeadPriorityConstants.PassiveRevisitEndMints;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptPassiveRevisit = lstPriorityModel.Where(x => (x.Call != null && x.Revisit != null) && (x.Call.TalkTime == 0)  // last Attempt NANC
                                                                        //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt
                                                                        && (x.IsLastBucketRest == false) // lastly not in RestBucket
                                                                        && (x.Call.NANC_Attempts % 2 == 1)// odd attempts
                                                                        && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC Was before 30 mints (means moving out of bucket)
                                                                        && (x.Revisit.ts.AddMinutes(ActiveRevisitShowTime) <= x.Call.calltime.AddSeconds(TimeDiffDailer))  // last Attempt was after  30 min of Revisit time
                                                                        && (x.Revisit.ts.AddMinutes(PassEnd) >= x.Call.calltime.AddSeconds(TimeDiffDailer)))  //last attemmpt between 30 min to 24 hrs after revisit time
                        //.Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptPasvRevisit)).OrderByDescending(x => x.Revisit.ts).ToList();
                                                                       .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptPasvRevisit, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception ex)
            {

            }
            return lst2ndAttemptPassiveRevisit;
        }

        //Get Passive New for 2nd Attempt
        private List<PriorityModel> Get2ndAttemptPassiveNew(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lst2ndAttemptPassiveNew = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptPassiveNew = lstPriorityModel.Where(x => (x.Call != null) && (x.Call.TalkTime == 0)   // last Attempt NANC
                                                                     //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt
                                                                     && (x.IsLastBucketRest == false) // lastly not in RestBucket
                                                                     && (x.Call.NANC_Attempts == 1 && x.Call.CallAttempts == 1)
                                                                     && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC Was before 30 mints (means moving out of bucket)
                                                                     && (x.LeadCreatedOn.AddMinutes(ActivNewLeadShowTime) <= x.Call.calltime.AddSeconds(TimeDiffDailer)))// last Attempt was done after  30 min of createdOn time
                                                                    .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptPasvNew, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception ex)
            {

            }
            return lst2ndAttemptPassiveNew;
        }

        //Get Passive CB for 2nd Attempt
        private List<PriorityModel> Get2ndAttemptPassiveCB(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lst2ndAttemptPassiveCB = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                Int16 ActvCBEndTime = _LeadPriorityConstants.ActiveCBEndTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short PassivEnd = _LeadPriorityConstants.PassiveCBEndMints;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptPassiveCB = lstPriorityModel.Where(x => (x.Call != null && x.CallBack != null) && (x.Call.TalkTime == 0)   // last Attempt NANC
                                                                     //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt
                                                                     && (x.IsLastBucketRest == false) // lastly not in RestBucket
                                                                     && (x.Call.NANC_Attempts % 2 == 1)// so that lead do not keep on coming in the same bucket for ever
                                                                     && (x.CallBack.CBtime.AddMinutes(ActvCBEndTime) <= x.Call.calltime.AddSeconds(TimeDiffDailer))  // last Attempt was done after 15 MIN of CB TIME
                                                                     && (x.CallBack.CBtime.AddMinutes(PassivEnd) >= x.Call.calltime.AddSeconds(TimeDiffDailer))  // last Attempt was done till  24 hrs MIN of CB TIME
                                                                     && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC))// last NANC Was before 30 mints (means moving out of bucket)
                  .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptPasvCB, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception ex)
            {

            }
            return lst2ndAttemptPassiveCB;
        }

        //Get 2nd Attempt All Leads except rest Leads
        private List<PriorityModel> Get2ndAttemptLeads(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lst2ndAttemptLeads = new List<PriorityModel>();
            try
            {
                var ScndAtmptPCBLds = Get2ndAttemptPCBLeads(lstPriorityModel, counter);
                var ScndAtmptActiveRevisitLds = Get2ndAttemptActiveRevisit(lstPriorityModel);
                var ScndAtmptActiveNewLds = Get2ndAttemptActiveNew(lstPriorityModel);
                var ScndAtmptActiveCBLds = Get2ndAttemptActiveCB(lstPriorityModel, counter);
                var ScndAtmptPassiveRevisitLds = Get2ndAttemptPassiveRevisit(lstPriorityModel);
                var ScndAtmptPassiveNewLds = Get2ndAttemptPassiveNew(lstPriorityModel);
                var ScndAtmptPassiveCBLds = Get2ndAttemptPassiveCB(lstPriorityModel, counter);

                var ScnAttemptList = new List<SortingPriorityModel>();// list of 2nd Attempt List
                foreach (var lead in ScndAtmptPCBLds) // 2nd attempt PCB Lds
                {
                    SortingPriorityModel o2ndAtmptLead = new SortingPriorityModel();
                    o2ndAtmptLead.PriorityModel = lead;
                    o2ndAtmptLead.SortingDate = lead.CallBack.CBtime;
                    ScnAttemptList.Add(o2ndAtmptLead);
                }
                foreach (var lead in ScndAtmptActiveRevisitLds)// 2nd attempt Active revisit Leads
                {
                    SortingPriorityModel o2ndAtmptLead = new SortingPriorityModel();
                    o2ndAtmptLead.PriorityModel = lead;
                    o2ndAtmptLead.SortingDate = lead.Revisit.ts;
                    ScnAttemptList.Add(o2ndAtmptLead);
                }
                foreach (var lead in ScndAtmptActiveNewLds) // 2nd attempt Active New Leads
                {
                    SortingPriorityModel o2ndAtmptLead = new SortingPriorityModel();
                    o2ndAtmptLead.PriorityModel = lead;
                    o2ndAtmptLead.SortingDate = lead.LeadCreatedOn;
                    ScnAttemptList.Add(o2ndAtmptLead);
                }
                foreach (var lead in ScndAtmptActiveCBLds) // 2nd attempt Actvie CallBack Leads
                {
                    SortingPriorityModel o2ndAtmptLead = new SortingPriorityModel();
                    o2ndAtmptLead.PriorityModel = lead;
                    o2ndAtmptLead.SortingDate = lead.CallBack.CBtime;
                    ScnAttemptList.Add(o2ndAtmptLead);
                }
                foreach (var lead in ScndAtmptPassiveRevisitLds)// 2nd attempt Passive Revisit Leads
                {
                    SortingPriorityModel o2ndAtmptLead = new SortingPriorityModel();
                    o2ndAtmptLead.PriorityModel = lead;
                    o2ndAtmptLead.SortingDate = lead.Revisit.ts;
                    ScnAttemptList.Add(o2ndAtmptLead);
                }
                foreach (var lead in ScndAtmptPassiveNewLds) // 2nd attempt Passive New Leads
                {
                    SortingPriorityModel o2ndAtmptLead = new SortingPriorityModel();
                    o2ndAtmptLead.PriorityModel = lead;
                    o2ndAtmptLead.SortingDate = lead.LeadCreatedOn;
                    ScnAttemptList.Add(o2ndAtmptLead);
                }
                foreach (var lead in ScndAtmptPassiveCBLds) // 2ND attemPT passive Call back Leads
                {
                    SortingPriorityModel o2ndAtmptLead = new SortingPriorityModel();
                    o2ndAtmptLead.PriorityModel = lead;
                    o2ndAtmptLead.SortingDate = lead.CallBack.CBtime;
                    ScnAttemptList.Add(o2ndAtmptLead);
                }

                lst2ndAttemptLeads = (List<PriorityModel>)ScnAttemptList.OrderByDescending(x => x.SortingDate).Select(x => x.PriorityModel).ToList();
            }
            catch (Exception ex)
            {

            }

            return lst2ndAttemptLeads;
        }
        //GetRestLeads
        private List<PriorityModel> GetRest_1Leads(List<PriorityModel> lstPriorityModel, Int16 counter)// eventually means  Leads on which no work is done today
        {
            List<PriorityModel> lstRestLeads = new List<PriorityModel>();
            DateTime ct = DateTime.Now;
            try
            {
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;
                byte RestLeadLastCall = _LeadPriorityConstants.RestLeadLastCall;
                byte NANCMaxAttemInDiffShifts = _LeadPriorityConstants.NANCMaxAttemInDiffShifts;
                byte NANCLastCallDayGap = _LeadPriorityConstants.NANCLastCallDayGap;
                byte NANCMax1DayAttempt = _LeadPriorityConstants.NANCMax1DayAttempt;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(DateTime.Now);
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {

                    lstRestLeads = lstPriorityModel
                                        .Where(x =>
                                                ((x.Call == null || ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap) // NOT CALLED IN lAST 12 HRS ( not called toady)
                                                && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                                && (x.PrevPolicyExpDate != null && x.PrevPolicyExpDate > DateTime.MinValue)
                                                && (
                                                    //After expiry date
                                                    (ct.Date.Subtract(x.PrevPolicyExpDate.Date).TotalDays > 0 && ct.Date.Subtract(x.PrevPolicyExpDate.Date).TotalDays <= 45
                                                     && (x.Call == null || ct.Date.Subtract(x.Call.calltime.Date).TotalDays > 1)
                                                     )
                                                )
                                            )
                                        ).Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.RestLeads_1, counter))
                                        .OrderByDescending(x => x.PrevPolicyExpDate).ToList();

                }

            }
            catch (Exception ex)
            {

            }
            return lstRestLeads;
        }

        private List<PriorityModel> GetRest_2Leads(List<PriorityModel> lstPriorityModel)// this will eventually represent the bucket on whom  work is done today
        {
            List<PriorityModel> lstRestLeads = new List<PriorityModel>();
            try
            {
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;
                byte RestLeadLastCall = _LeadPriorityConstants.RestLeadLastCall;
                byte NANCMaxAttemInDiffShifts = _LeadPriorityConstants.NANCMaxAttemInDiffShifts;
                byte NANCLastCallDayGap = _LeadPriorityConstants.NANCLastCallDayGap;
                byte NANCMax1DayAttempt = _LeadPriorityConstants.NANCMax1DayAttempt;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(DateTime.Now);
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstRestLeads = lstPriorityModel
                                       .Where(x => (x.Call == null ? true : (x.Call.TalkTime == 0 && x.Call.Duration > 0 ? DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC : true))// Not visible if NANC in last 30 mints
                                           && (x.CallBack == null ? true : DateTime.Now.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)    // No future CallBack
                                        && (((x.Call != null) && (x.Call.TotalTT == 0)) ?
                                                                     (((x.Call.NANC_Attempts < NANCMaxAttemInDiffShifts) && ((x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < NANCMax1DayAttempt) && (x.Call.Shifts == null || !x.Call.Shifts.Contains(CurrentCallShift)))
                                                                       || (x.Call.NANC_Attempts == NANCMaxAttemInDiffShifts && DateTime.Now.Date > x.Call.calltime.Date)// 7th Call on next day
                                                                       || (x.Call.NANC_Attempts > NANCMaxAttemInDiffShifts && DateTime.Now.Date.Subtract(x.Call.calltime.Date).TotalDays >= NANCLastCallDayGap)// 8th and 9th Call on alternate days
                                                                                       ) : true
                                                                                      )
                                        )
                                    .ToList();
                }
            }
            catch (Exception ex)
            {

            }
            return PrioritizeRestLeads(lstRestLeads, false).Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.RestLeads_2, counter)).ToList();
        }

        private List<PriorityModel> Get2ndAttemptRestLeads(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> SecndAttemptlRestLeads = new List<PriorityModel>();
            try
            {
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;
                byte RestLeadLastCall = _LeadPriorityConstants.RestLeadLastCall;
                byte NANCMaxAttemInDiffShifts = _LeadPriorityConstants.NANCMaxAttemInDiffShifts;
                byte NANCLastCallDayGap = _LeadPriorityConstants.NANCLastCallDayGap;
                byte NANCMax1DayAttempt = _LeadPriorityConstants.NANCMax1DayAttempt;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(DateTime.Now);
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    SecndAttemptlRestLeads = lstPriorityModel
                                       .Where(x => (x.Call == null ? true : (x.Call.TalkTime == 0 && x.Call.Duration > 0 ? DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC : true))// Not visible if NANC in last 30 mints
                                           //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt
                                           && (x.Call != null && x.Call.NANC_Attempts % 2 == 1)
                                           && (x.CallBack == null ? true : DateTime.Now.Subtract(x.CallBack.CBtime).TotalSeconds >= 0) // No future CallBack
                                        && (((x.Call != null) && (x.Call.TotalTT == 0)) ?
                                                                     (((x.Call.NANC_Attempts < NANCMaxAttemInDiffShifts) && ((x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < NANCMax1DayAttempt) && (x.Call.Shifts == null || !x.Call.Shifts.Contains(CurrentCallShift)))
                                                                       || (x.Call.NANC_Attempts == NANCMaxAttemInDiffShifts && DateTime.Now.Date > x.Call.calltime.Date)// 7th Call on next day
                                                                       || (x.Call.NANC_Attempts > NANCMaxAttemInDiffShifts && DateTime.Now.Date.Subtract(x.Call.calltime.Date).TotalDays >= NANCLastCallDayGap)// 8th and 9th Call on alternate days
                                                                                       ) : true
                                                                                      )
                                        )
                                    .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptRestLeads, counter)).ToList();
                }
                //lstRestLeads.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.RestLeads);// setting the leadCategory
            }
            catch (Exception ex)
            {

            }
            return SecndAttemptlRestLeads;
        }

        public List<PriorityModel> GetExpiryDateLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstTodayExpiry = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                Int16 TodyCreatdExprGap = _LeadPriorityConstants.TodayCreatedExprTimeGap;
                Int16 BefTodyCreatdExprGap = _LeadPriorityConstants.BeforeTodayCreatedExprTimeGap;
                byte ExpiryCount = _LeadPriorityConstants.ExpiryLeadsShowCount;
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(DateTime.Now);
                DateTime ct = DateTime.Now;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstTodayExpiry = lstPriorityModel
                                       .Where(x => (x.PrevPolicyExpDate.Date == ct.Date || x.PrevPolicyExpDate.Date == ct.AddDays(1).Date)// Expiring today or tomorrow
                                          && (x.CallBack == null || x.CallBack.CBtime < ct) // no future call back
                                           && (x.Call != null && ct.Subtract(x.Call.calltime).TotalMinutes > TodyCreatdExprGap) // for today created check                                           
                                           )
                                        .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.RecentExpiry, counter))
                                        .OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception ex)
            {

            }
            return lstTodayExpiry;
        }

        private productPriorityconstant getPriorityConfigByProduct(Int16 productID)
        {
            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            return _LeadPriorityConstants.PriorityConstant.Where(p => (p.productID == productID) && (p.subProduct.ToUpper() == "CHATIBRENEWAL")).SingleOrDefault();
        }

        public List<PriorityModel> CallReleaseLeads(List<PriorityModel> lstAgentActiveLeads, Int16 counter)
        {
            List<PriorityModel> lstReleasedCallleads = new List<PriorityModel>();
            try
            {
                DateTime ct = DateTime.Now;
                List<PriorityModel> lstAgentLeads = new List<PriorityModel>(lstAgentActiveLeads);
                LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
                if (lstAgentActiveLeads != null && lstAgentActiveLeads.Count > 0)
                {
                    lstReleasedCallleads = lstAgentLeads
                                       .Where(x => (x.CallReleaseCount > 0)
                                           // && (x.Call != null && DateTime.Now.Subtract(x.Call.calltime).TotalMinutes < _LeadPriorityConstants.Releaseleadsshowtime)
                                                    && (x.SkippingTime != null || x.SkippingTime < x.CallReleaseTime) //  so that if release after skipping then it should come in priority
                                              ).Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.CallReleasedLeads, counter)).ToList();
                }
            }
            catch (Exception ex)
            {

            }
            return lstReleasedCallleads;
        }

        public List<PriorityModel> PrioritizeRestLeads(List<PriorityModel> lstPriorityModel, bool Rest_1Flag)
        {
            Int16 InterestedNRestPntDedMintGap = _LeadPriorityConstants.InterestedNRestPntDedMintGap;
            byte RestPriorityCallHrGap = _LeadPriorityConstants.RestPriorityCallHrGap;
            List<PriorityModel> lstRestLeads = new List<PriorityModel>();
            List<PriorityModel> CarGoingToExpirylds = new List<PriorityModel>();
            List<PriorityModel> Last72hrRestLeads = new List<PriorityModel>();
            List<PriorityModel> ProspectRestLeads = new List<PriorityModel>();
            List<PriorityModel> InterestedRestLeads = new List<PriorityModel>();
            List<PriorityModel> OtherRestLeads = new List<PriorityModel>();
            short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
            byte CarExpiryDayGap = _LeadPriorityConstants.CarExpiryDayGap;
            try
            {
                if (Rest_1Flag)// not worked on these leads today
                {
                    foreach (PriorityModel lead in lstPriorityModel)
                    {

                        if (lead.ProductID == 117 && // For motor
                            lead.Call != null && DateTime.Now.Subtract(lead.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InterestedNRestPntDedMintGap//not called today
                           && lead.PrevPolicyExpDate.Subtract(DateTime.Now).TotalDays >= 0 && lead.PrevPolicyExpDate.Subtract(DateTime.Now).TotalDays <= CarExpiryDayGap)
                        {
                            CarGoingToExpirylds.Add(lead);
                        }
                        else if (lead.Call != null && DateTime.Now.Subtract(lead.Call.calltime.AddSeconds(TimeDiffDailer)).TotalHours > RestPriorityCallHrGap)              // not called in 24hrs
                        {
                            Last72hrRestLeads.Add(lead);
                            continue;
                        }
                        else if (lead.LeadStatus.StatusID == 11 && lead.PointDeductTime != DateTime.MinValue
                                && (lead.Call != null && DateTime.Now.Subtract(lead.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InterestedNRestPntDedMintGap))//Interested Leads
                        {
                            ProspectRestLeads.Add(lead);
                            continue;
                        }
                        else if (lead.LeadStatus.StatusID == 4 && lead.PointDeductTime != DateTime.MinValue &&
                               DateTime.Now.Subtract(lead.PointDeductTime).TotalMinutes > InterestedNRestPntDedMintGap
                               && (lead.Call != null && DateTime.Now.Subtract(lead.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InterestedNRestPntDedMintGap))//Interested Leads
                        {
                            InterestedRestLeads.Add(lead);
                            continue;
                        }
                        else if (lead.PointDeductTime != DateTime.MinValue && DateTime.Now.Subtract(lead.PointDeductTime).TotalMinutes > InterestedNRestPntDedMintGap)                                                                                        // other rest Leads                  
                        {
                            OtherRestLeads.Add(lead);
                        }
                    }
                }
                else // 2nd Rest Leads representing todays called Rest Leads
                {
                    foreach (PriorityModel lead in lstPriorityModel)
                    {
                        if (lead.ProductID == 117  // For motor
                           && lead.PrevPolicyExpDate.Subtract(DateTime.Now).TotalDays >= 0 && lead.PrevPolicyExpDate.Subtract(DateTime.Now).TotalDays <= CarExpiryDayGap)
                        {
                            CarGoingToExpirylds.Add(lead);
                        }
                        else if (lead.LeadStatus.StatusID == 11)//Interested Leads
                        {
                            ProspectRestLeads.Add(lead);
                            continue;
                        }
                        else if (lead.LeadStatus.StatusID == 4)//Interested Leads
                        {
                            InterestedRestLeads.Add(lead);
                            continue;
                        }
                        else  // other rest Leads                  
                        {
                            OtherRestLeads.Add(lead);
                        }
                    }
                }
                if (CarGoingToExpirylds.Count > 0)
                    lstRestLeads.AddRange(PrioritizeRestLeadInternally(CarGoingToExpirylds));
                if (Last72hrRestLeads.Count > 0)
                    lstRestLeads.AddRange(PrioritizeRestLeadInternally(Last72hrRestLeads));
                if (InterestedRestLeads.Count > 0)
                    lstRestLeads.AddRange(PrioritizeRestLeadInternally(ProspectRestLeads));
                if (InterestedRestLeads.Count > 0)
                    lstRestLeads.AddRange(PrioritizeRestLeadInternally(InterestedRestLeads));
                if (OtherRestLeads.Count > 0)
                    lstRestLeads.AddRange(PrioritizeRestLeadInternally(OtherRestLeads));

                return lstRestLeads;
            }
            catch (Exception ex)
            {
                return lstPriorityModel.OrderByDescending(x => x.LeadCreatedOn).ToList();
            }
        }

        private static List<PriorityModel> PrioritizeRestLeadInternally(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lstLeads = new List<PriorityModel>();
            List<PriorityModel> lstNRILeads = new List<PriorityModel>();
            List<PriorityModel> lstTotalTT1minLeads = new List<PriorityModel>();
            List<PriorityModel> lst3orLessAttemptLeads = new List<PriorityModel>();
            List<PriorityModel> lstInboundNEmailRevert = new List<PriorityModel>();
            List<PriorityModel> lstSelectionLeads = new List<PriorityModel>();
            List<PriorityModel> lstMultipleChildLeads = new List<PriorityModel>();
            List<PriorityModel> lstRemainOtherRestLeads = new List<PriorityModel>();
            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
            try
            {
                foreach (PriorityModel lead in lstPriorityModel)
                {
                    if (lead.IsNRI || lead.IsReferral)
                    {
                        lstNRILeads.Add(lead);
                        continue;
                    }
                    if (lead.Call != null && lead.Call.TotalTT > 60)
                    {
                        lstTotalTT1minLeads.Add(lead);
                        continue;
                    }
                    if (lead.Call != null && lead.Call.CallAttempts < 4)
                    {
                        lst3orLessAttemptLeads.Add(lead);
                        continue;
                    }
                    if (lead.IsInbound || lead.IsEmailRevert)
                    {
                        lstInboundNEmailRevert.Add(lead);
                        continue;
                    }
                    if (lead.IsSelection)
                    {
                        lstSelectionLeads.Add(lead);
                        continue;
                    }
                    if (lead.ActiveLeadSet.Count > 1)
                    {
                        lstMultipleChildLeads.Add(lead);
                        continue;
                    }
                    else
                    {
                        lstRemainOtherRestLeads.Add(lead);
                    }
                }
                lstLeads.AddRange(lstNRILeads.OrderBy(x => x.Call == null ? x.LeadCreatedOn : x.Call.calltime).ToList());
                lstLeads.AddRange(lstTotalTT1minLeads.OrderBy(x => x.Call == null ? x.LeadCreatedOn : x.Call.calltime).ToList());
                lstLeads.AddRange(lst3orLessAttemptLeads.OrderBy(x => x.Call == null ? x.LeadCreatedOn : x.Call.calltime).ToList());
                lstLeads.AddRange(lstInboundNEmailRevert.OrderBy(x => x.Call == null ? x.LeadCreatedOn : x.Call.calltime).ToList());
                lstLeads.AddRange(lstSelectionLeads.OrderBy(x => x.Call == null ? x.LeadCreatedOn : x.Call.calltime).ToList());
                lstLeads.AddRange(lstMultipleChildLeads = lstMultipleChildLeads.OrderBy(x => x.Call == null ? x.LeadCreatedOn : x.Call.calltime).ToList());
                lstLeads.AddRange(lstRemainOtherRestLeads = lstRemainOtherRestLeads.OrderBy(x => x.Call == null ? x.LeadCreatedOn : x.Call.calltime).ToList());
                return lstLeads;
            }
            catch (Exception ex)
            {
                return new List<PriorityModel>();
            }

        }

        //GetUnansweredLeads
        private List<PriorityModel> GetUnansweredRecentLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstUnansweredLeads = new List<PriorityModel>();
            try
            {
                DateTime CurrentTime = DateTime.Now;
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(DateTime.Now);
                byte NANCMaxAttemInDiffShifts = _LeadPriorityConstants.NANCMaxAttemInDiffShifts;
                byte NANCLastCallDayGap = _LeadPriorityConstants.NANCLastCallDayGap;
                byte NANCMax1DayAttempt = _LeadPriorityConstants.NANCMax1DayAttempt;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                byte UnAnsweredDaysGap = _LeadPriorityConstants.UnAnsweredDaysGap;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstUnansweredLeads = lstPriorityModel.Where(x => (x.Call == null || (x.Call.TotalTT == 0 && x.Call.NANC_Attempts <= 4))
                                                          && (x.CallBack == null || DateTime.Now.Subtract(x.CallBack.CBtime).TotalSeconds > 0)// no future CB ..To handle existing CB Lead with tt =0
                                                          && (x.Call == null || (x.Call.TalkTime == 0 && DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC))// Not visible if NANC in last 30 mints
                                                          && (x.Call == null || CurrentTime.Subtract(x.Call.calltime).TotalDays > UnAnsweredDaysGap ||  //no shift check for lead called more than 2 days earlier 
                                                                                      (
                                                                                      ((x.Call.NANC_Attempts < NANCMaxAttemInDiffShifts) && ((x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < NANCMax1DayAttempt) && (x.Call.Shifts == null || !x.Call.Shifts.Contains(CurrentCallShift)))
                                                                                       || (x.Call.NANC_Attempts == NANCMaxAttemInDiffShifts && DateTime.Now.Date > x.Call.calltime.Date)// 7th Call on next day                                                                                       
                                                                                       || (x.Call.NANC_Attempts > NANCMaxAttemInDiffShifts && DateTime.Now.Date.Subtract(x.Call.calltime.Date).TotalDays >= NANCLastCallDayGap)// 8th and 9th Call on alternate days
                                                                                       )
                                                                                      )
                                                          )
                                                         .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.UnansweredRecentLeads, counter)).OrderByDescending(x => x.LeadCreatedOn).ToList();//neverAnsweredCall
                }
                //lstUnansweredLeads.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.UnansweredLeads);// setting the leadCategory
            }
            catch (Exception ex)
            {

            }
            return lstUnansweredLeads;
        }

       
    }
}
