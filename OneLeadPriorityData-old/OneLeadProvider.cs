﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PropertyLayers;
using ReadXmlProject;

namespace OneLeadPriorityData
{
   public class OneLeadProvider
    {
        public static IOneLead getoneLeadProvide(Int16 productID, bool RenwalGrp = false,Int16 GroupID=0,Int16 processId=0, List<PriorityModel> BookedList = null)
        {
            List<Int16> PaymentGroups = "HealthPaymentGroups".AppSettings().Split(',').Select(Int16.Parse).ToList();
            List<Int16> ChatRenewalGroups = "MotorChatGroups".AppSettings().Split(',').Select(Int16.Parse).ToList();
            List<Int16> ChatIBRenewalGroups = "ChatIBRenewalGroups".AppSettings().Split(',').Select(Int16.Parse).ToList();
            List<Int16> HealthFosGroups = "HealthFOSGroups".AppSettings().Split(',').Select(Int16.Parse).ToList();

            if (productID == 117 && ChatRenewalGroups.Contains(GroupID))
                return new OneLead_CarRenewalChat(productID, GroupID);
            else if (productID == 117 && ChatIBRenewalGroups.Contains(GroupID))
                return new OneLead_CarRenewalChat(productID, GroupID);
            else if (productID == 117 && RenwalGrp)
                return new OneLead_CarRenewals(productID, GroupID);
            else if (productID == 117)
                return new OneLead_Car(productID, GroupID);
            else if (productID == 115)
                return new OneLead_INV(productID, GroupID, processId, BookedList);
            else if (productID == 7)
                return new OneLead_Term(productID, GroupID, processId, BookedList);
            else if (productID == 2 && PaymentGroups.Contains(GroupID)) //1610 live,412 QA
                return new OneLead_HealthPayment(productID, GroupID);
            else if (productID == 2 && HealthFosGroups.Contains(GroupID)) 
                return new OneLead_HealthFOS(productID, GroupID, processId);
            else if (productID == 2 && RenwalGrp)
                return new OneLead_HealthRenewals(productID, GroupID);
            else if (productID == 2)
                return new OneLead_Health(productID, GroupID, processId);
            else if (productID == 3)
                return new OneLead_Travel(productID, GroupID);
            else if (productID == 131 && RenwalGrp)
                return new OneLead_SMERenewals(productID, GroupID);
            else if (productID == 101 && RenwalGrp)
                return new OneLead_HomeRenewals(productID, GroupID);
            else if (productID == 131 || productID == 101)
                return new OneLead_SME(productID, GroupID);
            else if (productID == 139 && RenwalGrp)
                return new OneLead_CommercialCarRenewals(productID, GroupID);
            else
                return new OneLead_Car(productID, GroupID);
        }
    }
}
