﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{66D29649-18A6-4D17-84CF-CB426845CE16}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>OneLeadPriorityData</RootNamespace>
    <AssemblyName>OneLeadPriorityData</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="MongoDB.Bson, Version=1.10.1.73, Culture=neutral, PublicKeyToken=f686731cfb9cc103, processorArchitecture=MSIL">
      <HintPath>..\packages\mongocsharpdriver.1.10.1\lib\net35\MongoDB.Bson.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MongoDB.Driver, Version=1.10.1.73, Culture=neutral, PublicKeyToken=f686731cfb9cc103, processorArchitecture=MSIL">
      <HintPath>..\packages\mongocsharpdriver.1.10.1\lib\net35\MongoDB.Driver.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="IOneLeadProvider.cs" />
    <Compile Include="OneLeadData.cs" />
    <Compile Include="OneLeadProvider.cs" />
    <Compile Include="OneLead_CommercialCarRenewals.cs" />
    <Compile Include="OneLead_HealthFOS.cs" />
    <Compile Include="OneLead_HomeRenewals.cs" />
    <Compile Include="OneLead_CarIBRenewalChat.cs" />
    <Compile Include="OneLead_CarRenewalChat.cs" />
    <Compile Include="OneLead_InternationCar.cs" />
    <Compile Include="OneLead_HealthPayment.cs" />
    <Compile Include="OneLead_SMERenewals.cs" />
    <Compile Include="OneLead_SME.cs" />
    <Compile Include="OneLead_CarRenewals.cs" />
    <Compile Include="OneLead_Travel.cs" />
    <Compile Include="OneLead_HealthRenewals.cs" />
    <Compile Include="OneLead_Health.cs" />
    <Compile Include="OneLead_INV.cs" />
    <Compile Include="OneLead_Car.cs" />
    <Compile Include="OneLead_Term.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DataAccessLibrary\DataAccessLibrary.csproj">
      <Project>{4e250218-5a28-4657-89f9-e2ef65843005}</Project>
      <Name>DataAccessLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\DataHelper\DataHelper.csproj">
      <Project>{a5a742d6-9c8f-47f1-a1b3-da3047fd38d6}</Project>
      <Name>DataHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\Helper\Helper.csproj">
      <Project>{c83a7dfb-3470-480f-8b46-8703d018f021}</Project>
      <Name>Helper</Name>
    </ProjectReference>
    <ProjectReference Include="..\LoggingHelper\LoggingHelper.csproj">
      <Project>{ce6ddbf9-516d-4551-b03a-c1d7a6569cad}</Project>
      <Name>LoggingHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\MessageLibrary\MessageLibrary.csproj">
      <Project>{646bef6f-9253-4e2e-ab66-73f0fca427fa}</Project>
      <Name>MessageLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="..\PropertyLayers\PropertyLayers.csproj">
      <Project>{a2368032-96fe-43f1-8976-63b3826b7f74}</Project>
      <Name>PropertyLayers</Name>
    </ProjectReference>
    <ProjectReference Include="..\ReadXmlProject\ReadXmlProject.csproj">
      <Project>{cef2e494-0e05-479a-aa0c-66893fcf7a3e}</Project>
      <Name>ReadXmlProject</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir)\.nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir)\.nuget\NuGet.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>