﻿using System;
using MongoDB.Driver;
using Microsoft.Extensions.Configuration;

namespace DataAccessLibrary
{
    public class LoggerSingletonClass
    {

        static MongoDatabase _LoggingDB;
        private LoggerSingletonClass()
        {

        }

        public static MongoDatabase LoggingDB(string MongoDBConnection, string MongoDataBaseName)
        {
            try
            {
                if (_LoggingDB != null)
                {
                    return _LoggingDB;
                }
                else
                {
                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    var connectionString = MongoDBConnection;
                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    _LoggingDB= server.GetDatabase(MongoDataBaseName);
                    return _LoggingDB;

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


    }
}