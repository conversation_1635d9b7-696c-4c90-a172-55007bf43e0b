﻿using System;
using MongoDB.Bson;
using DataHelper;
using System.Configuration;
using System.IO;
using System.Web;
using Newtonsoft.Json;
using ReadXmlProject;
using System.Runtime.Serialization;
using MongoDB.Bson.Serialization.Attributes;
using Experimental.System.Messaging;
using Microsoft.Extensions.Configuration;
using DataAccessLibrary;
using System.Diagnostics;
using Helper;
using PropertyLayers;

namespace LoggingHelper
{
    public static class LoggingHelper
    {
        static MongoHelper objDBentity;

        static LoggingHelper()
        {
        
        }        
        public static void AddloginQueue(string strUniqueID, long LeadID, string strexCeption, string strMethodName, string ApplicationName, string CreatedBy, string strRequestText, string strResponseText, DateTime RequestDateTime, DateTime ResponseDateTime)
        {
            LoggingModel loggingModel = getLoggingModel(strUniqueID, LeadID, strexCeption, strMethodName, ApplicationName, CreatedBy, strRequestText, strResponseText, RequestDateTime, ResponseDateTime);

            //-------------If Error Occur ----------------//
            if (CoreCommonMethods.IsValidString(loggingModel.Exception))
            {
                Console.WriteLine("Error => " + JsonConvert.SerializeObject(loggingModel));
            }

            bool IsSQSLogingQueueEnabled = "IsSQSLogingQueueEnabled".AppSettings().ToLower().Equals("true");            
            try
            {

                if ("queueloggingon".AppSettings() == "1")
                {
                    if (IsSQSLogingQueueEnabled)
                    {
                        try
                        {
                            /*
                            AmazonSqs amazonSqs = new AmazonSqs();
                            IConfiguration con = Custom.ConfigurationManager.AppSetting;

                            string SQSLogingQueueUrl = con.GetSection("Communication").GetSection("SQSLogingQueueUrl").Value.ToString();
                            string SQSQueueUrl = SQSLogingQueueUrl;
                            amazonSqs.SQSSendMessage(SQSQueueUrl, loggingModel);*/
                            LoggingRequest _LoggingRequest = new()
                            {
                                ConnectionName = "MATRIX_MONGODB_LOGGER",
                                DbName = "logger",
                                CollectionName = "Log_Collection",
                                RequestPayload = Newtonsoft.Json.JsonConvert.SerializeObject(loggingModel)
                            };
                            _ = KafkaWrapper.PushToKafka(_LoggingRequest);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine("AddloginQueueInsideError => " + ex.ToString());
                        }

                    }
                    else
                    {
                        Console.WriteLine("QueueOff => " + JsonConvert.SerializeObject(loggingModel));
                    }

                }
                else
                {
                    Console.WriteLine("QueueOff => " + JsonConvert.SerializeObject(loggingModel));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("AddloginQueueError- " +ex.ToString());
                //Log(strUniqueID, LeadID, ex.ToString(), strMethodName, ApplicationName, CreatedBy, strRequestText, strResponseText, RequestDateTime, ResponseDateTime);
            }            
        }

        public static void AddloginQueue(LoggingModel _LoggingModel)
        {            
            string QueueName = "loggingqueue".AppSettings();
            bool IsSQSLogingQueueEnabled = "IsSQSLogingQueueEnabled".AppSettings().ToLower().Equals("true");            
            try
            {
                if ("queueloggingon".AppSettings() == "1")
                {
                    if (IsSQSLogingQueueEnabled)
                    {
                        AmazonSqs amazonSqs = new AmazonSqs();
                        IConfiguration con = Custom.ConfigurationManager.AppSetting;
                        string SQSLogingQueueUrl = con.GetSection("Communication").GetSection("SQSLogingQueueUrl").Value.ToString();
                        string SQSQueueUrl = SQSLogingQueueUrl;
                        //TODO List
                        //string SQSQueueUrl = ConfigurationManager.AppSettings["SQSLogingQueueUrl"];
                        amazonSqs.SQSSendMessage(SQSQueueUrl, _LoggingModel);
                    }
                    else
                    {
                        Console.WriteLine("QueueOff => " + JsonConvert.SerializeObject(_LoggingModel));
                    }

                }
                else
                {
                    Console.WriteLine("QueueOff => " + JsonConvert.SerializeObject(_LoggingModel));
                }
            }
            catch (Exception Ex)
            {
                Console.WriteLine("Error => " + Ex.ToString());
            }        
        }        
        
        private static LoggingModel getLoggingModel(string strUniqueID, long LeadID, string strexCeption, string strMethodName, string ApplicationName, string CreatedBy, string strRequestText, string strResponseText, DateTime RequestDateTime, DateTime ResponseDateTime)
        {            
            LoggingModel objLoggingModel = new LoggingModel();
            if (!string.IsNullOrEmpty(strUniqueID))
                objLoggingModel.TrackingID = strUniqueID;
            else 
                objLoggingModel.TrackingID = LeadID.ToString();    
            objLoggingModel.Exception = strexCeption;
            objLoggingModel.Method = strMethodName;
            objLoggingModel.Application = ApplicationName;
            objLoggingModel.RequestText = strRequestText;
            objLoggingModel.ResponseText = strResponseText;
            objLoggingModel.Requesttime = RequestDateTime;
            objLoggingModel.Responsetime = ResponseDateTime;
            objLoggingModel.CreatedOn = DateTime.Now;
            objLoggingModel.CreatedBy = CreatedBy;
            
            //TODO List Priority
            //if (HttpContext.Current != null)
              //  objLoggingModel.IP = HttpContext.Current.Request.UserHostAddress;
            return objLoggingModel;
        }
    }

    [DataContract]
    public class LoggingModel
    {
        [DataMember(EmitDefaultValue = false)]
        public ObjectId Id
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string IP
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Channel
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Application
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Method
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string TrackingID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string RequestText
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string ResponseText
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime Requesttime
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime Responsetime
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Exception
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime CreatedOn
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string CreatedBy
        { get; set; }
    }

    public enum LogType
    { 
        No=0,
        DB=1,
        File=2,
        Queue=3
    }
}
