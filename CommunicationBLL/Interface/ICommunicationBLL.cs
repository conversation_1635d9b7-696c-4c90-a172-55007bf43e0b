﻿using System;
using Microsoft.AspNetCore.Mvc;
using PropertyLayers;

namespace CommunicationBLL
{
	public interface ICommunicationBLL
	{
		public EasyDialSoftPhoneResponse PrepareCallData(CallRequestObj callRequest, bool isUAE = false);

		public bool UpdateCallStatus(CallStatus callStatus);
        public List<CallingDataFields> getCallingNoData(string CustomerId, string ProductId, string LeadId);
		public string getMaskedCustContactInfo(string CustomerId, string Type, string LeadId);
		public string AswatIndiaLogin(string AgentID);
		public string AswatIndiaLogout(string AgentID);
		string AswatIndiaStatus(string AgentID, string status);
    }
}

