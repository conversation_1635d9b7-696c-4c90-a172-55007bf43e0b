﻿using PropertyLayers;

namespace CommunicationBLL
{
    public interface IDialerBLL
    {
        DialerLeadDetailModel GetLeadBasisInfo(long leadId, out bool status);
        public DialerAsteriskData updateAgentLoginStatus(PredictiveAgentStatus PredictiveAgentStatus);
        public bool UpdateOBCallDispositionDetails(DialerDispDetails _DispositionUpdate);
        public ResponseData<bool> UpdateCustomerConnect(Int64 uid, Int64 CallingNo, Int64 LeadID, string EmpId);
        public int QueueAgentsLogoutBulk(List<string> Queues);
    }
}

