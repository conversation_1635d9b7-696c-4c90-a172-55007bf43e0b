<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Interface\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Interface\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DialerDataUpdate\DialerDataUpdate.csproj" />
    <ProjectReference Include="..\PropertyLayers\PropertyLayers.csproj" />
    <ProjectReference Include="..\ReadXmlProject\ReadXmlProject.csproj" />
    <ProjectReference Include="..\DataAccessLayer\DataAccessLayer.csproj" />
    <ProjectReference Include="..\OneLeadPriorityData\OneLeadPriorityData.csproj" />
    <ProjectReference Include="..\OBConnectCall\OBConnectCall.csproj" />
    <ProjectReference Include="..\Talktime\Talktime.csproj" />
  </ItemGroup>
</Project>
