﻿using DataAccessLayer;
using DataAccessLibrary;
using DataHelper;
using Google.Apis.Sheets.v4.Data;
using Helper;
using MongoDB.Driver.Builders;
using MongoDB.Driver;
using Newtonsoft.Json;
using OneLeadPriorityData;
using PropertyLayers;
using ReadXmlProject;
using System.Data;
using System.Security.Policy;
using DnsClient;
using Microsoft.Extensions.Logging;
using System.Diagnostics.Metrics;
using Amazon.Auth.AccessControlPolicy;
using System.Drawing.Drawing2D;
using System.Text;
using DialerDataUpdate;

namespace CommunicationBLL;

public class DialerBLL : IDialerBLL
{
    // Global object
    public LeadPriorityConstants oLeadPriorityConstants;

    public DialerBLL()
    {
        oLeadPriorityConstants = DataAccessLibrary.PriorityConfig.getConfig();
    }


    public DialerAsteriskData updateAgentLoginStatus(PredictiveAgentStatus PredictiveAgentStatus)
    {
        string error = string.Empty;
        string method = "updateAgentLoginStatus";
        StringBuilder sb = new();
        sb.Append("PredictiveAgentStatus " + JsonConvert.SerializeObject(PredictiveAgentStatus) + "\r\n");

        DialerAsteriskData? oDialerAsteriskData = new() { status = false };
        string[] _LogOutReasons = LeadPrioritizationDLL.getLogoutReasons("LogoutReasons");
        string Ip = string.Empty;
        string Asteriskurl = string.Empty;
        string queues = String.Empty;
        bool queueLogin = false;
        try
        {
            PredictiveAgentStatus? _PredictiveAgentStatus = PredictiveAgentStatus;
            List<string> companies = new List<string>() { "CLAIM", "BMS", "BMSPSOP" };
            string? ProcessType = "CLAIM";


            if (string.IsNullOrEmpty(PredictiveAgentStatus.Process) || PredictiveAgentStatus.Process.ToUpper() == "PB" || PredictiveAgentStatus.Process.ToUpper() == "FOSAPP")
            {
                DataSet obj = LeadPrioritizationDLL.GetAgentDetails(PredictiveAgentStatus.AgentCode, PredictiveAgentStatus.Source);

                if (PredictiveAgentStatus != null && obj != null && obj.Tables.Count > 0 && obj.Tables[0].Rows.Count > 0)
                {
                    ProcessType = Convert.ToString(obj.Tables[0].Rows[0]["ProcessType"]);
                    _PredictiveAgentStatus = (from item in obj.Tables[0].AsEnumerable()
                                              select new PredictiveAgentStatus
                                              {
                                                  TL = new List<string>() { Convert.ToString(item["ManagerCode"]), Convert.ToString(item["ManagerCode2"]) },
                                                  UserGroup = new List<string>() { Convert.ToString(item["UserGroupName"]) },
                                                  AgentId = Convert.ToString(item["UserID"]),
                                                  AgentCode = PredictiveAgentStatus.AgentCode,
                                                  status = PredictiveAgentStatus.status != null ? PredictiveAgentStatus.status.ToUpper() : PredictiveAgentStatus.status,
                                                  RoleId = Convert.ToString(item["RoleId"] == DBNull.Value ? "" : item["RoleId"]),
                                                  CallType = PredictiveAgentStatus?.CallType,
                                                  Document = PredictiveAgentStatus?.Document,
                                                  IsProgressive = PredictiveAgentStatus.IsProgressive,
                                                  opensv = PredictiveAgentStatus.opensv,
                                                  Grade = Convert.ToString(item["Grade"] == DBNull.Value ? "" : item["Grade"]),
                                                  TLName = Convert.ToString(item["ManagerName"]),
                                                  UserName = Convert.ToString(item["UserName"]),
                                                  Context = Convert.ToString(item["Contex"]),
                                                  AgentIP = Convert.ToString(item["Asterisk_IP"]),///PredictiveAgentStatus.AgentIP,
                                                  Asterisk_Url = Convert.ToString(item["Asterisk_Url"]),
                                                  DIDNo = Convert.ToString(item["DIDNo"]),
                                                  CallingCompany = Convert.ToString(item["CallingCompany"]),
                                                  IsWFH = Convert.ToBoolean(item["IsWFH"]),
                                                  ProductId = Convert.ToString(item["ProductId"]),
                                                  remainingpausetime = string.IsNullOrEmpty(Convert.ToString(item["PauseTimer"])) ? -1 : Convert.ToInt32(item["PauseTimer"]),
                                                  AsteriskToken = PredictiveAgentStatus.AsteriskToken,
                                                  TotalCalls = PredictiveAgentStatus.TotalCalls,
                                                  TotalTalkTime = PredictiveAgentStatus.TotalTalkTime,
                                                  TotalConnectedCalls = PredictiveAgentStatus.TotalConnectedCalls,
                                                  TotalUniqueCalls = PredictiveAgentStatus.TotalUniqueCalls,
                                                  IsMultiQueue = item["IsMultiQueue"] != null && item["IsMultiQueue"] != DBNull.Value && Convert.ToBoolean(item["IsMultiQueue"]),
                                                  MultiQueue = item["MultiQueue"] != null && item["MultiQueue"] != DBNull.Value ? Convert.ToString(item["MultiQueue"]).Split(',').ToList() : null,
                                                  ProcessId = item["ProcessID"] != null && item["ProcessID"] != DBNull.Value ? Convert.ToInt16(item["ProcessID"]) : Convert.ToInt16(0),
                                                  GroupId = item["GroupId"] != null && item["GroupId"] != DBNull.Value ? Convert.ToInt16(item["GroupId"]) : Convert.ToInt16(0)
                                              }).SingleOrDefault();
                }

            }


            if (_PredictiveAgentStatus != null && !string.IsNullOrEmpty(_PredictiveAgentStatus.AgentId) && Convert.ToInt64(_PredictiveAgentStatus.AgentId) > 0 && _PredictiveAgentStatus.AgentCode[0] != '0')
            {
                if (PredictiveAgentStatus != null && !string.IsNullOrEmpty(PredictiveAgentStatus.Process))
                {
                    ProcessType = PredictiveAgentStatus.Process.ToUpper();
                }
                _PredictiveAgentStatus.IsCustAnswered = false;
                _PredictiveAgentStatus._updatedAt = DateTime.Now;
                _PredictiveAgentStatus.LastUpdatedOn = DateTime.Now;
                _PredictiveAgentStatus.updatedBy = "updateAgentLoginStatus";

                if (PredictiveAgentStatus != null && !string.IsNullOrEmpty(PredictiveAgentStatus.Process) && PredictiveAgentStatus.Process.ToUpper() == "CLAIM")
                    _PredictiveAgentStatus.AgentId += "000";
                else if (PredictiveAgentStatus != null && !string.IsNullOrEmpty(PredictiveAgentStatus.Process) && (PredictiveAgentStatus.Process.ToUpper() == "BMSPSOP" || PredictiveAgentStatus.Process.ToUpper() == "PBPARTNERS"))
                    _PredictiveAgentStatus.AgentId += "99999";
                else if (PredictiveAgentStatus != null && !string.IsNullOrEmpty(PredictiveAgentStatus.Process) && PredictiveAgentStatus.Process.ToUpper() == "PBHEALTH")
                    _PredictiveAgentStatus.AgentId += "88888";
                    
                if (_PredictiveAgentStatus.IsWFH == true && !string.IsNullOrEmpty(_PredictiveAgentStatus.CallingCompany) && _PredictiveAgentStatus.CallingCompany.ToUpper() == "WFH_NEW" && _PredictiveAgentStatus.status == "IDLE")
                    _PredictiveAgentStatus.status = "UNAVAILABLE";

                if (_PredictiveAgentStatus != null && !string.IsNullOrEmpty(_PredictiveAgentStatus.status) && _LogOutReasons.Contains(_PredictiveAgentStatus.status.ToUpper()))
                {
                    if (_PredictiveAgentStatus.status.ToUpper() != "PGVPAUSE")
                    {
                        _PredictiveAgentStatus.AsteriskToken = "*********";
                    }
                }


                if (_PredictiveAgentStatus != null && !string.IsNullOrEmpty(_PredictiveAgentStatus.Context) && (_PredictiveAgentStatus.RoleId == "13" || _PredictiveAgentStatus.RoleId == "16" || (_PredictiveAgentStatus.RoleId == "12" && _PredictiveAgentStatus.GroupId == 3472)) )
                {
                    queues = _PredictiveAgentStatus.Context;
                    if (!string.IsNullOrEmpty(_PredictiveAgentStatus.OfflineQueue))
                        queues = queues + "," + _PredictiveAgentStatus.OfflineQueue;

                    queueLogin = true;
                }

                string url = string.Empty;
                string agentip = string.Empty;
                string agentPhone = string.Empty;

                if (!string.IsNullOrEmpty(PredictiveAgentStatus?.Source) && PredictiveAgentStatus.Source.ToLower() == "matrix")
                {
                    agentip = _PredictiveAgentStatus?.AgentIP;
                }
                if (!string.IsNullOrEmpty(_PredictiveAgentStatus?.DIDNo) && ((!string.IsNullOrEmpty(_PredictiveAgentStatus?.CallingCompany) && (_PredictiveAgentStatus?.CallingCompany.ToUpper() == "WFH_NEW" || _PredictiveAgentStatus?.CallingCompany.ToUpper() == "DIALER_APP")) || (!string.IsNullOrEmpty(PredictiveAgentStatus.Source) && PredictiveAgentStatus.Source.ToLower() == "fosapp")))
                {
                    agentPhone = _PredictiveAgentStatus?.DIDNo;
                }

                if (_PredictiveAgentStatus != null && !string.IsNullOrEmpty(_PredictiveAgentStatus.status) && (_PredictiveAgentStatus.status.ToUpper() == "IDLE" || _PredictiveAgentStatus.status.ToUpper() == "UNAVAILABLE"))
                {
                    url = "DialerAPI".AppSettings() + "api/v2/dialer/dialerEvents/login?agentId=" + _PredictiveAgentStatus.AgentCode + "&queues=" + queues + "&iswfh=" + _PredictiveAgentStatus.IsWFH + "&status=" + _PredictiveAgentStatus.status + "&callingcompany=" + _PredictiveAgentStatus.CallingCompany + "&agentphone=" + agentPhone + "&source=" + ProcessType.ToUpper() + "&queueLogin=" + queueLogin + "&ProductId=" + _PredictiveAgentStatus.ProductId + "&agentip=" + agentip;
                }
                else if (_PredictiveAgentStatus != null && !string.IsNullOrEmpty(_PredictiveAgentStatus.status) && _LogOutReasons.Contains(_PredictiveAgentStatus.status.ToUpper()))
                    url = "DialerAPI".AppSettings() + "api/v2/dialer/dialerEvents/logout?agentId=" + _PredictiveAgentStatus.AgentCode + "&queues=" + queues + "&iswfh=" + _PredictiveAgentStatus.IsWFH + "&status=" + _PredictiveAgentStatus.status + "&callingcompany=" + _PredictiveAgentStatus.CallingCompany + "&agentphone=" + agentPhone + "&source=" + ProcessType.ToUpper() + "&queueLogin=" + queueLogin + "&ProductId=" + _PredictiveAgentStatus.ProductId + "&agentip=" + agentip;



                if (_PredictiveAgentStatus != null
                   && (!string.IsNullOrEmpty(_PredictiveAgentStatus.status) && (_PredictiveAgentStatus.status.ToUpper() == "PGVPAUSE"))
                   || (!string.IsNullOrEmpty(PredictiveAgentStatus.Process)
                   && (PredictiveAgentStatus.Process.ToUpper() == "FOSAPP" || PredictiveAgentStatus.Process.ToUpper() == "DIALER_APP"))
                )
                {
                    url = "";
                }

                if (CoreCommonMethods.IsValidString(url))
                {
                    string data = string.Empty;
                    try
                    {
                        sb.Append(" ,url" + url);
                        data = CommonAPICall.CallAPI(url, null, "GET", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", null);
                        sb.Append(" ,data" + data);
                    }
                    catch (Exception ex)
                    {
                        sb.Append(" - Dialer API error : " + ex.Message + " - ");
                    }
                    if (CoreCommonMethods.IsValidString(data))
                    {
                        DialerAsteriskRoot? res = JsonConvert.DeserializeObject<DialerAsteriskRoot>(data);
                        if (res != null)
                        {
                            if (PredictiveAgentStatus != null && PredictiveAgentStatus.Source != null
                                && (PredictiveAgentStatus.Source.ToUpper() == "BMS" ||
                                (PredictiveAgentStatus.Source.ToUpper() == "MATRIX" && _PredictiveAgentStatus!=null && _PredictiveAgentStatus.RoleId == "13"))
                            )
                            {
                                if(PredictiveAgentStatus.Source.ToUpper() == "MATRIX" && _PredictiveAgentStatus != null && !string.IsNullOrEmpty(res.data.asteriskIp) && _PredictiveAgentStatus.AgentIP != res.data.asteriskIp && _PredictiveAgentStatus.GroupId>0) {
                                    DialerDLL.UpdateAsteriskIpForGroup(_PredictiveAgentStatus.GroupId, res.data.asteriskIp, res.data.asteriskUrl);
                                }

                                _PredictiveAgentStatus.AgentIP = Convert.ToString(res.data.asteriskIp);
                                _PredictiveAgentStatus.Asterisk_Url = Convert.ToString(res.data.asteriskUrl);
                            }
                            oDialerAsteriskData = new DialerAsteriskData()
                            {
                                asteriskIp = Convert.ToString(res.data.asteriskIp),
                                asteriskUrl = Convert.ToString(res.data.asteriskUrl),
                                status = true
                            };
                        }
                    }

                }
                
                oDialerAsteriskData.status = true;
                PredictiveAgentStatusRedis.updateAgentLoginRedis(_PredictiveAgentStatus);
                return oDialerAsteriskData;
            }
            else
            {
                oDialerAsteriskData.msg = "Invalid AgentID";
            }
        }
        catch (Exception ex)
        {
            error = ex.ToString();
            oDialerAsteriskData = new DialerAsteriskData()
            {
                msg = "failed"
            };
        }
        finally
        {
            LoggingHelper.LoggingHelper.AddloginQueue(PredictiveAgentStatus.AgentCode, 0, error, method, "oneLead", "CommAPI", sb.ToString(), JsonConvert.SerializeObject(oDialerAsteriskData), DateTime.Now, DateTime.Now);
        }

        return oDialerAsteriskData;
    }

    public Boolean UpdateOBCallDispositionDetails(DialerDispDetails _DispositionUpdate)
    {
        LeadPrioritizationBLL objlpBLL = new LeadPrioritizationBLL();
        _DispositionUpdate.TryCount = Convert.ToInt16(_DispositionUpdate.TryCount + 1);

        if (!string.IsNullOrEmpty(_DispositionUpdate.CallingTime))
        {
            _DispositionUpdate.callDate = Convert.ToDateTime(_DispositionUpdate.CallingTime);
        }

        if (_DispositionUpdate.Action == "agentanswered")
        {
            objlpBLL.InsertUpdateDialerData(_DispositionUpdate);
        }
        else
        {
            objlpBLL.InsertUpdateDialerData(_DispositionUpdate);
            objlpBLL.InsertCallDatafromDialerQueue(_DispositionUpdate);
        }
        return true;
    }

    public DialerLeadDetailModel GetLeadBasisInfo(Int64 LeadId, out bool Status)
    {
        DialerLeadDetailModel dialerLeadDetailModel = null;
        DateTime reqTime = DateTime.Now;
        Status = false;
        try
        {
            DataSet oDataSet = DialerDLL.GetLeadBasisInfo(LeadId);
            if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
            {
                dialerLeadDetailModel = new DialerLeadDetailModel()
                {
                    ProductId = oDataSet.Tables[0].Rows[0]["ProductID"] != DBNull.Value ? Convert.ToInt32(oDataSet.Tables[0].Rows[0]["ProductID"]) : 0,
                };
                if (dialerLeadDetailModel.ProductId > 0)
                    Status = true;

            }
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.ToString(), "GetLeadBasisInfo", "Onelead", "DialerBLL", string.Empty, JsonConvert.SerializeObject(dialerLeadDetailModel), reqTime, DateTime.Now);
        }
        return dialerLeadDetailModel;
    }

    public ResponseData<bool> UpdateCustomerConnect(Int64 uid, Int64 CallingNo, Int64 LeadID, string EmpId)
    {
        ResponseData<bool> oResponseAPI = new ResponseData<bool>() { Status = false, Message = "Something went wrong", Data = false };
        DateTime Requesttime = DateTime.Now;
        string strException = string.Empty;
        try
        {
            DialerDLL.UpdateCustomerConnect(uid, CallingNo);
            DialerDLL.UpdateTotalTTFlagInMongo(LeadID);
            if (!string.IsNullOrEmpty(EmpId))
            {
                Int64 UserId = InsertUpdateCallData.GetUserId(EmpId);

                UserNext5Leads oUserNext5Leads = LeadPrioritizationDLL.GetUserNxt5LeadsFromMongo(UserId);

                if (LeadID > 0 && oUserNext5Leads != null && oUserNext5Leads.Leads != null && oUserNext5Leads.Leads.Count > 0)
                {

                    Next5WidgetLead _Next5WidgetLead = oUserNext5Leads.Leads.Where(x => x.LeadId == LeadID).FirstOrDefault();
                    if(_Next5WidgetLead!=null)
                    {
                        _Next5WidgetLead.CustConnectTime = Requesttime;
                        LeadPrioritizationDLL.UpdateNext5Leads(UserId, oUserNext5Leads);
                    }
                   
                }
            }
            oResponseAPI.Status = true;
            oResponseAPI.Message = "Success";
            oResponseAPI.Data = true;

        }
        catch (Exception ex)
        {
            strException = ex.ToString();
            
        }
        finally
        {
            LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(uid), uid, strException, "UpdateCustomerConnect", "Onelead", "DialerBLL", string.Empty, string.Empty, Requesttime, DateTime.Now);
        }
        return oResponseAPI;
    }

    public int QueueAgentsLogoutBulk(List<string> Queues)
    {

        int count = 0;
        foreach (var queue in Queues)
        {
            try
            {
                // get agentId from Group
                DataSet ds = DialerDLL.GetAgentsByQueue(queue);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        string EmpId = "";
                        try
                        {
                            EmpId = row["EmployeeId"] != DBNull.Value ? (string)row["EmployeeId"] : string.Empty;
                            long UserId = row["UserId"] != DBNull.Value ? Convert.ToInt64(row["UserId"]) : 0;

                            PredictiveAgentStatus predictiveAgentStatus = new()
                            {
                                AgentCode = EmpId,
                                status = "LOGOUT",
                                Source = "matrix"
                            };
                            updateAgentLoginStatus(predictiveAgentStatus);
                            LogOutfromDB(UserId, 22, 124);
                            count++;
                        }
                        catch (Exception ex)
                        {
                            LoggingHelper.LoggingHelper.AddloginQueue(EmpId, 0, ex.ToString(), "GrpAgentsLogoutBulk", "Onelead", "DialerBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
                        }
                    }

                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(queue, 0, ex.ToString(), "GrpAgentsLogoutBulk", "Onelead", "DialerBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
        }

        return count;
    }

    public static void LogOutfromDB(long userId, int logOutType,long logoutBy)
    {
        try
        {
            var objLogin = new LogInDTO
            {
                UserId = userId,
                IsActive = false,
                LogOutBy = logoutBy,
                LogOutType = logOutType
            };
            DialerDLL.UpdateLoginDetails(objLogin);
        }
        catch (Exception ex)
        {
            LoggingHelper.LoggingHelper.AddloginQueue("", userId, ex.ToString(), "LogOutfromDB", "Onelead", "DialerBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
        }
    }
}

