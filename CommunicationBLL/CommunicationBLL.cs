using System;
using System.Data;
using System.Net.NetworkInformation;
using System.Text.RegularExpressions;
using DataAccessLayer;
using DataAccessLibrary;
using Helper;
using Newtonsoft.Json;
using OBConnectCall;
using PropertyLayers;
using ReadXmlProject;
using Redis;
using StackExchange.Redis;


namespace CommunicationBLL
{
    public class CommunicationBLL : ICommunicationBLL
    {
        public EasyDialSoftPhoneResponse PrepareCallData(CallRequestObj callRequest, bool isUAE = false)
        {
            string ExMsg = string.Empty;
            DateTime Requesttime = DateTime.Now;
            bool isCallViaDialerApp = false; // DIALER_APP

            CommunicationModel objModel = new()
            {
                LeadID = callRequest.LeadID,
                UserID = callRequest.userId,
                Redial = callRequest.redial,
                PriorityReasonId = callRequest.PriorityReasonId,
                callingSource = callRequest.source,
                AsteriskIP = callRequest.AsteriskIP
            };

            EasyDialSoftPhoneResponse obj = new() { LeadID = callRequest.LeadID };            

            if (callRequest.LeadID > 0 && callRequest.customerId > 0)
            {
                try
                {
                    if (callRequest.userId == 0)
                    {
                        obj.Status = "UserId not present.";
                        return obj;
                    }

                    /*validate call restriction for the lead calling from fos APP*/
                    ValidateleadForCalling(callRequest, obj);
                    if (!string.IsNullOrEmpty(obj.Status))
                        return obj;

                    CallingData _CallingData;

                    if (string.IsNullOrEmpty(callRequest.EncryptedMobileNo))
                    {
                        _CallingData = LeadPrioritizationDLL.GetCallableNumber(callRequest.LeadID, callRequest.customerId);
                    }
                    else
                    {
                        _CallingData = new CallingData()
                        {
                            MobileNumber = CryptoHelper.Decrytion_Payment_AES(callRequest.EncryptedMobileNo, "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings()),
                            CountryCode = callRequest.CountryCode,
                            CountryID = callRequest.CountryId != null ? (short)callRequest.CountryId : (short)0,
                        };
                    }

                    //TODO change later temp return
                    int callCountinHour = 0; //callRequest.attempt ? 0 : CallCommunicationDLL.CallCountInLastHour(callRequest.LeadID, "CallCountInLastHour".AppSettings());

                    if (callCountinHour < 2)
                    {
                        DataSet ds = CallCommunicationDLL.GetPredictiveLeadDetails(callRequest.LeadID, objModel.PriorityReasonId, callRequest.userId);
                        if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                        {
                            var rowObj = ds.Tables[0].Rows[0];

                            if (rowObj != null)
                            {
                                int leadGroupId = rowObj["AssignedToGroupId"] != DBNull.Value ? Convert.ToInt32(rowObj["AssignedToGroupId"]) : 0;

                                (bool isRenewalGroup, string IsCallAllowedMessage) = LeadPrioritizationDLL.IsCallAllowed_UnassignedUser(callRequest.userId,leadGroupId);

                                long IsAgentNumber = rowObj["IsCalledAgent"] == DBNull.Value ? Convert.ToInt64(0) : Convert.ToInt64(rowObj["IsCalledAgent"]);
                                string? mobNumber = rowObj["MobileNo"] == DBNull.Value ? string.Empty : Convert.ToString(rowObj["MobileNo"]);

                                if ((IsAgentNumber == 0 || isRenewalGroup) && !string.IsNullOrEmpty(mobNumber)) //PB Agent Case
                                {
                                    long AssignedToUserID = rowObj["AssignedToUserID"] != DBNull.Value ? Convert.ToInt64(rowObj["AssignedToUserID"]) : 0;//Booked Agent in 7,115 only
                                    long CurrentAssignedAgentID = rowObj["CurrentAssignedUserID"] != DBNull.Value ? Convert.ToInt64(rowObj["CurrentAssignedUserID"]) : 0;

                                    if (!isRenewalGroup && callRequest.roleId == 13 && (AssignedToUserID != callRequest.userId && CurrentAssignedAgentID != callRequest.userId))//Both booked agent and current assigned can call on lead for 7,115 
                                    {
                                        obj.Status = string.IsNullOrEmpty(IsCallAllowedMessage) ? "Assignment changed." : IsCallAllowedMessage;
                                        LeadPrioritizationDLL.SkipLead(callRequest.LeadID, 6);
                                        return obj;
                                    }

                                    short CountryId = Convert.ToInt16(rowObj["CountryId"]);
                                    Conversations objConversations = new Conversations
                                    {
                                        ToReceipent = new List<string> { mobNumber },
                                        UserID = callRequest.userId,
                                        AgentID = rowObj["CalledEmp"] == DBNull.Value ? string.Empty : Convert.ToString(rowObj["CalledEmp"]),
                                        CountryCode = rowObj["CountryCode"] == DBNull.Value ? string.Empty : Convert.ToString(rowObj["CountryCode"]),
                                    };

                                    //change country code and mobileno if mismatched in customer DB and leaddetails
                                    if (_CallingData != null && (!string.IsNullOrEmpty(_CallingData.MobileNumber))
                                        && Convert.ToInt64(_CallingData.MobileNumber) > 0
                                        && (Convert.ToInt16(rowObj["ProductID"]) != 115 || _CallingData.MobileNumber != Convert.ToString(mobNumber)))
                                    {
                                        CountryId = _CallingData.CountryID != 0 ? _CallingData.CountryID : CountryId;
                                        objConversations.ToReceipent = (!string.IsNullOrEmpty(_CallingData.MobileNumber)) ? new List<string> { _CallingData.MobileNumber } : objConversations.ToReceipent;
                                        objConversations.CountryCode = (!string.IsNullOrEmpty(_CallingData.CountryCode)) ? _CallingData.CountryCode : objConversations.CountryCode;
                                        mobNumber = (!string.IsNullOrEmpty(_CallingData.MobileNumber)) ? _CallingData.MobileNumber : mobNumber;
                                    }

                                    if (mobNumber.Length < 5)
                                    {
                                        obj.Status = "Invalid calling number";
                                        LeadPrioritizationDLL.SkipLead(callRequest.LeadID, 7);
                                        return obj;
                                    }

                                    short areaCode = Convert.ToInt16(mobNumber.Substring(0, 3));
                                    string? NriCity = (rowObj["NriCity"] != null && rowObj["NriCity"] != DBNull.Value) ? Convert.ToString(rowObj["NriCity"]) : "";
                                    // NriCity="";
                                    bool status = LeadPrioritizationDLL.IsCorrectTimeToCall(CountryId, areaCode, NriCity);

                                    if (!status)
                                    {
                                        obj.Status = "Calling at this Time is not allowed";
                                        LeadPrioritizationDLL.SkipLead(callRequest.LeadID, 61);
                                        return obj;
                                    }

                                    PreferenceStatus objPreferenceStatus = new()
                                    {
                                        LeadId = callRequest.LeadID,
                                        CustomerId = callRequest.customerId,
                                        CategoryId = 1,
                                        CommType = Convert.ToByte(CommunicationType.Call),
                                        MobileNo = Convert.ToInt64(mobNumber)
                                    };

                                    //prepare calling obj
                                    objModel.LeadID = rowObj["LeadID"] == DBNull.Value ? 0 : Convert.ToInt64(rowObj["LeadID"]);
                                    objModel.Conversations = new List<Conversations> { objConversations };
                                    objModel.ProductID = rowObj["ProductID"] == DBNull.Value ? (short)0 : Convert.ToInt16(rowObj["ProductID"]);
                                    objModel.LeadSource = rowObj["LeadSource"] == DBNull.Value ? string.Empty : Convert.ToString(rowObj["LeadSource"]);
                                    objModel.SubProductId = rowObj["SubProductId"] == DBNull.Value ? (short)0 : Convert.ToInt16(rowObj["SubProductId"]);
                                    
                                    if(objModel.AsteriskIP == null || objModel.AsteriskIP == "") {
                                        objModel.AsteriskIP = rowObj["Asterisk_IP"] == DBNull.Value ? string.Empty : Convert.ToString(rowObj["Asterisk_IP"]);
                                    }

                                    if (objModel.ProductID == 131 && (CoreCommonMethods.IsValidString(callRequest.VirtualNumber)))
                                    {
                                        objModel.VirtualNumber = callRequest.VirtualNumber;
                                    }

                                    short reasonId;
                                    ResponseAPI objResult = IsCustomerBlocked(objPreferenceStatus, objModel, out reasonId);
                                    if (objResult.status)
                                    {
                                        obj = ConnectCall.DialOBCall(objModel, isUAE, ref isCallViaDialerApp, callRequest.CallOnAswat, reasonId);
                                        if (objModel.CallTrackingID > 0)
                                        {
                                            if (obj.Status.ToUpper() == "SUCCESS")
                                            {
                                                UpdateAgentStatus(obj.empId, obj.LeadID);
                                                //LeadPrioritizationDLL.UpdateWorkDoneLog(objModel.LeadID, objModel.UserID, reasonId, obj.uid, 2);

                                                obj.Status = "CallInitiated";
                                            }
                                            else
                                                MarkCallFailed(objModel, obj, reasonId);

                                        }
                                        else
                                        {
                                            obj.Status = "DB Issue";
                                        }
                                    }
                                    else
                                        obj.Status = objResult.message;
                                }
                                else
                                {
                                    obj.Status = "PB Agent Lead";
                                    LeadPrioritizationDLL.SkipLead(objModel.LeadID, 1441);
                                    LeadPrioritizationDLL.LogLeadHistory(124, objModel.LeadID, obj.Status);
                                    LeadPrioritizationDLL.UpdateWorkDoneLog(objModel.LeadID, objModel.UserID, objModel.PriorityReasonId, obj.uid, 20, 2);
                                }
                            }
                            else
                                obj.Status = "Lead Issue";
                        }
                        else
                            obj.Status = "Lead Issue";
                    }
                    else
                        obj.Status = "Attempts exceed";
                }
                catch (Exception ex)
                {
                    obj.Status = "Error Occured";
                    ExMsg = ex.ToString();
                }
                finally
                {
                    if (obj.Status != "Attempts exceed")
                    {
                        Int64 MobileNo = 0;
                        if (objModel.Conversations != null && objModel.Conversations.Count > 0 && objModel.Conversations[0] != null)
                            MobileNo = Convert.ToInt64(objModel.Conversations[0].ToReceipent[0]);

                        LeadPrioritizationDLL.UpdateCallStatusOnCallInitiate(objModel.LeadID, obj.Status, objModel.UserID, objModel.CallTrackingID, MobileNo);
                        if(isCallViaDialerApp && obj.Status== "CallInitiated") {
                            ConnectCall.CallViaDialerApp(objModel);
                        }
                    }

                    LoggingHelper.LoggingHelper.AddloginQueue(string.Empty, objModel.LeadID, ExMsg, "ConnectCallSoftPhone", "CommAPI", "CommunicationBLL", JsonConvert.SerializeObject(objModel), JsonConvert.SerializeObject(obj), Requesttime, DateTime.Now);
                }
            }

            return obj;
        }

        private EasyDialSoftPhoneResponse ValidateleadForCalling(CallRequestObj callRequest, EasyDialSoftPhoneResponse obj)
        {
            if (callRequest.source == "fosapp" || callRequest.PriorityReasonId == 0 || callRequest.PriorityReasonId == 71)            
            {
                UserNext5Leads _ValidateLead = new UserNext5Leads();
                _ValidateLead.Leads = new List<Next5WidgetLead>()
                                                        {
                                                            new Next5WidgetLead { LeadId = callRequest.LeadID,
                                                            IsAddLeadtoQueue=0,IsNeedToValidate=callRequest.source == "fosapp" ? 0 : 1

                                                            }
                                                        };
                _ValidateLead.UserId = callRequest.userId;
                _ValidateLead.Source = "fosapp";

                LeadPrioritizationBLL _LeadPrioritizationBLL = new LeadPrioritizationBLL();
                var _ValidateionResponse = _LeadPrioritizationBLL.ValidateAddLeadToPriorityQueue(_ValidateLead);
                if (_ValidateionResponse.status == 0)
                {
                    obj.Status = "attemptexceed";
                    obj.Message = _ValidateionResponse.message;                    
                }                
            }
            return obj;
        }

        public static ResponseAPI CheckCustomerPrefStatus(PreferenceStatus obj)
        {
            ResponseAPI response = new() { status = true };

            response.status = CallCommunicationDLL.GetCommPreferrences(obj.MobileNo, obj.CustomerId, obj.LeadId, obj.CategoryId, obj.CommType);

            if (!response.status)/*If customer pref is disabled ,Check for current lead leadsource/Status (Renewal or bookings)*/
            {
                if (obj.LeadId > 0)
                {
                    DataTable dt = CallCommunicationDLL.GetLeadBasicDetails(obj.LeadId);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        string? leadsource = Convert.ToString(dt.Rows[0]["LeadSource"]);
                        int statusId = Convert.ToByte(dt.Rows[0]["StatusID"]);
                        if (!string.IsNullOrEmpty(leadsource) && leadsource.ToUpper().Equals("RENEWAL"))
                        {
                            obj.CategoryId = 4;
                        }
                        else if (statusId >= 13 && statusId != 14)
                        {
                            obj.CategoryId = 2;
                        }

                        if (obj.CategoryId == 2 || obj.CategoryId == 4)
                            response.status = CallCommunicationDLL.GetCommPreferrences(obj.MobileNo, obj.CustomerId, obj.LeadId, obj.CategoryId, obj.CommType);
                    }
                }
            }
            if (!response.status)
            {
                response.message = "Communication blocked";
            }

            return response;
        }

        private static int CallingBlockedCheck(long leadId, long customerId, long userId, int productId, out short reasonId, short PriorityReasonId = 0)
        {
            reasonId = 0;
            DateTime Requesttime = DateTime.Now;
            try
            {

                if (PriorityReasonId == 0 && LeadPrioritizationDLL.ChkBookedLeadinpriorityQueue(leadId, userId, out reasonId) == true)
                    return 0;
                else
                    reasonId = PriorityReasonId;
                var flag = 1;
                if (Convert.ToBoolean("BlockedNumberCalling".AppSettings()))
                    flag *= 2;

                String productIdListForChat = "ProductIDListForChat".AppSettings();
                if (!Convert.ToBoolean("CheckChatStatus".AppSettings()))
                    flag *= 3;
                else if (!productIdListForChat.Contains("," + productId + ","))
                    flag *= 3;

                return CallCommunicationDLL.CallingBlockedCheck(leadId, customerId, userId, flag);
            }

            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", leadId, ex.ToString(), "ChkBookedLeadinpriorityQueue", "CommAPI", "LeadPrioritizationDLL", "", "counter", Requesttime, DateTime.Now);
                return 0;
            }
        }       
        private static ResponseAPI IsCustomerBlocked(PreferenceStatus objPreferenceStatus, CommunicationModel objModel, out short reasonId)
        {
            reasonId = 0;
            ResponseAPI objResult = CheckCustomerPrefStatus(objPreferenceStatus);
            if (!objResult.status)
            {
                objResult.message = "Customer Blocked";
                objPreferenceStatus.CommType = Convert.ToByte(CommunicationType.Email);
                objResult = CheckCustomerPrefStatus(objPreferenceStatus);
                if (objResult.status)
                {
                    objResult.status = false;
                    objResult.message = "Email Only Customer.";
                    LeadPrioritizationDLL.SkipLead(objModel.LeadID, 62);
                    LeadPrioritizationDLL.LogLeadHistory(124, objModel.LeadID, objResult.message);
                }
            }
            else
            {
                int res = CallingBlockedCheck(objModel.LeadID, objPreferenceStatus.CustomerId, objModel.UserID, objModel.ProductID, out reasonId, objModel.PriorityReasonId);
                switch (res)
                {
                    case 0:
                        objResult.status = true;
                        objResult.message = "success";
                        break;
                    case 2:
                        objResult.status = false;
                        objResult.message = "Customer Is On Chat";
                        break;
                    default:
                        objResult.status = false;
                        objResult.message = "Call Not Allowed";
                        break;
                }

                if (!objResult.status)
                {
                    LeadPrioritizationDLL.SkipLead(objModel.LeadID, 4);
                    LeadPrioritizationDLL.LogLeadHistory(124, objModel.LeadID, objResult.message);
                }
            }

            return objResult;
        }

        public static void UpdateAgentStatus(string agentCode, long leadId)
        {
            DateTime Requesttime = DateTime.Now;
            try
            {
                PredictiveAgentStatus predictiveAgentStatus = new PredictiveAgentStatus
                {
                    AgentCode = agentCode,
                    _updatedAt = DateTime.Now,
                    status = "CALLINITIATED",
                    CallType = "OB",
                    LeadId = Convert.ToString(leadId),
                    opensv = 0,
                    IsCustAnswered = false,
                };

                PredictiveAgentStatusRedis.updateAgentStatusByRedis(predictiveAgentStatus, false);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", leadId, ex.ToString(), "UpdateAgentStatus", "CommAPI", "LeadPrioritizationDLL", "", "", Requesttime, DateTime.Now);
            }
        }

        private static void MarkCallFailed(CommunicationModel objModel, EasyDialSoftPhoneResponse obj, short reasonId)
        {
            bool updateDB = false;
            DialerDispDetails _DispositionUpdate = new DialerDispDetails()
            {
                LeadID = objModel.LeadID,
                Duration = 1,
                talktime = 0,
                CallType = "OB",
                CallId = Guid.NewGuid().ToString("N"),
                CallTrackingID = obj.uid
            };

            if (obj.Status.ToUpper() == "BLOCKED")
            {
                updateDB = true;
                obj.Status = "Customer Blocked";
                LeadPrioritizationDLL.SkipLead(objModel.LeadID, 8);
                LeadPrioritizationDLL.LogLeadHistory(124, objModel.LeadID, obj.Status);
            }
            else if (obj.Status.ToUpper() == "FAILED")
            {
                updateDB = true;
                obj.Status = obj.Message;
                LeadPrioritizationDLL.SkipLead(objModel.LeadID, 5);
                LeadPrioritizationDLL.LogLeadHistory(objModel.UserID, objModel.LeadID, obj.Status);
            }
            else
            {
                updateDB = true;
                obj.Status = "Call Conjunction";
            }

            if (updateDB)
            {
                _DispositionUpdate.Disposition = obj.Status;
                LeadPrioritizationDLL.UpdateCalldata(_DispositionUpdate);

            }
        }

        public bool UpdateCallStatus(CallStatus callStatus)
        {
            string status = callStatus.status == null ? " " : callStatus.status;
            bool resp = LeadPrioritizationDLL.UpdateCallStatusOnCallInitiate(callStatus.leadid, status, callStatus.userid);
            return resp;
        }

        public List<CallingDataFields> getCallingNoData(string CustomerId, string ProductId, string LeadId)
        {
            List<CallingDataFields> listCallingNo = new();

            string exstr = string.Empty;
            string? MobileNo = string.Empty;
            short? CountryCode = 0;
            short? CountryId = 0;
            bool HasCrossSell = false;
            string? Country = string.Empty;
            bool unMaskMobileNo = false;
            string? IntProducts = "intProductIds".AppSettings();
            
            try
            {
                string? Key = RedisCollection.CallingList() + ":" + CustomerId;
                string? obj = RedisHelper.GetRedisData(Key);

                if (obj == null)
                {
                    if (!string.IsNullOrEmpty(CustomerId) && !string.IsNullOrEmpty(ProductId) && !string.IsNullOrEmpty(LeadId))
                    {
                        CallingNoModel? objCalling = LeadPrioritizationDLL.getMobileDetails(Convert.ToInt64(LeadId), CustomerId);
                        if (IntProducts.Contains("," + ProductId + ","))
                            unMaskMobileNo = true;


                        //==========================  Get Data  from DB from international products  ====================/
                        DataSet? ds = LeadPrioritizationDLL.GetCallingNumberData(CustomerId, LeadId);

                        if (objCalling != null && objCalling.GetCallingNumberResult != null && objCalling.GetCallingNumberResult.Data.Count > 0 && ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                        {
                            MobileNo = ds.Tables[0].Rows[0]["MobileNo"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["MobileNo"]) : "";
                            CountryCode = ds.Tables[0].Rows[0]["CountryCode"] != DBNull.Value ? Convert.ToInt16(ds.Tables[0].Rows[0]["CountryCode"]) : Convert.ToInt16(0);
                            Country = ds.Tables[0].Rows[0]["Country"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["Country"]) : string.Empty;
                            CountryId = ds.Tables[0].Rows[0]["CountryId"] != DBNull.Value ? Convert.ToInt16(ds.Tables[0].Rows[0]["CountryId"]) : Convert.ToInt16(0);
                            HasCrossSell = ds.Tables[0].Rows[0]["HasCrossSell"] != DBNull.Value ? Convert.ToBoolean(ds.Tables[0].Rows[0]["HasCrossSell"]) : false;
                        }
                        //=======================   Get Data  from API  =========================================//

                        if (objCalling != null && objCalling.GetCallingNumberResult != null && objCalling.GetCallingNumberResult.Data.Count > 0)
                        {
                            for (int i = 0; i < objCalling.GetCallingNumberResult.Data.Count; i++)
                            {
                                CallingDataFields? objCallingNumberResult = objCalling.GetCallingNumberResult.Data[i];
                                string? DecryptPhoneNo = CryptoHelper.Decrytion_Payment_AES(objCalling.GetCallingNumberResult.Data[i].EncryptedMobileNo.ToString(), "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings());


                                CallingDataFields? objCallingNumber = new()
                                {
                                    Name = objCallingNumberResult.Name,
                                    IsPrimary = Convert.ToBoolean(objCallingNumberResult.IsPrimary),
                                    IsCallable = Convert.ToBoolean(objCallingNumberResult.IsCallable),
                                    EncryptedMobileNo = objCallingNumberResult.EncryptedMobileNo,
                                    CustMobId = objCallingNumberResult.CustMobId != 0 ? Convert.ToInt32(objCallingNumberResult.CustMobId) : Convert.ToInt32(0),
                                    CreatedOn = Convert.ToString(objCallingNumberResult.CreatedOn),
                                    IsEmergencyContact = Convert.ToBoolean(objCallingNumberResult.IsEmergencyContact)
                                };

                                if (DecryptPhoneNo == MobileNo && objCallingNumberResult.CountryCode != CountryCode && unMaskMobileNo)
                                    objCallingNumberResult.CountryId = Convert.ToInt16(CountryId);


                                if (DecryptPhoneNo == MobileNo && objCallingNumberResult.CountryId != CountryId && Convert.ToInt16(ProductId) == 115)
                                {
                                    objCallingNumberResult.CountryId = Convert.ToInt16(CountryId);
                                    objCallingNumberResult.Country = Convert.ToString(Country);
                                    objCallingNumberResult.CountryCode = Convert.ToInt16(CountryCode);
                                }


                                objCallingNumber.MobileNo = !HasCrossSell && unMaskMobileNo && objCallingNumberResult.CountryId != 392
                                 ? DecryptPhoneNo : objCallingNumberResult.MobileNo;


                                objCallingNumber.CountryCode = DecryptPhoneNo == MobileNo &&
                                    objCallingNumberResult.CountryCode != CountryCode && unMaskMobileNo ?
                                    Convert.ToInt16(CountryCode) : Convert.ToInt16(objCallingNumberResult.CountryCode);

                                objCallingNumber.CountryId = DecryptPhoneNo == MobileNo &&
                                   objCallingNumberResult.CountryCode != CountryCode && unMaskMobileNo ?
                                    Convert.ToInt16(CountryId) : Convert.ToInt16(objCallingNumberResult.CountryId);

                                objCallingNumber.Country = DecryptPhoneNo == MobileNo &&
                                  objCallingNumberResult.CountryCode != CountryCode && unMaskMobileNo ? Country
                                  : Convert.ToString(objCallingNumberResult.Country);

                                listCallingNo.Add(objCallingNumber);
                            }
                        }

                        //If no data exist then chk from Leaddetails
                        if (listCallingNo.Count == 0)
                        {
                            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                            {
                                CallingDataFields objCallingNumber = new CallingDataFields();
                                string? EncrytPhoneNo = CryptoHelper.Encrytion_Payment_AES(ds.Tables[0].Rows[0]["MobileNo"].ToString(), "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings(), false);

                                objCallingNumber.Name = ds.Tables[0].Rows[0]["Name"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["Name"]) : "";
                                objCallingNumber.IsPrimary = ds.Tables[0].Rows[0]["IsPrimary"] != DBNull.Value ? Convert.ToBoolean(ds.Tables[0].Rows[0]["IsPrimary"]) : true;
                                objCallingNumber.IsCallable = ds.Tables[0].Rows[0]["IsCallable"] != DBNull.Value ? Convert.ToBoolean(ds.Tables[0].Rows[0]["IsCallable"]) : true;
                                objCallingNumber.EncryptedMobileNo = EncrytPhoneNo;
                                objCallingNumber.CreatedOn = ds.Tables[0].Rows[0]["CreatedOn"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["CreatedOn"]) : "";
                                objCallingNumber.CountryId = ds.Tables[0].Rows[0]["countryId"] != DBNull.Value ? Convert.ToInt16(ds.Tables[0].Rows[0]["countryId"]) : 0;
                                objCallingNumber.MobileNo = !HasCrossSell && unMaskMobileNo && objCallingNumber.CountryId != 392
                                 ? Convert.ToString(ds.Tables[0].Rows[0]["MobileNo"]) : Convert.ToString(ds.Tables[0].Rows[0]["MobileNo"]).Mask(fromLeft: true, numberOfChars: Convert.ToString(ds.Tables[0].Rows[0]["MobileNo"]).Length - 2);

                                objCallingNumber.CustMobId = ds.Tables[0].Rows[0]["CustMobId"] != DBNull.Value ? Convert.ToInt16(ds.Tables[0].Rows[0]["CustMobId"]) : 0;
                                objCallingNumber.CountryCode = ds.Tables[0].Rows[0]["CountryCode"] != DBNull.Value ? Convert.ToInt16(ds.Tables[0].Rows[0]["CountryCode"]) : 0;

                                objCallingNumber.Country = ds.Tables[0].Rows[0]["Country"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["Country"]) : "";

                                listCallingNo.Add(objCallingNumber);
                            }
                        }

                        //RedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(listCallingNo), new TimeSpan(12, 0, 0, 0));

                        if (listCallingNo.Count == 0)
                            RedisHelper.SetRedisData(Key, null, new TimeSpan(0, 0, 0, 1));
                        else
                            RedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(listCallingNo), new TimeSpan(8, 0, 0));

                    }
                }
                else
                    listCallingNo = JsonConvert.DeserializeObject<List<CallingDataFields>>(obj);


                #region Motor Renwal - CCTEC-5546
                if (listCallingNo != null && listCallingNo.Count > 0 && Convert.ToInt16(ProductId) == 117) {
                    DataSet ds = CallCommunicationDLL.GetEmergencyCallableNum(Convert.ToInt64(LeadId));
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0 && ds.Tables[0].Rows[0]["Reason"] != DBNull.Value && ds.Tables[0].Rows[0]["Reason"].ToString() == "MotorRenewal_SecondaryNumSwitch" && ds.Tables[0].Rows[0]["CustMobId"] != DBNull.Value ) {
                        foreach (var callingNo in listCallingNo) {
                            callingNo.IsCallable = false;
                            if (callingNo.CustMobId == Convert.ToInt32(ds.Tables[0].Rows[0]["CustMobId"])) {
                                callingNo.IsCallable = true;
                            }
                        }
                    }
                }
                #endregion

                #region Emergency Number
                if (listCallingNo != null && listCallingNo.Exists((obj) => obj.IsEmergencyContact))
                {
                    PriorityModel leadData = LeadPrioritizationDLL.GetPriorityModelMongo(Convert.ToInt64(LeadId));
                    if (leadData!=null
                        && !string.IsNullOrEmpty(leadData.LeadSource) && leadData.LeadSource.ToLower() == "renewal"
                        && leadData.CustEmergencyNo != null && leadData.CustEmergencyNo.CustMobId>0
                        )
                    {
                        //DataSet ds = CallCommunicationDLL.GetEmergencyCallableNum(Convert.ToInt64(LeadId));
                        
                            long emergencyMobId = leadData.CustEmergencyNo.CustMobId;
                            if (emergencyMobId > 0) {
                                foreach(var callingNo in listCallingNo) {
                                    callingNo.IsCallable = false;
                                    if(callingNo.CustMobId == emergencyMobId) {
                                        callingNo.IsCallable = true;
                                    }
                                }
                            }
                        
                    }

                }
                #endregion
            }
            catch (Exception ex)
            {
                exstr = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(LeadId), exstr, "getCallingNoData", "CommAPI", "CommunicationBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }            
            return listCallingNo;
        }

        public string getCustContactInfo(string CustomerId, string Type, string LeadId)
        {
            DateTime requestTime = DateTime.Now;
            string data = string.Empty;
            string url = "coreAPI".AppSettings();
            try
            {
                string CustId = CryptoHelper.Encrytion_Payment_AES(CustomerId.ToString(), "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings(), true);
                //Dictionary<string, string> _Dict = new Dictionary<string, string>();
                Dictionary<object, object> _Dict = new Dictionary<object, object>();
                _Dict.Add("authKey", "coreAPIauthKey".AppSettings());
                _Dict.Add("clientKey", "coreAPIclientKey".AppSettings());
                int timeout = Convert.ToInt32("CommunicationAPItimeout".AppSettings());

                if (!string.IsNullOrEmpty(Type) && Type.ToLower() == "emailid")
                    url = url + "cs/mtx/customerEmails?customerId=" + CustId;
                else if (!string.IsNullOrEmpty(Type) && Type.ToLower() == "mobileno"){
                    url = "coreAPIv1".AppSettings();
                    url = url + "cs/mtx/getMobileDetails?customerId=" + CustId;
                }

                if (!string.IsNullOrEmpty(url))
                    data = CommonAPICall.CallAPI(url, "", "GET", 100000, "application/json", _Dict);


            }
            catch (Exception ex)
            {
                string TrackingID = string.Empty;
                if (LeadId == "0")
                    TrackingID = "custid-" + CustomerId;
                LoggingHelper.LoggingHelper.AddloginQueue(TrackingID, Convert.ToInt64(LeadId), ex.ToString(), "getCustContactInfo", "CommAPI", "CommunicationBLL", url, string.Empty, requestTime, DateTime.Now);
                return null;
            }

            return data;
        }

        public string getMaskedCustContactInfo(string CustomerId, string Type, string LeadId)
        {
            string data = getCustContactInfo(CustomerId, Type, LeadId);

            if (Type.ToLower() == "emailid")
            {
                Regex regex = new Regex(@"^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$", RegexOptions.CultureInvariant | RegexOptions.Singleline);
                var Response = JsonConvert.DeserializeObject<dynamic>(data);
                if (Response!=null && Response.statusMsg == "SUCCESS")
                {
                    string pattern = @"(?<=[\w]{1})[\w-\._\+%]*(?=[\w]{2}@)";
                    foreach (dynamic admObj in Response.Data)
                    {
                        admObj.EncryptedEmail = null;
                        string input = admObj.Email.Value;
                        var isValidEmail = regex.IsMatch(input) && !input.ToLower().Contains("test") && !input.ToLower().Contains("healthmobonly");
                        if (isValidEmail)
                        {
                            admObj.EncryptedEmail.Value = CryptoHelper.Encrytion_Payment_AES(input, "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings(), true);
                            admObj.Email.Value = Regex.Replace(input, pattern, m => new string('*', m.Length));
                        }
                    }
                }
                data = JsonConvert.SerializeObject(Response);

            }
            return data;
        }

        public string AswatIndiaLogin(string AgentID)
        {
            string context = "", IP = "", DIDNo = "", CallingCompany = "", VirtualNo = "", AswatPosition="";
            int groupId = 0;
            bool iSWFH = false;
            Int16 AgentCountry = 0;
            var agentContext = CallCommunicationDLL.GetContexQueue(AgentID, ref context, ref IP, ref DIDNo, ref CallingCompany, ref groupId, ref VirtualNo, ref iSWFH, ref AgentCountry,ref AswatPosition);
            if (CallingCompany == "ASWATINDIA" && !string.IsNullOrEmpty(AswatPosition))
            {
                string apiKey = "aswat_india_api_key".AppSettings();
                var url = "aswatindiacallingurl".AppSettings() + "integrations/cti/agents/" + AswatPosition + "/login/" + AgentID.ToLower();
                string json = "";
                Dictionary<string, string> headers = new Dictionary<string, string>() { { "api_key", apiKey } };
                var result = CommonAPICall.PostAPICall_Aswat(url, Convert.ToInt32("DialerAPITimeout".AppSettings()), json, headers);
                if (result == "") { result = AswatIndiaStatus(AgentID, "1"); }
                return result;
            }
            return "";
        }
        public string AswatIndiaLogout(string AgentID)
        {

            string context = "", IP = "", DIDNo = "", CallingCompany = "", VirtualNo = "", AswatPosition="";
            int groupId = 0;
            bool iSWFH = false;
            Int16 AgentCountry = 0;
            var agentContext = CallCommunicationDLL.GetContexQueue(AgentID, ref context, ref IP, ref DIDNo, ref CallingCompany, ref groupId, ref VirtualNo, ref iSWFH, ref AgentCountry,ref AswatPosition);
            if (CallingCompany == "ASWATINDIA" && !string.IsNullOrEmpty(AswatPosition))
            {
                string apiKey = "aswat_india_api_key".AppSettings();
                var url = "aswatindiacallingurl".AppSettings() + "integrations/cti/agents/" + AswatPosition + "/logout/";
                string json = "";
                Dictionary<string, string> headers = new Dictionary<string, string>() { { "api_key", apiKey } };
                var result = CommonAPICall.PostAPICall_Aswat(url, Convert.ToInt32("DialerAPITimeout".AppSettings()), json, headers);
                return result;
            }
            return "";
        }
        public string AswatIndiaStatus(string AgentID, string status)
        {

            string context = "", IP = "", DIDNo = "", CallingCompany = "", VirtualNo = "", AswatPosition="";
            int groupId = 0;
            bool iSWFH = false;
            Int16 AgentCountry = 0;
            var agentContext = CallCommunicationDLL.GetContexQueue(AgentID, ref context, ref IP, ref DIDNo, ref CallingCompany, ref groupId, ref VirtualNo, ref iSWFH, ref AgentCountry,ref AswatPosition);
            if (CallingCompany == "ASWATINDIA" && !string.IsNullOrEmpty(AswatPosition))
            {
                string apiKey = "aswat_india_api_key".AppSettings();
                string apiToken = "aswat_india_access_token".AppSettings();
                var url = "aswatindiacallingurl".AppSettings() + "live/agents/" + AswatPosition + "/status/" + status;
                string json = "";
                Dictionary<string, string> headers = new Dictionary<string, string>() { { "api_key", apiKey }, { "access_token", apiToken } };
                //Dictionary<string, string> headers = new Dictionary<string, string>() { { "api_key", apiKey } };
                var result = CommonAPICall.PutAPICall_Aswat(url, Convert.ToInt32("DialerAPITimeout".AppSettings()), json, headers);
                return result;
            }
            return "";
        }
    }
}

