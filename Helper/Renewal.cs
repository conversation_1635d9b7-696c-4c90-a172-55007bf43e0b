﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Helper
{
    public class Renewal
    {
        public static string FormatQuote(string input)
        {
            if (string.IsNullOrEmpty(input) || input == "0")
            {
                return string.Empty;
            }
            else
            {
                return input;
            }
        }
        public static string FormatPremium(string input)
        {
            if (string.IsNullOrEmpty(input) || input == "0")
            {
                return string.Empty;
            }
            else
            {
                return input;
            }
        }
        public static long ToSafeLong(String Input)
        {
            if (Input.Contains("."))
            {
                float i;
                float.TryParse(Input, out i);
                return (long)i;
            }
            else
            {
                long i = 0;
                long.TryParse(Input, out i);
                return i;
            }
        }

        public static int ToSafeInt(String Input)
        {
            if (Input.Contains("."))
            {
                float i;
                float.TryParse(Input, out i);
                return (int)i;
            }
            else
            {
                int i = 0;
                int.TryParse(Input, out i);
                return i;
            }
        }

        public static bool ToSafeBool(String Input)
        {
            if (Input.Length == 0)
            {

                return false;
            }
            else
            {
                return true;
            }
        }
    }
}
