﻿using System;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace Helper
{
    public static class CryptoHelper
    {
        public static String Encrytion_Payment_AES(String Input, string encSource, int KeySize, int BlockSize, string encKey, string IVKey, bool IsUrlEncoded = true)
        {
            String Output = string.Empty;
            try
            {
                var aes = new RijndaelManaged();
                aes.KeySize = KeySize;
                aes.BlockSize = BlockSize;
                aes.Padding = PaddingMode.PKCS7;
                aes.Key = Encoding.UTF8.GetBytes(encKey);
                aes.IV = Encoding.UTF8.GetBytes(IVKey);
                var encrypt = aes.CreateEncryptor(aes.Key, aes.IV);
                byte[]? xBuff = null;
                using (var ms = new MemoryStream())
                {
                    using (var cs = new CryptoStream(ms, encrypt, CryptoStreamMode.Write))
                    {
                        byte[] xXml = Encoding.UTF8.GetBytes(Input);
                        cs.Write(xXml, 0, xXml.Length);
                    }

                    xBuff = ms.ToArray();
                }
                Output = Convert.ToBase64String(xBuff);
                if (IsUrlEncoded)
                    Output = UrlEncode(Output);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in Encrytion_Payment_AES: " + ex.ToString());
            }

            return Output;
        }

        public static string UrlEncode(string input)
        {
            return HttpUtility.UrlEncode(input);
        }

        public static String Encrypt_AES256(String Input)
        {
            String enryptStringCrypto = "";
            string Inputkey = "AKSA18CDY3464CG0A2E8O71F9B6B9EA9";
            var result = EncryptRijndael(Input, ref enryptStringCrypto, Inputkey);
            return enryptStringCrypto;
        }

        private static RijndaelManaged NewRijndaelManaged(string Inputkey)
        {
            var aesAlg = new RijndaelManaged();
            aesAlg.Key = Encoding.ASCII.GetBytes(Inputkey);
            aesAlg.IV = Encoding.ASCII.GetBytes(Inputkey.Substring(0, 16));
            return aesAlg;
        }

        public static bool EncryptRijndael(string plainString, ref string encString, String Key)
        {
            try
            {
                if (string.IsNullOrEmpty(plainString))
                    throw new ArgumentNullException("Not a valid text");

                var aesAlg = NewRijndaelManaged(Key);

                var encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);
                var msEncrypt = new MemoryStream();
                using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                using (var swEncrypt = new StreamWriter(csEncrypt))
                {
                    swEncrypt.Write(plainString);
                }
                var s = msEncrypt.ToArray();
                encString = Convert.ToBase64String(msEncrypt.ToArray());
                return true;
            }
            catch (Exception ex)
            {
                encString = ex.Message;
                return false;
            }
        }

        public static String Decrytion_Payment_AES(String Input, string encSource, int KeySize, int BlockSize, string encKey, string IVKey)
        {
            Input = Input.Replace(" ", "+").Replace("\\", "");
            RijndaelManaged aes = new RijndaelManaged();
            aes.KeySize = KeySize;
            aes.BlockSize = BlockSize;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;
            aes.Key = Encoding.UTF8.GetBytes(encKey);
            aes.IV = Encoding.UTF8.GetBytes(IVKey);

            var decrypt = aes.CreateDecryptor();
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, decrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Convert.FromBase64String(Input);
                    cs.Write(xXml, 0, xXml.Length);
                }

                xBuff = ms.ToArray();
            }

            String Output = Encoding.UTF8.GetString(xBuff);
            return Output;
        }

        public static string Mask(this string input, int numberOfChars = 0, bool fromLeft = true, char delimiter = 'X')
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var output = input.ToArray();
            if (fromLeft)
            {
                for (int i = 0; i < numberOfChars; i++)
                    output[i] = delimiter;
            }
            else
            {
                for (int i = input.Length - 1; i >= input.Length - numberOfChars; i--)
                    output[i] = delimiter;
            }
            return new string(output);
        }

        // Method for communiation team
        public static string AESencrypt_Communication(string plainText)
        {
            byte[] key = Encoding.UTF8.GetBytes("NjU1YjIwZjg2NWE1ZDEwMDAxM2QxYTk0");
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = key;
                aesAlg.IV = Convert.FromBase64String("S0d4bUhSaXZqeXNvSG1aUQ==");
                aesAlg.Mode = CipherMode.CBC;

                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                        {
                            swEncrypt.Write(plainText);
                        }
                    }
                    return Convert.ToBase64String(msEncrypt.ToArray().ToArray());
                }
            }
        }

    }
}

