﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Amazon.SQS;
using Amazon.SQS.Model;
using Newtonsoft;
using System.Configuration;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using Amazon.DynamoDBv2.DocumentModel;
using PropertyLayers;

namespace Helper
{
    public static class AWSSQS
    {
        //static string AccessKey = ConfigurationManager.AppSettings["AccessKeyAmazonSQS"];
        //static string Secret = ConfigurationManager.AppSettings["SecretAmazonSQS"];    

        public static void SQSSendMessage(string queueUrl, string SQSMsg, Int32 DelaySeconds = 1)
        {

            using (var client = new AmazonSQSClient(Amazon.RegionEndpoint.APSouth1))
            {
                var request = new SendMessageRequest()
                {
                    QueueUrl = queueUrl,
                    MessageBody = SQSMsg,
                    DelaySeconds = DelaySeconds,
                };

                //SendMessageResponse response = client.SendMessage(request);
                var response = client.SendMessageAsync(request);
            }

        }
    }
}
