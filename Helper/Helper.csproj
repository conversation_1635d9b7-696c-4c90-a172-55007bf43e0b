<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Amazon.Sqs" Version="0.20.4" />
    <PackageReference Include="AWSSDK.DynamoDBv2" Version="3.7.0.30" />
    <PackageReference Include="AWSSDK.SecretsManager" Version="3.7.102.59" />
    <PackageReference Include="AWSSDK.SQS" Version="3.7.0.30" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="5.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="5.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="StackExchange.Redis" Version="2.6.70" />
    <PackageReference Include="System.Runtime.Caching" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PropertyLayers\PropertyLayers.csproj" />
  </ItemGroup>

</Project>
