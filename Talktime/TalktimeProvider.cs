﻿using System;
using PropertyLayers;

namespace Talktime
{
    public class TalktimeProvider
    {
        public static ITalktime getTalktimeProvider(PriorityModel reqPriorityModel, PriorityModel respPriorityModel)
        {

            if (respPriorityModel.Call == null)
            {
                if (reqPriorityModel.Call.TalkTime >= 15)  // not valid attempts NANC --valid attempt
                    return new InsertValidAnswered(reqPriorityModel, respPriorityModel);

                else if (reqPriorityModel.Call.TalkTime > 0) // valid + not valid
                    return new InsertMongoAnswered(reqPriorityModel, respPriorityModel);

                else // valid + not valid
                    return new InsertMongoUnAnswered(reqPriorityModel, respPriorityModel);

            }
            else
            {
                if (reqPriorityModel.Call.TalkTime >= 15) // not valid attempts
                    return new UpdateValidAnswered(reqPriorityModel, respPriorityModel);

                else if (reqPriorityModel.Call.TalkTime > 0) // valid + not valid
                    return new UpdateMongoAnswered(reqPriorityModel, respPriorityModel);

                else //// valid + not valid
                    return new UpdateMongoUnAnswered(reqPriorityModel, respPriorityModel);
            }
        }
    }
}

