﻿using System;
using Amazon.DynamoDBv2.Model;
using DataAccessLayer;
using DataAccessLibrary;
using DataHelper;
using MongoDB.Driver;
using MongoDB.Driver.Builders;
using PropertyLayers;

namespace Talktime
{
    public class InsertUpdateTalktimeData
    {
        public static UpdateBuilder<PriorityModel> InsertAnsweredAttempts(UpdateBuilder<PriorityModel> update, PriorityModel reqPriorityModel, PriorityModel respPriorityModel)
        {
            update = update.Set(p => p.Call.TodayAnswered, 1);
            return update;
        }

        public static UpdateBuilder<PriorityModel> UpdateAnsweredAttempts(UpdateBuilder<PriorityModel> update, PriorityModel reqPriorityModel, PriorityModel respPriorityModel)
        {
            short TodayAnswered = reqPriorityModel.Call.calltime.Date > respPriorityModel.Call.calltime.Date ? Convert.ToInt16(1) : Convert.ToInt16(respPriorityModel.Call.TodayAnswered + 1);

            update = update.Set(p => p.Call.TodayAnswered, TodayAnswered);
            return update;
        }

        public static UpdateBuilder<PriorityModel> InsertValidNANCAttempts(UpdateBuilder<PriorityModel> update, PriorityModel reqPriorityModel, PriorityModel respPriorityModel)
        {
            short currentWeek = Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(reqPriorityModel.Call.calltime.Date.Subtract(Convert.ToDateTime(respPriorityModel.User.FirstAssignedOn).Date).TotalDays) / 7) + 1);

            update = update.Set(p => p.Call.Week_Attempt, 1)
                           .Set(p => p.Call.Current_Week, currentWeek)
                           .Set(p => p.Call.TodaysNANCAttempt, 1)
                           .Set(p => p.Call.LastNminuteNANCeAttempts, 1)
                           .Set(p => p.Call.TodaysAttempt, 1);

            return update;
        }

        public static UpdateBuilder<PriorityModel> UpdateValidNANCAttempts(UpdateBuilder<PriorityModel> update, PriorityModel reqPriorityModel, PriorityModel respPriorityModel)
        {
            short currentWeek = Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(reqPriorityModel.Call.calltime.Date.Subtract(Convert.ToDateTime(respPriorityModel.User.FirstAssignedOn).Date).TotalDays) / 7) + 1);
            short TodaysAttempt = reqPriorityModel.Call.calltime.Date > respPriorityModel.Call.calltime.Date ? Convert.ToInt16(1) : Convert.ToInt16(respPriorityModel.Call.TodaysAttempt + 1);
            short Week_Attempt = currentWeek != respPriorityModel.Call.Current_Week ? Convert.ToInt16(1) : Convert.ToInt16(respPriorityModel.Call.Week_Attempt + 1);
            short TodaysNANCAttempt = reqPriorityModel.Call.calltime.Date > respPriorityModel.Call.calltime.Date ? Convert.ToInt16(1) : Convert.ToInt16(respPriorityModel.Call.TodaysNANCAttempt + 1);

            update = update.Set(p => p.Call.Current_Week, currentWeek)
                            .Set(p => p.Call.Week_Attempt, Week_Attempt)
                            .Set(p => p.Call.TodaysNANCAttempt, TodaysNANCAttempt)
                            .Set(p => p.Call.TodaysAttempt, TodaysAttempt);

            //Need to discuss
            if (reqPriorityModel.Call.calltime.Date > respPriorityModel.Call.calltime.Date)
                update = update.Set(p => p.Call.LastNminuteNANCeAttempts, 1);

            else if (respPriorityModel.Call.LastNminuteNANCeAttempts == 0)
                update = update.Set(p => p.Call.LastNminuteNANCeAttempts, 1);

            else if (reqPriorityModel.Call.LastCallTime != DateTime.MinValue && (reqPriorityModel.Call.calltime - reqPriorityModel.Call.LastCallTime).TotalMinutes < 30)
                update = update.Set(p => p.Call.LastNminuteNANCeAttempts, respPriorityModel.Call.LastNminuteNANCeAttempts + 1);

            else if (respPriorityModel.Call.LastNminuteNANCeAttempts >= 2)
                update = update.Set(p => p.Call.LastNminuteNANCeAttempts, respPriorityModel.Call.LastNminuteNANCeAttempts + 1);

            return update;
        }

        public static UpdateBuilder<PriorityModel> InsertNotValidNANCAttempts(UpdateBuilder<PriorityModel> update, PriorityModel reqPriorityModel, PriorityModel respPriorityModel)
        {
            update = update.Set(p => p.Call.TodaysNANCAttempt, 0)
                           .Set(p => p.Call.LastNminuteNANCeAttempts, 0);

            return update;
        }

        public static UpdateBuilder<PriorityModel> UpdateNotValidNANCAttempts(UpdateBuilder<PriorityModel> update, PriorityModel reqPriorityModel, PriorityModel respPriorityModel)
        {
            return update;
        }

        public static bool IsValidNANCAttempt(PriorityModel reqPriorityModel)
        {
            bool resp = false;
            string[] _dispositions = { "403", "404", "408", "500" };

            if (reqPriorityModel.Call.Duration > 2
                && (!string.IsNullOrEmpty(reqPriorityModel.Call.Disposition))
                && !(_dispositions.Contains(reqPriorityModel.Call.Disposition) || (reqPriorityModel.Call.Disposition == "503" && reqPriorityModel.Call.Duration < 10))
            )
            {
                resp = true;
            }

            return resp;
        }

    }
}

