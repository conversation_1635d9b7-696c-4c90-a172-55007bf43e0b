﻿using System;
using DataAccessLayer;
using MongoDB.Driver.Builders;
using PropertyLayers;

namespace Talktime
{
    public class UpdateValidAnswered : ITalktime
    {
        private readonly PriorityModel reqPriorityModel;
        private readonly PriorityModel respPriorityModel;

        public UpdateValidAnswered(PriorityModel _reqPriorityModel, PriorityModel _respPriorityModel)
        {
            reqPriorityModel = _reqPriorityModel;
            respPriorityModel = _respPriorityModel;
        }

        public UpdateBuilder<PriorityModel> InsertUpdateMongoCall(UpdateBuilder<PriorityModel> update, ref short ValidUnasnweredAttempt)
        {

            update = InsertUpdateTalktimeData.UpdateAnsweredAttempts(update, reqPriorityModel, respPriorityModel);
            update = InsertUpdateTalktimeData.UpdateNotValidNANCAttempts(update, reqPriorityModel, respPriorityModel);

            if (reqPriorityModel.Call.calltime.Date > respPriorityModel.Call.calltime.Date) // today's 1st call
            {
                update = update.Set(p => p.Call.TodaysAttempt, 1);
            }
            else
            {
                update = update.Set(p => p.Call.LastNminuteNANCeAttempts, 0)
                               .Set(p => p.Call.TodaysAttempt, respPriorityModel.Call.TodaysAttempt + 1);
            }

            if (reqPriorityModel.Call.TalkTime >= 240) //ok
            {
                CallData _CallData = LeadPrioritizationDLL.SetAnsWeekAttempts(respPriorityModel);
                short WeekAttempts = _CallData.Week_Attempt;

                update = update.Set(x => x.Call.Current_Week, _CallData.Current_Week)
                               .Set(p => p.Call.Week_Attempt, _CallData.Week_Attempt);

            }

            return update;
        }

    }
}

