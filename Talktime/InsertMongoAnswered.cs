﻿using System;
using MongoDB.Driver.Builders;
using PropertyLayers;

namespace Talktime;
public class InsertMongoAnswered : ITalktime
{
    private readonly PriorityModel reqPriorityModel;
    private readonly PriorityModel respPriorityModel;

    public InsertMongoAnswered(PriorityModel _reqPriorityModel, PriorityModel _respPriorityModel)
    {
        reqPriorityModel = _reqPriorityModel;
        respPriorityModel = _respPriorityModel;
    }

    public UpdateBuilder<PriorityModel> InsertUpdateMongoCall(UpdateBuilder<PriorityModel> update, ref short ValidUnasnweredAttempt)
    {
        update = InsertUpdateTalktimeData.InsertAnsweredAttempts(update, reqPriorityModel, respPriorityModel);

        //valid NANC attempt
        if (InsertUpdateTalktimeData.IsValidNANCAttempt(reqPriorityModel))
        {
            ValidUnasnweredAttempt = 1;
            update = InsertUpdateTalktimeData.InsertValidNANCAttempts(update, reqPriorityModel, respPriorityModel);

        }
        else  //invalid NANC attempt
        {
            ValidUnasnweredAttempt = 0;
        }

        return update;
    }

}

