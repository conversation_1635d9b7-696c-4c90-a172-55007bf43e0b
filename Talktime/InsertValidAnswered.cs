﻿using System;
using MongoDB.Driver.Builders;
using PropertyLayers;

namespace Talktime
{
    public class InsertValidAnswered : ITalktime
    {
        private readonly PriorityModel reqPriorityModel;
        private readonly PriorityModel respPriorityModel;

        public InsertValidAnswered(PriorityModel _reqPriorityModel, PriorityModel _respPriorityModel)
        {
            reqPriorityModel = _reqPriorityModel;
            respPriorityModel = _respPriorityModel;
        }

        public UpdateBuilder<PriorityModel> InsertUpdateMongoCall(UpdateBuilder<PriorityModel> update, ref short ValidUnasnweredAttempt)
        {
            short TodaysAttempt = reqPriorityModel.Call.calltime.Date > respPriorityModel.Call.calltime.Date ? Convert.ToInt16(0) : Convert.ToInt16(respPriorityModel.Call.TodaysAttempt + 1);
            ValidUnasnweredAttempt = 0;

            update = update.Set(p => p.Call.TodaysAttempt, 1);
            update = InsertUpdateTalktimeData.InsertAnsweredAttempts(update, reqPriorityModel, respPriorityModel);

            return update;
        }

    }
}

