﻿using System;
using MongoDB.Driver.Builders;
using PropertyLayers;

namespace Talktime
{
    public class UpdateMongoUnAnswered : ITalktime
    {
        private readonly PriorityModel reqPriorityModel;
        private readonly PriorityModel respPriorityModel;

        public UpdateMongoUnAnswered(PriorityModel _reqPriorityModel, PriorityModel _respPriorityModel)
        {
            reqPriorityModel = _reqPriorityModel;
            respPriorityModel = _respPriorityModel;
        }

        public UpdateBuilder<PriorityModel> InsertUpdateMongoCall(UpdateBuilder<PriorityModel> update, ref short ValidUnasnweredAttempt)
        {
            short TodayAnswered = reqPriorityModel.Call.calltime.Date > respPriorityModel.Call.calltime.Date ? Convert.ToInt16(0) : respPriorityModel.Call.TodayAnswered;

            update = update.Set(p => p.Call.TodayAnswered, TodayAnswered);

            //valid NANC attempt
            if (InsertUpdateTalktimeData.IsValidNANCAttempt(reqPriorityModel))
            {
                ValidUnasnweredAttempt = 1;
                update = InsertUpdateTalktimeData.UpdateValidNANCAttempts(update, reqPriorityModel, respPriorityModel);
            }
            else //invalid NANC attempt
            {
                ValidUnasnweredAttempt = 0;
            }

            return update;
        }
    }
}

