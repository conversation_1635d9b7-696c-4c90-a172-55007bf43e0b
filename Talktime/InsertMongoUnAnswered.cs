﻿using System;
using MongoDB.Driver.Builders;
using PropertyLayers;

namespace Talktime;
public class InsertMongoUnAnswered : ITalktime
{
    private readonly PriorityModel reqPriorityModel;
    private readonly PriorityModel respPriorityModel;

    public InsertMongoUnAnswered(PriorityModel _reqPriorityModel, PriorityModel _respPriorityModel)
    {
        reqPriorityModel = _reqPriorityModel;
        respPriorityModel = _respPriorityModel;
    }

    public UpdateBuilder<PriorityModel> InsertUpdateMongoCall(UpdateBuilder<PriorityModel> update, ref short ValidUnasnweredAttempt)
    {
        string[] _dispositions = { "403", "404", "408", "500" };

        short currentWeek = Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(reqPriorityModel.Call.calltime.Date.Subtract(Convert.ToDateTime(respPriorityModel.User.FirstAssignedOn).Date).TotalDays) / 7) + 1);

        update = update.Set(p => p.Call.TodayAnswered, 0);

        //valid NANC attempt
        if(InsertUpdateTalktimeData.IsValidNANCAttempt(reqPriorityModel))
        {
            ValidUnasnweredAttempt = 1;
            update = InsertUpdateTalktimeData.InsertValidNANCAttempts(update, reqPriorityModel, respPriorityModel);

        }
        else //invalid NANC attempt
        {
            ValidUnasnweredAttempt = 0;

            
        }

        return update;
    }

}

