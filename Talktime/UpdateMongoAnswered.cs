﻿using System;
using MongoDB.Driver.Builders;
using PropertyLayers;

namespace Talktime
{
    public class UpdateMongoAnswered : ITalktime
    {
        private readonly PriorityModel reqPriorityModel;
        private readonly PriorityModel respPriorityModel;

        public UpdateMongoAnswered(PriorityModel _reqPriorityModel, PriorityModel _respPriorityModel)
        {
            reqPriorityModel = _reqPriorityModel;
            respPriorityModel = _respPriorityModel;
        }

        public UpdateBuilder<PriorityModel> InsertUpdateMongoCall(UpdateBuilder<PriorityModel> update, ref short ValidUnasnweredAttempt)
        {

            update = InsertUpdateTalktimeData.UpdateAnsweredAttempts(update, reqPriorityModel, respPriorityModel);

            //valid NANC attempt
            if (InsertUpdateTalktimeData.IsValidNANCAttempt(reqPriorityModel))
            {
                ValidUnasnweredAttempt = 1;
                update = InsertUpdateTalktimeData.UpdateValidNANCAttempts(update, reqPriorityModel, respPriorityModel);
            }
            else
            {
                ValidUnasnweredAttempt = 1;
            }

            return update;
        }

    }
}

