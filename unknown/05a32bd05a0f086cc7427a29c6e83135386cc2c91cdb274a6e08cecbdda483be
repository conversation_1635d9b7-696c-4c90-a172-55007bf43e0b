﻿using System;
using System.Collections.Generic;
using DataHelper;
using System.Runtime.Caching;
using System.Data.SqlClient;
using System.Data;
using PropertyLayers;
using Newtonsoft.Json;
using Redis;
using Helper;
using System.Linq;
using MongoDB.Driver.Builders;

namespace DataAccessLibrary
{
    public class MasterData
    {
        public static List<CountryTimeZone> GetCountryTimeZone()
        {

            try
            {
                List<CountryTimeZone> lstZones;
                ObjectCache memcache = MemoryCache.Default;
                lstZones = (List<CountryTimeZone>)memcache.Get("Zones");
                if (lstZones == null)
                {
                    DateTime Requestdate = DateTime.Now;
                    //string Key = RedisCollection.TimeZone();
                  //  string obj = RedisHelper.GetRedisData(Key);
                    lstZones = new List<CountryTimeZone>();
                    CacheItemPolicy objCachePolicies = new()
                    {
                        AbsoluteExpiration =
                            new DateTimeOffset(
                                DateTime.UtcNow.AddHours(24))
                    };

                    //if (obj == null)
                    //{
                        SqlParameter[] SqlParam = Array.Empty<SqlParameter>();
                        DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GET_TimeZones]", SqlParam);

                        if (ds != null && ds.Tables.Count > 0)
                        {
                            foreach (DataRow row in ds.Tables[0].Rows)
                            {
                                CountryTimeZone zone = new()
                                {
                                    Country = row["Country"].ToString(),
                                    CountryId = (row["CountryId"] != DBNull.Value && row["CountryId"] != null) ? Convert.ToInt16(row["CountryId"]) : (short)0,
                                    StartTime = TimeSpan.Parse(row["StartTime"].ToString()),
                                    EndTime = TimeSpan.Parse(row["EndTime"].ToString())
                                };
                                lstZones.Add(zone);
                            }

                        }
                        ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GET_AreaCodeTimeZones]", SqlParam);
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            foreach (DataRow row in ds.Tables[0].Rows)
                            {
                                CountryTimeZone zone = new()
                                {
                                    Country = row["Country"].ToString(),
                                    CountryId = (row["CountryId"] != DBNull.Value && row["CountryId"] != null) ? Convert.ToInt16(row["CountryId"]) : (short)0,
                                    StartTime = TimeSpan.Parse(row["StartTime"].ToString()),
                                    EndTime = TimeSpan.Parse(row["EndTime"].ToString()),
                                    AreaCode = Convert.ToInt16(row["AreaCode"].ToString())
                                };
                                lstZones.Add(zone);
                            }

                        }
                        //Add into cache start
                        memcache.Add("Zones", lstZones, objCachePolicies);
                       // RedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(lstZones), new TimeSpan(7, 0, 0, 0));
                        //Add into cache end
                        return lstZones;
                   // }
                    //else
                    //{
                    //    lstZones = JsonConvert.DeserializeObject<List<CountryTimeZone>>(obj);
                    //    memcache.Add("Zones", lstZones, objCachePolicies);
                    //    return lstZones;
                    //}
                }
                else
                    return lstZones;
            }
            catch (Exception)
            {
                throw;
            }
        }
        public static Dictionary<string,string> EmployeeMaster()
        {
            try
            {
                if (MemoryCache.Default["EmployeeMaster"] != null)
                    return (Dictionary<string, string>)MemoryCache.Default["EmployeeMaster"];
                
                string strQuery = "SELECT UserID,EmployeeId,UserName FROM CRM.UserDetails (nolock) WHERE ISNULL(EmployeeId,'') <> '' AND IsActive = 1";
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.Text, strQuery);
                Dictionary<string, string> _AgentMasterData = new Dictionary<string, string>();
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    if (!_AgentMasterData.ContainsKey(Convert.ToString(dr["EmployeeId"])))
                        _AgentMasterData.Add(Convert.ToString(dr["EmployeeId"]), Convert.ToString(dr["UserID"]) + "-" + Convert.ToString(dr["UserName"]));
                }

                //var _AgentMasterData = (from row in ds.Tables[0].AsEnumerable()
                                        //select new Dictionary<string, Int64>
                                        //{
                                          //  EmployeeId = row.Field<string>("EmployeeId"),
                                            //UserId = Convert.ToInt64(row["UserID"])
                                        //});

                CommonCache.GetOrInsertIntoCache(_AgentMasterData, "EmployeeMaster", 24 * 60);

                return _AgentMasterData;
            }
            catch(Exception ex)
            {
                return new Dictionary<string, string>();
            }
        }
        public static List<ExpiryPriorityLogic> getExpiryPriorityLogicMaster()
        {

            try
            {
                List<ExpiryPriorityLogic> lstExpiryPriorityLogicMaster;
                ObjectCache memcache = MemoryCache.Default;
                lstExpiryPriorityLogicMaster = (List<ExpiryPriorityLogic>)memcache.Get("ExpiryPriorityLogic");
                if (lstExpiryPriorityLogicMaster == null)
                {
                    DateTime Requestdate = DateTime.Now;
                    lstExpiryPriorityLogicMaster = new List<ExpiryPriorityLogic>();
                    CacheItemPolicy objCachePolicies = new()
                    {
                        AbsoluteExpiration =
                            new DateTimeOffset(
                                DateTime.UtcNow.AddHours(24))
                    };

                    
                    SqlParameter[] SqlParam = Array.Empty<SqlParameter>();
                    DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetExpiryPriorityLogicMaster]", SqlParam);

                    if (ds != null && ds.Tables.Count > 0)
                    {
                        foreach (DataRow row in ds.Tables[0].Rows)
                        {
                            ExpiryPriorityLogic expprioritymaster = new()
                            {
                                MinExpDays = Convert.ToInt16(row["MinExpDays"]),
                                MaxExpDays = Convert.ToInt16(row["MaxExpDays"]),
                                Attempts = Convert.ToInt16(row["Attempts"]),
                                CallingGapDays = Convert.ToInt16(row["CallingGapDays"]),
                                GroupID = Convert.ToInt16(row["GroupID"]),
                                IsNRI = Convert.ToBoolean(row["IsNRI"]),
                                RYStart = Convert.ToInt16(row["RYStart"]),
                                RYEnd = Convert.ToInt16(row["RYEnd"]),
                                Priority = Convert.ToInt16(row["Priority"]),
                                MinTalkTime = Convert.ToInt32(row["MinTalkTime"]),
                                MaxTalkTime = Convert.ToInt32(row["MaxTalkTime"]),
                                Process = Convert.ToString(row["Process"]),
                                FilterDate = Convert.ToString(row["FilterDate"])

                            };
                            lstExpiryPriorityLogicMaster.Add(expprioritymaster);
                        }

                    }                    
                    //Add into cache start
                    memcache.Add("ExpiryPriorityLogic", lstExpiryPriorityLogicMaster, objCachePolicies);                    
                    return lstExpiryPriorityLogicMaster;                                        
                }
                else
                    return lstExpiryPriorityLogicMaster;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public static Dictionary<Int32, string> CountryMaster()
        {
            try
            {
                if (MemoryCache.Default["CountryMaster"] != null)
                    return (Dictionary<Int32, string>)MemoryCache.Default["CountryMaster"];

                SqlParameter[] SqlParam = Array.Empty<SqlParameter>();
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCountryList_CV]", SqlParam);
                Dictionary<Int32, string> _CountryMasterData = new Dictionary<Int32, string>();
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    if (!_CountryMasterData.ContainsKey(Convert.ToInt32(dr["CountryID"])))
                        _CountryMasterData.Add(Convert.ToInt32(dr["CountryID"]), Convert.ToString(dr["Country"]));
                }

                CommonCache.GetOrInsertIntoCache(_CountryMasterData, "CountryMaster", 24 * 60);

                return _CountryMasterData;
            }
            catch (Exception ex)
            {
                return new Dictionary<Int32, string>();
            }
        }


        public static Dictionary<string, NriCityTimeZone> GetCityTimeZone()
        {
            try
            {
                Dictionary<string, NriCityTimeZone> lstCityZones;
                ObjectCache memcache = MemoryCache.Default;
                lstCityZones = (Dictionary<string, NriCityTimeZone>)memcache.Get("NriCityZones");

                if (lstCityZones == null)
                {
                    DateTime Requestdate = DateTime.Now;
                    lstCityZones = new();
                    CacheItemPolicy objCachePolicies = new CacheItemPolicy()
                    {
                        AbsoluteExpiration =
                            new DateTimeOffset(
                                DateTime.UtcNow.AddHours(24))
                    };

                    SqlParameter[] SqlParam = Array.Empty<SqlParameter>();
                    DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetNriCityTimeZones]", SqlParam);

                    if (ds != null && ds.Tables.Count > 0)
                    {
                        foreach (DataRow row in ds.Tables[0].Rows)
                        {
                            NriCityTimeZone zone = new();
                            zone.NriCity = (row["NriCity"] != DBNull.Value && row["NriCity"] != null) ? row["NriCity"].ToString() : string.Empty;
                            zone.StartTime = TimeSpan.Parse(row["StartTime"].ToString());
                            zone.EndTime = TimeSpan.Parse(row["EndTime"].ToString());
                            zone.TimeZone = (row["TimeZone"] != DBNull.Value && row["TimeZone"] != null) ? row["TimeZone"].ToString() : string.Empty;
                            zone.TimeDiffFromIST = (row["TimeDiffFromIST"] != DBNull.Value && row["TimeDiffFromIST"] != null) ? Convert.ToString(row["TimeDiffFromIST"]) : "+00:00";
                            lstCityZones.TryAdd(zone.NriCity, zone);
                        }

                    }

                    //Add into cache start
                    memcache.Add("NriCityZones", lstCityZones, objCachePolicies);

                    //Add into cache end
                    return lstCityZones;
                }
                else
                    return lstCityZones;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static List<UnassistedPilotLead> getUnassistedPilotLeads()
        {
            try
            {
                List<UnassistedPilotLead> lstUnassistedPilotLeads;
                ObjectCache memcache = MemoryCache.Default;
                lstUnassistedPilotLeads = (List<UnassistedPilotLead>)memcache.Get("HealthRenewalUnassistedPilotLeads");
                
                if (lstUnassistedPilotLeads == null)
                {
                    DateTime Requestdate = DateTime.Now;
                    lstUnassistedPilotLeads = new List<UnassistedPilotLead>();
                    CacheItemPolicy objCachePolicies = new()
                    {
                        AbsoluteExpiration = new DateTimeOffset(DateTime.UtcNow.AddHours(24))
                    };

                    MongoHelper OneLeadDb = new(SingletonClass.OneLeadDB());
                  
                    lstUnassistedPilotLeads = OneLeadDb.GetDocuments<UnassistedPilotLead>(
                        null,
                        MongoCollection.HealthRenewalUnassistedPilotLeads()
                    ).ToList();


                    if (lstUnassistedPilotLeads.Count > 0)
                    {
                        memcache.Add(
                            "HealthRenewalUnassistedPilotLeads",
                            lstUnassistedPilotLeads,
                            objCachePolicies
                        );
                    }
                }

                return lstUnassistedPilotLeads;
            }
            catch (Exception)
            {
                return new List<UnassistedPilotLead>();
            }
        }

    }
}
