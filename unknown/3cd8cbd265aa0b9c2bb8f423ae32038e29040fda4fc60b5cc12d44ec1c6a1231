﻿using System;
using CommunicationBLL;
using Helper;
using Microsoft.AspNetCore.Mvc;
using PropertyLayers;
using ReadXmlProject;

namespace CommAPI.Controllers
{
    [CommAuth]
    [ApiController]
    [Route("onelead/api/[controller]/[action]")]
    public class CommunicationController : ControllerBase
    {
        private readonly ICommunicationBLL objCommBLL;

        public CommunicationController(ICommunicationBLL communicationBLL)
        {
            objCommBLL = communicationBLL;
        }

        [HttpGet("{LeadID}/{userId}/{customerId}")]
        public EasyDialSoftPhoneResponse ConnectCallSF(string LeadID, string userId, string customerId, [FromQuery] CallRequest callRequest)
        {
            userId = Request != null ? Request.Headers["AgentId"] : string.Empty;
            string source = Request != null ? Request.Headers["source"] : string.Empty;

            CallRequestObj call = new()
            {
                LeadID = Convert.ToInt64(LeadID),
                userId = CoreCommonMethods.IsValidString(userId) ? Convert.ToInt64(userId) : 0,
                customerId = Convert.ToInt64(customerId),
                redial = callRequest.redial,
                PriorityReasonId = callRequest.PriorityReasonId,
                apiCallTime = callRequest.apiCallTime,
                attempt = callRequest.attempt,
                roleId = callRequest.roleId,
                source = source == "fosapp" ? "fosapp" : callRequest.source,
                CallOnAswat = callRequest.callOnAswat != null && (bool)callRequest.callOnAswat,
                EncryptedMobileNo = callRequest.EncryptedMobileNo,
                CountryId = callRequest.CountryId,
                CountryCode = callRequest.CountryCode,
                AsteriskIP = callRequest.AsteriskIP,
                VirtualNumber = callRequest.VirtualNumber,
            };

            call.source = string.IsNullOrEmpty(call.VirtualNumber) ? call.source : source;
            var requestHost = HttpContext.Request?.Headers["Referer"].ToString(); //HttpContext.Request?.Host.ToString();
            List<string> UAEDomainList = "UAEdomain".AppSettings().Split(',').ToList();
            bool isUAE = !string.IsNullOrEmpty(requestHost) && UAEDomainList.Contains(requestHost.ToLower());

            return objCommBLL.PrepareCallData(call, isUAE);
        }

        [HttpGet]
        public bool updatecallstatus([FromQuery] CallStatus callStatus)
        {
            return objCommBLL.UpdateCallStatus(callStatus);
        }

        [HttpGet("{CustomerId}/{ProductId}/{LeadId}")]
        public List<CallingDataFields> getCallingNoData(string CustomerId, string ProductId, string LeadId)
        {
            List<CallingDataFields>? oCallingDataFields = null;

            if (CoreCommonMethods.IsValidInteger(CustomerId) > 0 && CoreCommonMethods.IsValidInteger(ProductId) > 0 && CoreCommonMethods.IsValidInteger(LeadId) > 0)
                oCallingDataFields = objCommBLL.getCallingNoData(CustomerId, ProductId, LeadId);

            return oCallingDataFields;
        }
        [HttpGet("{CustId}/{Type}/{LeadId}")]
        public string getCustContactInfo(string CustId, string Type, string LeadId)
        {
            string response = objCommBLL.getMaskedCustContactInfo(CustId, Type, LeadId);
            return response;
        }
        [HttpGet("{AgentId}")]
        public string AswatIndiaLogin(string AgentId)
        {           
            return objCommBLL.AswatIndiaLogin(AgentId);
        }
        [HttpGet("{AgentId}/{status}")]
        public string AswatIndiaStatus(string AgentId, string status)
        {            
            return objCommBLL.AswatIndiaStatus(AgentId, status);
        }
        [HttpGet("{AgentId}")]
        public string AswatIndiaLogout(string AgentId)
        {            
            return objCommBLL.AswatIndiaLogout(AgentId);
        }

    }
}

