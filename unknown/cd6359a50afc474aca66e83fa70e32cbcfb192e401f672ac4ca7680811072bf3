﻿using Google.Apis.Sheets.v4.Data;
using PropertyLayers;

namespace CommunicationBLL
{
    public interface ILeadPrioritizationBLL
	{
        public PriorityModel? GetOneLead(long UserId, short ProductId, string Source);
        public List<PriorityModel>? GetAgentAllLeads(long UserId, short ProductId, bool showCallableLeads, string source);
        public bool MarkRetentionLeads(List<PriorityModel> leads);
        public bool PushCallDatainQueue(string CallID, string LeadID, string AgentID, string Duration, string talkTime, string status, string callDate, string callType, string Context, string Disposition, string uid, string dst, string channel, string action, string hanguptime, string IP, string callRequestType, string IsBMS, string recfile, string t_type, string CallingNo, string rectype);
        public string GetDNCDetails(Int64 LeadId);
        public AddLeadValidation ValidateAddLeadToPriorityQueue(UserNext5Leads priorityLead);
        public int GetReleaseLeadsCount(Int64 UserID);
        public bool INSERTLeadReopenTrack(long LeadId, int SubStatusID, Int64 UserID);
        public void ReleaseRequest(ReleaseLeadRequest _ReleaseLeadRequest);
        public UnAnsweredSummary GetUnAnsweredSummary(string LeadID, string ProductID);
        public ResponseAPI IsCallBackAllowed(string LeadId, string CBTime);
        public List<NotContactedLeads> GetNotContactedLeads(string UserId, string LastDays);
        public bool SkipCustomer(SkipCustomerRequest skipCustomerRequest);
        public bool PushDncData(PriorityModel oPriorityModel);
        public ResponseAPI CallRelease(string LeadId, string UserID, string EmployeeID);
        public List<ReleasePointData> GetReleaseLeads(string UserID, string productID, string RoleID);
        public List<PriorityModel> GetUserAssignedLeads(Int64 UserID);
        public List<PriorityModel> GetStarLeads(Int64 UserId);
        public List<PriorityModel> GetExpiringLeads(Int64 UserId, byte productID);
        public List<Int64> CheckLeadTimeZone(List<Int64> LeadList);
        public long GetTLCallingNo(string UserId);
        List<PriorityModel> GetLastCalledLeads(Int64 UserId, Int16 productID);
        ResponseAPI StarLead(Int64 UserId, Int16 productID, Int64 LeadID, byte Star);
        bool SkipLead(long leadId, Int16 SkipDuration, Int64 Userid);
        public int SetRealTimeLeadStatus(LeadCallDetailsDTO _LeadCallDetailsDTO);
        public Boolean updateagentstatus(PredictiveAgentStatus obj, bool onCall = false);
        bool RemoveInvalidLeadsFromNext5Leads(RemoveNext5Leads obj);
        List<RemoveNext5Leads> GetInvalidLeadsFromNext5Leads(RemoveNext5Leads obj);
        bool ProcessLeadByCategory(RenewalIntentRequest request);
    }
}

