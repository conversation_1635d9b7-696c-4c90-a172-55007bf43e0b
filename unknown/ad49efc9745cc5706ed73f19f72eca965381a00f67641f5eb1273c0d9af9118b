﻿using MongoDB.Driver;
using System.Runtime.Caching;
using ReadXmlProject;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Helper;
using DataAccessLibrary;
using PropertyLayers;
using Newtonsoft.Json;
using MongoDB.Driver.Builders;
using System.Text;
using Amazon.Auth.AccessControlPolicy;
using MongoDB.Driver.Linq;

namespace CommAPI
{
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = false, Inherited = true)]
    public class CommAuth : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext actionContext)
        {
            var context = actionContext.HttpContext;
            string? Source = context.Request != null ? context.Request.Headers["source"] : string.Empty;

            if (IsValidRequest(context.Request, context))
            {
                if (CoreCommonMethods.IsValidString(Source) && !IsAuthorize(context, context.Request, Source))
                {
                    actionContext.HttpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    actionContext.Result = new RedirectToRouteResult(new RouteValueDictionary(new { controller = "Index", action = "NonAccess" }));
                }
                //base.OnActionExecuting(actionContext);
            }
            else
            {
                actionContext.HttpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                actionContext.Result = new RedirectToRouteResult(new RouteValueDictionary(new { controller = "Index", action = "NonAuthorised" }));
            }
        }

        public static bool IsValidRequest(HttpRequest request, HttpContext context)
        {
            bool Result = false;
            try
            {
                var endpoint = context.GetEndpoint() as RouteEndpoint;
                string? Domain = request != null ? Convert.ToString(request.Host): string.Empty;
                string? Source = request != null ? request.Headers["source"] : string.Empty;
                string? token = request != null ? request.Headers["Token"] : string.Empty;
                List<string> InvalidTokens = new() { "123456", "123456789" };

                List<string> SourceArr = "SourceArr".AppSettings().Split(',').ToList();
                
                ////////-----------------------  for Customer public access -------------------------//////////////
                if (CoreCommonMethods.IsValidString(Source) && !string.IsNullOrEmpty(Source) && SourceArr.Contains(Source.ToLower()))
                {
                    string? EncryptLeadId = request != null ? request.Headers["EncryptLeadId"] : string.Empty;
                    if (CoreCommonMethods.IsValidString(EncryptLeadId))
                        Result = MongoDLL.IsValidateCustomer(EncryptLeadId, token);


                }
                ////////-----------------------  for public access -------------------------////////////////////
                else if (!string.IsNullOrEmpty(Domain) && Domain.ToUpper().Contains("MATRIXCOREAPI"))
                {
                    string? agentId = request!= null ? request.Headers["AgentId"] : string.Empty;

                    var reqCookies = request != null ? request.Cookies["MatrixToken"] : string.Empty;
                    string _cookies = CoreCommonMethods.ReadCookies(reqCookies);

                    if (!string.IsNullOrEmpty(_cookies) && CoreCommonMethods.IsValidString(_cookies))
                    {
                        User? user = !string.IsNullOrEmpty(_cookies) ? JsonConvert.DeserializeObject<User>(_cookies) : null;
                        agentId = user != null ? user.UserId : string.Empty;
                        token = user != null ? user.AsteriskToken : string.Empty;

                        //if (request != null && string.IsNullOrEmpty(request.Headers["AgentId"]))
                        //    request.Headers.Add("AgentId", agentId);
                        if (request != null)
                        {
                            request.Headers.Remove("AgentId");
                            request.Headers.Add("AgentId", agentId);
                        }

                    }

                    if (CoreCommonMethods.IsValidString(agentId) && CoreCommonMethods.IsValidString(token)
                        && !string.IsNullOrEmpty(token) && !string.IsNullOrEmpty(agentId) && !InvalidTokens.Contains(token))
                    {
                        string Key = $"{RedisCollection.PredictiveAgent()}:{agentId.Trim()}";
                        string FOSKey = $"{RedisCollection.AppKey()}:{agentId.Trim()}";

                        string? CacheToken = GetCacheToken(Key);

                        if (CoreCommonMethods.IsValidString(CacheToken) && !string.IsNullOrEmpty(CacheToken) && CacheToken.Trim() == token.Trim())
                            Result = true;
                        else
                        {
                            string MatrixToken = PredictiveAgentStatusRedis.GetMatrixToken(Convert.ToString(agentId));

                            /*PredictiveAgentStatus objPredictiveAgentDetails = PredictiveAgentStatusRedis.GetAgentDetails(Convert.ToString(agentId));
                            if (objPredictiveAgentDetails != null && objPredictiveAgentDetails.AsteriskToken == token && objPredictiveAgentDetails.AsteriskToken != "123456" && objPredictiveAgentDetails.AsteriskToken != "123456789")
                            {
                                Result = true;
                                CommonCache.GetOrInsertIntoCache(objPredictiveAgentDetails.AsteriskToken, Key, 8 * 60);
                            }*/
                            if(CoreCommonMethods.IsValidString(MatrixToken) && MatrixToken == token)
                            {
                                Result = true;
                                CommonCache.GetOrInsertIntoCache(MatrixToken, Key, 8 * 60);
                            }
                            else if (!Result)
                            {
                                string AppToken = PredictiveAgentStatusRedis.GetAppToken(Convert.ToString(agentId));
                                if (CoreCommonMethods.IsValidString(AppToken) && AppToken == token)
                                {
                                    Result = true;
                                    CommonCache.GetOrInsertIntoCache(AppToken, Key, 8 * 60);
                                }
                            }
                        }
                    }
                }
                ////////-----------------------  for Internal access -------------------------////////////////////
                else
                {

                    string? clientKey = request != null ? request.Headers["clientKey"] : string.Empty;
                    string? AuthKey = request != null ? request.Headers["authKey"] : string.Empty;

                    if (CoreCommonMethods.IsValidString(AuthKey) && CoreCommonMethods.IsValidString(Source) && CoreCommonMethods.IsValidString(clientKey))
                    {
                        string Key = $"{RedisCollection.MongoConfig()}";
                        Dictionary<string, SysConfigData> _dictCollection = GetMongoCacheData(Key);


                        if (_dictCollection == null || _dictCollection.Count == 0)
                        {
                            _dictCollection = GetDictCollection();
                            CommonCache.GetOrInsertIntoCache(_dictCollection, Key, 12 * 60);
                        }

                        if (_dictCollection != null && _dictCollection.Count > 0)
                        {
                            SysConfigData SourceCollection = GetValidSource(_dictCollection, Source, Key);

                            if (SourceCollection != null)
                                Result = IsMatchData(SourceCollection, Source, AuthKey, clientKey); 

                        }

                    }
                }
            }
            catch (Exception ex)
            {
                Result = false;
                Console.WriteLine("Exception in IsValidRequest." + ex.ToString());
            }
            return Result;
        }

        public static string? GetCacheToken(string Key)
        {
            string? token = string.Empty;
            try
            {
                if (MemoryCache.Default[Key] is var memKey && memKey != null)
                {
                    token = Convert.ToString(memKey);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in getCacheDatain Commauth." + ex.ToString());
            }

            return token;
        }

        public static Dictionary<string, SysConfigData> GetMongoCacheData(string Key)
        {
            Dictionary<string, SysConfigData> obj = new();
            try
            {
                if (MemoryCache.Default[Key] != null)
                {
                    ObjectCache CacheConfig = MemoryCache.Default;
                    obj = (Dictionary<string, SysConfigData>)CacheConfig.Get(Key);

                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetMongoCacheData Commauth." + ex.ToString());
            }
            return obj;
        }

        public static List<Dictionary<string, SysConfigData>> MakeDictCollection(List<SysConfigData> objMongo)
        {

            List<Dictionary<string, SysConfigData>> obj = (from r in objMongo.AsEnumerable().Where(x => x.source != null)
                                                           select new Dictionary<string, SysConfigData>() { { r.source, r } }).ToList();

            return obj;
        }

        public static Dictionary<string, SysConfigData> GetDictCollection()
        {
            
            Dictionary<string, SysConfigData> oMongoData = MongoDLL.GetConfiValueFromMongo();
            return oMongoData;
        }

        public static SysConfigData GetValidSource(Dictionary<string, SysConfigData> _dictCollection, string Source, string Key)
        {
            SysConfigData SourceCollection = null;
            try
            {
                //----------------if source not exist thn callmongo otherwise matchcondition-----------//
                if (_dictCollection.ContainsKey(Source.ToLower()))
                {
                    SourceCollection = _dictCollection[Source.ToLower()];
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetValidSource Commauth." + ex.ToString());
            }
            return SourceCollection;
        }

        public static bool IsMatchData(SysConfigData SourceCollection, string Source, string AuthKey, string clientKey)
        {
            bool res = false;
            try
            {
                if (SourceCollection != null && SourceCollection.source.ToLower() == Source.ToLower() && SourceCollection.authKey == AuthKey && SourceCollection.clientKey == clientKey)
                    res = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in IsMatchData." + ex.ToString());
            }
            return res;
        }

        public bool IsAuthorize(HttpContext context, HttpRequest request, string Source)
        {

            bool IsValidRequest = false;
            StringBuilder sb = new();
            try
            {
                var endpoint = context.GetEndpoint() as RouteEndpoint;
                var CurrentActionMethod = endpoint?.RoutePattern?.RequiredValues?.Values?.FirstOrDefault();

                Dictionary<string, string>? SourceCollection = null;
                string? MethodURL = request != null ? request.Path.Value : string.Empty;
                string Key = $"{RedisCollection.ACLMongoConfig()}";
                string? Origin = request != null ? request.Headers["Origin"].FirstOrDefault() : string.Empty;

                sb.Append("MethodURL " + JsonConvert.SerializeObject(MethodURL) + "\r\n");
                sb.Append(" Source " + Source + "\r\n");
                sb.Append(" Origin " + Origin + "\r\n");

                if (MemoryCache.Default[Key] != null)
                {
                    sb.Append(" ,Enter in cache " + "\r\n");
                    ObjectCache CacheConfig = MemoryCache.Default;
                    SourceCollection = (Dictionary<string, string>)CacheConfig.Get(Key);
                }
                else
                    SourceCollection = getACLDictCollection(Key);

                //----- Valid method found or not
                IsValidRequest = IsValidACLRequest(SourceCollection, Source, CurrentActionMethod.ToString());

                if (!IsValidRequest)//logging
                    LoggingHelper.LoggingHelper.AddloginQueue("", 0, "", "IsAuthorize_OneLead", "OneLeadPriority", "CommAuth", JsonConvert.SerializeObject(sb), "", DateTime.Now, DateTime.Now);

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 1, ex.ToString(), "IsAuthorize_OneLead", "OneLeadPriority", "CommAuth", JsonConvert.SerializeObject(sb), "", DateTime.Now, DateTime.Now);
            }
            return IsValidRequest;

        }

        public bool IsValidACLRequest(Dictionary<string, string> SourceCollection, string Source, string MethodURL)
        {
            bool res = false;

            string KeyToSearch = MethodURL.ToLower() + "_" + Source.ToLower();
            if (SourceCollection.ContainsKey(KeyToSearch))
            {
                res = true;
            }
            return res;
        }
        public Dictionary<string, string> getACLDictCollection(string Key)
        {
            List<ACLConfigData> oMongoData = null;
            IMongoQuery varquery = Query.And(Query.EQ("isActive", true),
                                    Query.EQ("application", "onelead")
                                    );
            IMongoFields Field = Fields.Include("source", "method", "isActive");
            oMongoData = MongoDLL.GetACLMongoConfig(Field, varquery);

            Dictionary<string, string> dictionary = oMongoData.
                GroupBy(data => $"{data.method.ToLower()}_{data.source.ToLower()}").
                ToDictionary(group => group.Key, group => group.First().method);

            if (dictionary.Any())
                CommonCache.GetOrInsertIntoCache(dictionary, Key, 12 * 60);
            return dictionary;
        }

    }
}

