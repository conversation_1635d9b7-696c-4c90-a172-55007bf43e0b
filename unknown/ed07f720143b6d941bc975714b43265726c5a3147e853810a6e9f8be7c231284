﻿using System;
using DataAccessLibrary;
using DataHelper;
using System.Data;
using System.Data.SqlClient;
using MongoDB.Driver;
using MongoDB.Driver.Builders;
using PropertyLayers;
using System.Collections.Generic;
using System.Linq;
using ReadXmlProject;
using Helper;
using Newtonsoft.Json;
using System.Runtime.Caching;
using System.Text;


namespace DataAccessLayer
{
    public class DialerDLL
    {
        public static DataSet GetAgentDetails(string UserId)
        {
            DateTime reqTime = DateTime.Now;
            string connection = ConnectionClass.LivesqlConnection();
            SqlParameter[] _Param = new SqlParameter[1];
            _Param[0] = new SqlParameter("@AgentCode", UserId);
            return SqlHelper.ExecuteDataset(connection, CommandType.StoredProcedure, "MTX.GetAgentDetails", _Param);
        }

        public static string[] getLogoutReasons(string Collection)
        {
            string[] sysConfigs = { };
            StringBuilder sb = new();
            var list = new List<string>();


            try
            {
                if (MemoryCache.Default[Collection] != null)
                    sysConfigs = (string[])MemoryCache.Default.Get(Collection);

                else
                {
                    DateTime reqTime = DateTime.Now;
                    string connection = ConnectionClass.ReplicasqlConnection();

                    SqlParameter[] _Param = new SqlParameter[1];
                    _Param[0] = new SqlParameter("@MasterTypeId", 1);
                    DataSet oDataSet = SqlHelper.ExecuteDataset(connection, CommandType.StoredProcedure, "[MTX].[GetMasterTypeList]", _Param);

                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        foreach (DataRow row in oDataSet.Tables[0].Rows)
                        {
                            string val = row["Name"] == DBNull.Value ? string.Empty : Convert.ToString(row["Name"]).ToUpper();
                            list.Add(val.ToString());
                        }
                        sysConfigs = list.ToArray();
                        if (list.Count > 0)
                            CommonCache.GetOrInsertIntoCache(list.ToArray(), Collection, 8 * 60);
                    }
                }
            }
            catch (Exception)
            {
                return null;
            }
            return sysConfigs;
        }
        public static string DataTableToJSONWithJSONNet(DataTable table)
        {
            string JSONString = string.Empty;
            JSONString = JsonConvert.SerializeObject(table);
            return JSONString;
        }

        public static DataSet GetLeadBasisInfo(Int64 LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetProductIdByLeadId]", 3000, sqlParam);
        }

        public static bool UpdateCustomerConnect(Int64 uid, Int64 CallingNo)
        {
            string query = string.Empty;
            string connection = ConnectionClass.LivesqlConnection();

            SqlParameter[] sqlparm = new SqlParameter[2];
            sqlparm[0] = new SqlParameter("@CallDataID", uid);
            sqlparm[1] = new SqlParameter("@CallingNumber", CallingNo);

            var result = SqlHelper.ExecuteNonQuery(connection, CommandType.StoredProcedure, "[MTX].[UpdateCustConnect_CDH]", sqlparm);

            return true;
        }

        public static void UpdateTotalTTFlagInMongo(Int64 LeadID)
        {
            if (LeadID > 0)
            {
                try
                {
                    UpdateBuilder<PriorityModel> update = null;
                    MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                    IMongoQuery query = Query<PriorityModel>.EQ(p => p.LeadID, LeadID);
                    IMongoFields fields = Fields<PriorityModel>.Include(p => p.Call);
                    var _priorityModel = objCommDB.FindOneDocument<PriorityModel>(query, DataAccessLibrary.MongoCollection.LPDataCollection(), fields);

                    if (_priorityModel != null && _priorityModel.Call != null && _priorityModel.Call.TotalTT == 0)
                    {
                        update = Update<PriorityModel>.Set(p => p.Call.TotalTT, 1);
                        objCommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.LPDataCollection());
                    }
                }
                catch (Exception ex)
                {
                    LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, ex.ToString(), "UpdateTotalTTFlagInMongo", "UpdateTotalTTFlagInMongo", "LeadPrioritizationDLL", "", "", DateTime.Now, DateTime.Now);
                }
            }
        }

        public static DataSet GetCallDataInfo(Int64 uid)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@CallDataID", uid);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCallDataInfo]", 3000, sqlParam);
        }

        public static void UpdateAsteriskIpForGroup(short groupId, string asteriskIp, string asteriskUrl)
        {
            try
            {

                string connection = ConnectionClass.LivesqlConnection();

                SqlParameter[] sqlparm = new SqlParameter[3];
                sqlparm[0] = new SqlParameter("@GroupId", groupId);
                sqlparm[1] = new SqlParameter("@Asterisk_IP", asteriskIp);
                sqlparm[2] = new SqlParameter("@Asterisk_Url", asteriskUrl);


                var result = SqlHelper.ExecuteNonQuery(connection, CommandType.StoredProcedure, "[MTX].[UpdateAsteriskDetailsInGrp]", sqlparm);
            }
            catch { }
        }

        public static DataSet GetAgentsByQueue(string Queue)
        {
            string connection = ConnectionClass.ReplicasqlConnection();

            SqlParameter[] sqlparm = new SqlParameter[1];
            sqlparm[0] = new SqlParameter("@Queue", Queue);

            return SqlHelper.ExecuteDataset(connection, CommandType.StoredProcedure, "[MTX].[GetAgentsByQueue]", sqlparm);
        }

        public static bool UpdateLoginDetails(LogInDTO objLogin)
        {
            bool res = false;
            try
            {
                var sqlParam = new SqlParameter[7];
                sqlParam[0] = new SqlParameter("@UserId", objLogin.UserId);
                sqlParam[1] = new SqlParameter("@IsActive", objLogin.IsActive);
                sqlParam[2] = new SqlParameter("@LogOutBy", objLogin.LogOutBy);
                sqlParam[3] = new SqlParameter("@LogOutType", objLogin.LogOutType);
                sqlParam[4] = new SqlParameter("@Url", objLogin.URL);
                sqlParam[5] = new SqlParameter("@IpAddress", objLogin.IPAddress);
                sqlParam[6] = new SqlParameter("@LogoutButtonId", objLogin.LoginButtonId);

                SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[CRM].[Update_LoginDetails]", sqlParam);
                res = true;
            }
            catch
            {
                res = false;
            }
            return res;
        }


    }
}

