using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using MongoDB.Bson.Serialization.Attributes;

namespace PropertyLayers
{
    [DataContract]
    [Serializable]
    public class RenewalIntentRequest
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }

        //[DataMember(EmitDefaultValue = false)]
        //public string Category { get; set; }  // "Issue", "PreferableTime", "NearToExpiry", "NoAction", "WhatsappEmail"

        [DataMember(EmitDefaultValue = false)]
        public long CallDataID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Dictionary<string, string> Data { get; set; }
    }

    public enum RenewalBucketCategory
    {
        Issue = 1,              // Cx raise claim related issue to advisor on call
        PreferableTime = 2,     // Cx asked to call on preferable time
        CallNearToExpiry = 3,       // Cx asked to call near expiry
        NoAction = 4,           // Existing calling frequency continue
        WhatsappEmail = 5       // Whatsapp/Email - Existing calling frequency continue
    }
}