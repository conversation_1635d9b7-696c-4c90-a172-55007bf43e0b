﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Builders;
//using ReadXmlProject;
using Helper;
using Microsoft.Extensions.Configuration;

namespace DataAccessLibrary
{
    public class SingletonClass
    {        
        static MongoDatabase _OneLeadDB;
        static MongoDatabase _ResetOneLeadDB;
        private SingletonClass()
        {

        }        

        public static MongoDatabase OneLeadDB()
        {
            string Enviornment = string.Empty;
            try
            {
                if (_OneLeadDB != null)
                {
                    return _OneLeadDB;
                }
                else
                {

                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string OneLeadDB = con.GetSection("Communication").GetSection("OneLeadDB").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("OneLeadDBConnection").GetSection(Enviornment).Value.ToString();

                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    _OneLeadDB= server.GetDatabase(OneLeadDB);
                    return _OneLeadDB;

                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        public static MongoDatabase ResetOneLeadDB()
        {
            string Enviornment = string.Empty;
            try
            {
                if (_ResetOneLeadDB != null)
                {
                    return _ResetOneLeadDB;
                }
                else
                {

                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string OneLeadDB = con.GetSection("Communication").GetSection("OneLeadDB").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("ResetDBConnection").GetSection(Enviornment).Value.ToString();

                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    _ResetOneLeadDB= server.GetDatabase(OneLeadDB);
                    return _ResetOneLeadDB;

                }
            }
            catch (Exception)
            {
                throw;
            }
        }
    }
}