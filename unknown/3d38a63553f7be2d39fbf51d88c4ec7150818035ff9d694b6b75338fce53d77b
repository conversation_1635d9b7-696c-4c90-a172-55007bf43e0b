﻿using System;
using Newtonsoft.Json.Serialization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.NewtonsoftJson;
using Microsoft.AspNetCore.ResponseCompression;
using System.IO.Compression;
using CommunicationBLL;
using System.Configuration;
using ReadXmlProject;
using GlobalErrorHandling.Extensions;
using System.Net;

namespace CommAPI
{
    public class Startup
    {
        readonly string MyAllowSpecificOrigins = "_myAllowSpecificOrigins";

        public IConfiguration configRoot
        {
            get;
        }

        public Startup(IConfiguration configuration)
        {
            configRoot = configuration;
        }


        public void ConfigureServices(IServiceCollection services)
        {
            string[] origin = "ValidOrigins".AppSettings().Split(",").ToArray<string>();
            services.AddCors(options =>
            {
                options.AddPolicy(name: MyAllowSpecificOrigins,
                                  builder =>
                                  {
                                      builder.WithOrigins(origin)
                                        .AllowAnyHeader()
                                        .AllowCredentials()
                                        .AllowAnyMethod();
                                  });
            });

            services.AddControllers();
            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen();
            services.Configure<GzipCompressionProviderOptions>
                    (options => options.Level = CompressionLevel.Optimal);
            services.AddResponseCompression(options =>
            {
                options.EnableForHttps = true;
                options.Providers.Add<GzipCompressionProvider>();
            });

            services.AddScoped<ILeadPrioritizationBLL, LeadPrioritizationBLL>();
            services.AddScoped<ICommunicationBLL, CommunicationBLL.CommunicationBLL>();
            services.AddScoped<IDialerBLL, DialerBLL>();

            services.AddControllers().AddNewtonsoftJson();
            services.AddMvc().AddNewtonsoftJson(options => { options.SerializerSettings.ContractResolver = new DefaultContractResolver(); }); ;
            ServicePointManager.DefaultConnectionLimit = 50;            
        }


        public void Configure(WebApplication app, IWebHostEnvironment env)
        {
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI();
            }
            app.ConfigureExceptionHandler();//for globally catch error

            app.UseResponseCompression();
            app.UseHttpsRedirection();
            app.UseAuthorization();
            app.UseStaticFiles();
            app.UseRouting();
            app.UseCors(MyAllowSpecificOrigins);
            app.MapControllers();
            app.Run();
        }
    }
}

