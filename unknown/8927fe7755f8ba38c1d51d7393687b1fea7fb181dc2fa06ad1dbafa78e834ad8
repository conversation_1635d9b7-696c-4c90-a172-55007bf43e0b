﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PropertyLayers;
using Helper;
using DataAccessLibrary;
using ReadXmlProject;
using DataAccessLayer;

namespace OneLeadPriorityData
{
    public class OneLead_HealthFOS : IOneLead
    {
        readonly productPriorityconstant _LeadPriorityConstants;
        readonly Int16 _productID;
        readonly OneLeadData _OneLeadData;
        Int16 counter = 0;
        readonly Int16 _GroupID = 0;
        readonly Int16 _processId = 0;
        readonly OneLeadParams _oneLeadParams;

        public OneLead_HealthFOS(short productID, Int16 GroupID, Int16 processId, OneLeadParams oneLeadParams)
        {
            // TODO: Complete member initialization
            this._productID = productID;
            this._GroupID = GroupID;
            _processId = processId;
            _LeadPriorityConstants = getPriorityConfigByProduct(productID);
            _OneLeadData = new OneLeadData(productID, _LeadPriorityConstants, counter);
            _oneLeadParams = oneLeadParams;
        }
        // Getting Top PriorityLead say 5Leads
        public List<PriorityModel> getOneLead(Int16 ProductId, Int64 UserId, Int16 priorityleadCount, bool productCheck = false)
        {
            List<PriorityModel> lstPriorityModel = new List<PriorityModel>();
            List<PriorityModel> lstAgentAssignedLeads = LeadPrioritizationDLL.GetAgentActiveLeads(UserId, ProductId, false, _oneLeadParams, productCheck, _GroupID);// getting Agent all Active Leads

            List<PriorityModel> lstRest1stAttemptLeads = new List<PriorityModel>();

            lstPriorityModel = getPriorityQueue(lstPriorityModel, lstAgentAssignedLeads, lstRest1stAttemptLeads, priorityleadCount, ProductId, UserId);

            return lstPriorityModel;
        }
        // Get all active Leads of an agent   

        public List<PriorityModel> getBookedLead(Int16 ProductId, Int64 UserId, Int16 priorityleadCount)
        {
            List<PriorityModel> lstPriorityModel = new List<PriorityModel>();
            Int16 PriorityQueueSize = _LeadPriorityConstants.PriorityQueueSize;
            if (priorityleadCount == 1)
                PriorityQueueSize = 1;

            List<PriorityModel> lstAgentAssignedLeads = LeadPrioritizationDLL.GetAgentBookedLeads(UserId, ProductId, false);// getting Agent all Active Leads

            List<PriorityModel> lstRest1stAttemptLeads = new List<PriorityModel>();

            lstPriorityModel = getBookedPriorityQueue(lstPriorityModel, lstAgentAssignedLeads, lstRest1stAttemptLeads, PriorityQueueSize);

            return lstPriorityModel;
        }

        // get agent priority queue
        public List<PriorityModel> getPriorityQueue(List<PriorityModel> lstPriorityModel, List<PriorityModel> lstAgentAssignedLeads, List<PriorityModel> lstRest1stAttemptLeads, Int16 PriorityQueueSize, Int16 ProductID = 0, Int64 UserID = 0)
        {
            try
            {
                foreach (var prioritySequence in _LeadPriorityConstants.prioritySequence)
                {
                    List<PriorityModel>? _filteredleads = null;
                    switch (prioritySequence.ToUpper())
                    {
                        case "ACTIVEREVISIT": // active revisit
                            _filteredleads = _OneLeadData.GetActiveRevisitLeads(lstAgentAssignedLeads, counter, ProductID, UserID);
                            break;
                        case "2NDATTEMPTACTIVEREVISIT": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptActiveRevisit(lstAgentAssignedLeads, counter, ProductID, UserID); // 2nd attempt act revisit 
                            break;
                        case "ACTIVENEW": // active revisit
                            _filteredleads = _OneLeadData.GetActiveNewLead(lstAgentAssignedLeads, counter);  // active new
                            break;
                        case "2NDATTEMPTACTIVENEW": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptActiveNew(lstAgentAssignedLeads, counter);  // 2nd attempt active new
                            break;
                        case "PAYMENTCB": // active revisit
                            _filteredleads = _OneLeadData.GetPaymentCBLead(lstAgentAssignedLeads, counter);  // payment cb
                            break;
                        case "2NDATTEMPTPCB": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptPCBLeads(lstAgentAssignedLeads, counter);  // 2nd attempt payment cb
                            break;
                        case "EMAILREVISIT":
                            _filteredleads = _OneLeadData.GetEmailRevisitLeads(lstAgentAssignedLeads, counter);  // Email Revisit
                            break;
                        case "2NDATTEMPTEMAILREVISIT":
                            _filteredleads = _OneLeadData.Get2ndAttemptEmailRevisit(lstAgentAssignedLeads, counter);  // 2nd attempt Email Revisit
                            break;
                        case "PAYMENTFAILURE":
                            _filteredleads = OneLeadData.GetPaymentFailureLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTPAYMENTFAILURE":
                            _filteredleads = _OneLeadData.Get2ndAttemptPaymentFailureLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "ACTIVECB": // active revisit
                            _filteredleads = _OneLeadData.GetActiveCBLeads(lstAgentAssignedLeads, counter);  // active call back
                            break;
                        case "2NDATTEMPTACTIVECB": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptActiveCB(lstAgentAssignedLeads, counter);  // 2nd atmpactive call back
                            break;
                        case "PASSIVENEW": // active revisit
                            _filteredleads = _OneLeadData.GetPassiveNewLead(lstAgentAssignedLeads, counter); // passive new
                            break;
                        case "2NDATTEMPTPASSIVENEW": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptPassiveNew(lstAgentAssignedLeads, counter); // 2nd atmp passive new
                            break;
                        case "PASSIVEREVISIT": // active revisit
                            _filteredleads = _OneLeadData.GetPassiveRevisitLead(lstAgentAssignedLeads, counter, ProductID, UserID); // passive revisit
                            break;
                        case "2NDATTEMPTPASSIVEREVISIT": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptPassiveRevisit(lstAgentAssignedLeads, counter, ProductID, UserID); // 2nd atmp passive new
                            break;
                        case "PASSIVECB": // active revisit
                            _filteredleads = _OneLeadData.GetPassiveCBLead(lstAgentAssignedLeads, counter); // passive CALL BACK)
                            break;
                        case "2NDATTEMPTPASSIVECB": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptPassiveCB(lstAgentAssignedLeads, counter); // 2nd atmp passive CALL BACK)
                            break;
                        case "EXPIRY": // active revisit
                            _filteredleads = GetExpiryDateLeads(lstAgentAssignedLeads, counter);// expiryLeads
                            break;
                        case "CALLRELEASE": // active revisit
                            _filteredleads = CallReleaseLeads(lstAgentAssignedLeads, counter);   // call released
                            break;
                        case "UNANSWERED": // active revisit
                            _filteredleads = _OneLeadData.GetUnansweredLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "UNANSWEREDRECENT": // active revisit
                            _filteredleads = GetUnansweredRecentLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "REVISITCTC":
                            _filteredleads = _OneLeadData.GetCTCRevisitLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTREVISITCTC":
                            _filteredleads = _OneLeadData.Get2ndAttemptCTCRevisit(lstAgentAssignedLeads, counter);
                            break;
                        case "ANSWERED":
                            _filteredleads = _OneLeadData.GetAnsweredLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "RMLEAD":
                            _filteredleads = OneLeadData.GetRMLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "SERVICECB":
                            _filteredleads = _OneLeadData.GetServiceCBLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "REST":
                            _filteredleads = GetRest_1Leads(lstAgentAssignedLeads, counter);
                            lstRest1stAttemptLeads.AddRange(_filteredleads.Where(p => !lstPriorityModel.Exists(X => X.LeadID == p.LeadID)));
                            break;
                        case "RESTPRIORITYLEAD": // active revisit
                            _filteredleads = _OneLeadData.GetrestPriorityLeads(lstAgentAssignedLeads, counter, _GroupID, _processId, UserID);
                            break;
                        case "AIRESTLEADS": // active revisit
                            _filteredleads = _OneLeadData.GetAIRestLeads(lstAgentAssignedLeads, counter,_GroupID, UserID);
                            break;
                    }

                    if (_filteredleads != null && _filteredleads.Count > 0)
                        lstPriorityModel.AddRange(_filteredleads.Where(p => !lstPriorityModel.Exists(X => X.LeadID == p.LeadID)));
                    if (lstPriorityModel.Count >= PriorityQueueSize)
                        return lstPriorityModel;

                    counter += 1;
                }

            }
            catch (Exception)
            {


            }

            finally
            {
                LeadPrioritizationDLL.UpdateRestFlag(lstRest1stAttemptLeads);
            }

            return lstPriorityModel;
        }

        public List<PriorityModel> getBookedPriorityQueue(List<PriorityModel> lstPriorityModel, List<PriorityModel> lstAgentAssignedLeads, List<PriorityModel> lstRest1stAttemptLeads, Int16 PriorityQueueSize)
        {
            try
            {
                foreach (var prioritySequence in _LeadPriorityConstants.bookedPrioritySequence)
                {
                    List<PriorityModel>? _filteredleads = null;
                    switch (prioritySequence.ToUpper())
                    {
                        case "CALLBACK":
                            _filteredleads = _OneLeadData.GetBookedCBLeads(lstAgentAssignedLeads);
                            break;
                        case "TICKET":
                            _filteredleads = _OneLeadData.GetTicketLeads(lstAgentAssignedLeads);
                            break;
                        case "STATUSCHANGE":
                            _filteredleads = _OneLeadData.GetStatusChangeLeads(lstAgentAssignedLeads);
                            break;
                        case "UNANSWERED":
                            _filteredleads = _OneLeadData.GetBookedUnanswered(lstAgentAssignedLeads);
                            break;
                        case "REST": // active revisit
                            _filteredleads = _OneLeadData.GetBookedRest(lstAgentAssignedLeads);
                            lstRest1stAttemptLeads.AddRange(_filteredleads.Where(p => !lstPriorityModel.Exists(X => X.LeadID == p.LeadID)));
                            break;
                    }
                    if (_filteredleads != null && _filteredleads.Count > 0)
                        lstPriorityModel.AddRange(_filteredleads.Where(p => !lstPriorityModel.Exists(X => X.LeadID == p.LeadID)));
                    if (lstPriorityModel.Count >= PriorityQueueSize)
                        return lstPriorityModel;
                }
            }

            catch (Exception)
            {


            }

            return lstPriorityModel;
        }


        
        
        public List<PriorityModel> GetExpiryDateLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstTodayExpiry = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                Int16 TodyCreatdExprGap = _LeadPriorityConstants.TodayCreatedExprTimeGap;
                Int16 BefTodyCreatdExprGap = _LeadPriorityConstants.BeforeTodayCreatedExprTimeGap;
                byte ExpiryCount = _LeadPriorityConstants.ExpiryLeadsShowCount;
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(DateTime.Now);
                DateTime ct = DateTime.Now;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstTodayExpiry = lstPriorityModel
                                       .Where(x => x.Appointment != null
                                            && (x.Appointment.ScheduledOn.Date == ct.Date || x.Appointment.ScheduledOn.Date == ct.AddDays(1).Date)// Expiring today or tomorrow
                                            && (x.CallBack == null || x.CallBack.CBtime < ct) // no future call back
                                            && (x.Call != null && ct.Subtract(x.Call.calltime).TotalMinutes > TodyCreatdExprGap) // for today created check                                           
                                           )
                                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.RecentExpiry, counter))
                                        .OrderBy(x => x.Appointment != null ? x.Appointment.ScheduledOn : x.LeadCreatedOn).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstTodayExpiry;
        }

        public productPriorityconstant getPriorityConfigByProduct(Int16 productID)
        {
            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            var obj = _LeadPriorityConstants.PriorityConstant.Where(p => (p.productID == productID) && (p.subProduct == "FOS")).SingleOrDefault();
            if (obj == null)
                throw new NotImplementedException("getPriorityConfigByProduct config is empty");
            return obj;
        }

        public List<PriorityModel> CallReleaseLeads(List<PriorityModel> lstAgentActiveLeads, Int16 counter)
        {
            List<PriorityModel> lstReleasedCallleads = new List<PriorityModel>();
            try
            {
                DateTime CurrentTime = DateTime.Now;
                List<Int16> ExcessiveProducts = new List<short>() { 2, 7, 115 };
                DateTime ct = DateTime.Now;
                List<PriorityModel> lstAgentLeads = new List<PriorityModel>(lstAgentActiveLeads);
                LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();                
                if (lstAgentActiveLeads != null && lstAgentActiveLeads.Count > 0)
                {
                    lstReleasedCallleads = lstAgentLeads
                                       .Where(x => (x.CallReleaseCount > 0)
                                                    // && (x.Call != null && DateTime.Now.Subtract(x.Call.calltime).TotalMinutes < _LeadPriorityConstants.Releaseleadsshowtime)
                                                    && (x.SkippingTime < x.CallReleaseTime) //  so that if release after skipping then it should come in priority
                                                    && (!ExcessiveProducts.Contains(_productID) || x.Call.LastNminuteNANCeAttempts < 2 || (CurrentTime.Subtract(x.Call.calltime).TotalMinutes > 120 && x.Call.LastNminuteNANCeAttempts == 2)
                                                                                                  || (CurrentTime.Subtract(x.Call.calltime).TotalMinutes > 180 && x.Call.LastNminuteNANCeAttempts > 2)) //When consecutive 2 unanswered aqttempts                                                    
                                              ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.CallReleasedLeads, counter)).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstReleasedCallleads;
        }


        //GetUnansweredLeads
        private List<PriorityModel> GetUnansweredRecentLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstUnansweredLeads = new List<PriorityModel>();
            try
            {
                DateTime CurrentTime = DateTime.Now;
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(DateTime.Now);
                byte NANCMaxAttemInDiffShifts = _LeadPriorityConstants.NANCMaxAttemInDiffShifts;
                byte NANCLastCallDayGap = _LeadPriorityConstants.NANCLastCallDayGap;
                byte NANCMax1DayAttempt = _LeadPriorityConstants.NANCMax1DayAttempt;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                byte UnAnsweredDaysGap = _LeadPriorityConstants.UnAnsweredDaysGap;
                List<Int16> ExcessiveProducts = new List<short>() { 2, 7, 115 };
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstUnansweredLeads = lstPriorityModel.Where(x => (x.Call == null || (x.Call.TotalTT == 0 && x.Call.NANC_Attempts <= 4))
                                                          && (x.CallBack == null || DateTime.Now.Subtract(x.CallBack.CBtime).TotalSeconds > 0)// no future CB ..To handle existing CB Lead with tt =0
                                                          && (x.Call == null || (x.Call.TalkTime == 0 && DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC))// Not visible if NANC in last 30 mints
                                                          && (x.Call == null || CurrentTime.Subtract(x.Call.calltime).TotalDays > UnAnsweredDaysGap ||  //no shift check for lead called more than 2 days earlier 
                                                                                      (
                                                                                      ((x.Call.NANC_Attempts < NANCMaxAttemInDiffShifts) && ((x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < NANCMax1DayAttempt) && (x.Call.Shifts == null || !x.Call.Shifts.Contains(CurrentCallShift)))
                                                                                       || (x.Call.NANC_Attempts == NANCMaxAttemInDiffShifts && DateTime.Now.Date > x.Call.calltime.Date)// 7th Call on next day                                                                                       
                                                                                       || (x.Call.NANC_Attempts > NANCMaxAttemInDiffShifts && DateTime.Now.Date.Subtract(x.Call.calltime.Date).TotalDays >= NANCLastCallDayGap)// 8th and 9th Call on alternate days
                                                                                       )
                                                                                      )
                                                           && (!ExcessiveProducts.Contains(_productID) || x.Call == null || x.Call.TodaysAttempt < 5)
                                                          )
                                                         .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.UnansweredRecentLeads, counter)).OrderByDescending(x => x.LeadCreatedOn).ToList();//neverAnsweredCall
                }
            }
            catch (Exception )
            {

            }
            return lstUnansweredLeads;
        }

        private static List<PriorityModel> GetRest_1Leads(List<PriorityModel> lstPriorityModel, Int16 counter)// eventually means  Leads on which no work is done today
        {
            List<PriorityModel> lstRestLeads = new List<PriorityModel>();
            DateTime ct = DateTime.Now;
            try
            {
                lstRestLeads = lstPriorityModel
                    .Where(x => (x.Call != null)
                    && x.Call.calltime.Date != ct.Date
                    && (x.CallBack == null || DateTime.Now.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)    // No future CallBack
                    && (x.Appointment != null && x.Appointment.ScheduledOn > DateTime.MinValue && x.Appointment.ScheduledOn < ct)

                ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.RestLeads_1, counter))
                .OrderBy(x => x.Appointment != null ? x.Appointment.ScheduledOn : x.LeadCreatedOn).ToList();
            }
            catch (Exception)
            {

            }

            return lstRestLeads;
        }
    }
}
