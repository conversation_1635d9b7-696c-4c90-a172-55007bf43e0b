<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\ReadXmlProject\ReadXmlProject.csproj" />
    <ProjectReference Include="..\PropertyLayers\PropertyLayers.csproj" />
    <ProjectReference Include="..\DataAccessLayer\DataAccessLayer.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Interface\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Interface\" />
  </ItemGroup>
</Project>
