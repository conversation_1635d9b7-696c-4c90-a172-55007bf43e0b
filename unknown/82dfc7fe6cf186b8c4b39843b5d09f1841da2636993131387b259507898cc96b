﻿using System;
using DataAccessLibrary;
using DataHelper;
using System.Data;
using System.Data.SqlClient;
using System.Security.Policy;
using ReadXmlProject;
using PropertyLayers;

namespace DataAccessLayer
{
    public class CallCommunicationDLL
    {
        public static int CallCountInLastHour(long LeadID, string CallCountInLastHour = "1")
        {
            DateTime requestTime = DateTime.Now;
            try
            {
                string Connectionstring = ConnectionClass.ReplicasqlConnection();
                string strQuery = string.Empty;
                SqlParameter[] SqlParam = new SqlParameter[1];
                strQuery = "SELECT COUNT(1) FROM MTX.CallDataHistory (NOLOCK) WHERE CreatedOn > DATEADD(MINUTE,-" + CallCountInLastHour + ",GETDATE()) AND LeadID=@LeadID";
                SqlParam[0] = new SqlParameter("@LeadID", LeadID);
                Object Count = SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);
                return Convert.ToInt32(Count);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, ex.ToString(), "CallCountInLastHour", "CoreApi", "CallCommunicationDLL", string.Empty, string.Empty, requestTime, DateTime.Now);
                return 0;
            }
        }

        public static DataSet GetPredictiveLeadDetails(long leadId, short ReasonID = 0,long UserId = 0)
        {
            SqlParameter[] sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            sqlParam[1] = new SqlParameter("@UserId", UserId);
            sqlParam[2] = new SqlParameter("@ReasonID", ReasonID);

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetPredictiveLeadDetails]", sqlParam);
            return ds;
        }

        public static bool GetCommPreferrences(long MobileNo, long customerId, long LeadId, Int16 CategoryID, byte CommType)
        {
            try
            {
                if (CommType == 0)
                    CommType = 3;

                if (CategoryID == 0)
                    CategoryID = 1;

                SqlParameter[] SqlParam = new SqlParameter[5];
                SqlParam[0] = new SqlParameter("@CustomerID", customerId);
                SqlParam[1] = new SqlParameter("@MobileNo", MobileNo);
                SqlParam[2] = new SqlParameter("@LeadId", LeadId);
                SqlParam[3] = new SqlParameter("@CategoryID", CategoryID);
                SqlParam[4] = new SqlParameter("@ChannelID", CommType);
                var res = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[CheckCallPrefAllowed]", SqlParam);
                if (Convert.ToBoolean(res) == true)
                    return false;
                else
                    return true;

            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetCommPreferrences: " + ex.ToString());
                return true;
            }
        }

        public static DataTable GetLeadBasicDetails(long LeadId)
        {
            DataTable dt = null;
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            sqlParam[1] = new SqlParameter("@IsBooking", 0);
            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadAndBooking]", 3000, sqlParam);

            if (ds != null && ds.Tables.Count > 0)
                dt = ds.Tables[0];

            return dt;
        }

        public static int CallingBlockedCheck(long leadId, long customerId, Int64 userId, int flag)
        {
            SqlParameter[] sqlParam = new SqlParameter[4];
            sqlParam[0] = new SqlParameter("@CustomerID", customerId);
            sqlParam[1] = new SqlParameter("@LeadId", leadId);
            sqlParam[2] = new SqlParameter("@UserId", userId);
            sqlParam[3] = new SqlParameter("@Flag", flag);

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.CallingBlockedCheck", sqlParam);
            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                return Convert.ToInt32(ds.Tables[0].Rows[0]["Result"]);
            else
                return 0;

        }

        public static string GetContexQueue(string employeeId, ref string context, ref string ip, ref string DIDNo, ref string CallingCompany, ref int groupId, ref string VirtualNo, ref bool iSWFH, ref Int16 AgentCountry,ref string AswatPosition)
        {
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@EmployeeId", employeeId);
            SqlParam[1] = new SqlParameter("@RESULT", SqlDbType.VarChar, 50);
            SqlParam[1].Direction = ParameterDirection.Output;

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetContexQueue]", SqlParam);

            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                context = Convert.ToString(ds.Tables[0].Rows[0]["Context"]);
                ip = Convert.ToString(ds.Tables[0].Rows[0]["IP"]);
                DIDNo = Convert.ToString(ds.Tables[0].Rows[0]["DIDNO"]);
                CallingCompany = Convert.ToString(ds.Tables[0].Rows[0]["CallingCompany"]);
                groupId = ds.Tables[0].Rows[0]["GroupID"] != null && ds.Tables[0].Rows[0]["GroupID"] != DBNull.Value ? Convert.ToInt32(ds.Tables[0].Rows[0]["GroupID"]) : 0;
                VirtualNo = Convert.ToString(ds.Tables[0].Rows[0]["VirtualNo"]);
                iSWFH = ds.Tables[0].Rows[0]["iSWFH"] != DBNull.Value ? Convert.ToBoolean(ds.Tables[0].Rows[0]["iSWFH"]) : false;
                AgentCountry = Convert.ToInt16(ds.Tables[0].Rows[0]["AgentCountry"]);
                AswatPosition= Convert.ToString(ds.Tables[0].Rows[0]["AswatPosition"]);
                return context;
            }

            return "";
        }

        public static string DN_Context(Int64 MobileNo, Int64 LeadID)
        {
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@MobileNo", MobileNo);
            SqlParam[1] = new SqlParameter("@LeadID", LeadID);

            object _obj = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[IsDNCCustomer]", SqlParam);

            if (_obj != null)
            {
                return Convert.ToString(_obj);
            }

            return "";
        }


        public static long InsertCallData(DialerDispDetails _DispositionUpdate)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] sqlParam = new SqlParameter[22];
            sqlParam[0] = new SqlParameter("@CallID", _DispositionUpdate.CallId);
            if (_DispositionUpdate.IsService.Equals(1))
            {
                sqlParam[1] = new SqlParameter("@LeadID", _DispositionUpdate.LeadID);
            }
            else
            {
                sqlParam[1] = new SqlParameter("@LeadID", _DispositionUpdate.ParentID);
            }

            sqlParam[2] = new SqlParameter("@EmployeeID", _DispositionUpdate.AgentCode);
            sqlParam[3] = new SqlParameter("@CallDate", _DispositionUpdate.callDate);
            sqlParam[4] = new SqlParameter("@Duration", _DispositionUpdate.Duration);
            sqlParam[5] = new SqlParameter("@talktime", _DispositionUpdate.talktime);
            sqlParam[6] = new SqlParameter("@CallType", _DispositionUpdate.CallType);
            sqlParam[7] = new SqlParameter("@Status", _DispositionUpdate.Status);
            sqlParam[8] = new SqlParameter("@productId", _DispositionUpdate.ProductID);
            if (!string.IsNullOrEmpty(_DispositionUpdate.Disposition))
            {
                sqlParam[9] = new SqlParameter("@disposition", _DispositionUpdate.Disposition);
            }
            sqlParam[10] = new SqlParameter("@Context", _DispositionUpdate.Context);
            sqlParam[11] = new SqlParameter("@IsBMS", _DispositionUpdate.IsBMS);
            if (!string.IsNullOrEmpty(_DispositionUpdate.CallTrackingID))
            {
                sqlParam[12] = new SqlParameter("@CallDataID", Convert.ToInt64(_DispositionUpdate.CallTrackingID));
            }
            else
            {
                sqlParam[12] = new SqlParameter("@CallDataID", 0);
            }
            sqlParam[12].Direction = ParameterDirection.InputOutput;
            sqlParam[13] = new SqlParameter("@countryCode", _DispositionUpdate.CountryCode);
            sqlParam[14] = new SqlParameter("@phone", _DispositionUpdate.dst);
            sqlParam[15] = new SqlParameter("@IP", _DispositionUpdate.AsteriskIP);
            sqlParam[16] = new SqlParameter("@C2CID", _DispositionUpdate.C2CID);

            //InsertCheckFlag Added by sundarthapa 21-02-2019
            sqlParam[17] = new SqlParameter("@InsertCompleted", _DispositionUpdate.InsertCheckFlag);
            sqlParam[17].Direction = ParameterDirection.InputOutput;
            sqlParam[18] = new SqlParameter("@t_type", _DispositionUpdate.t_type);
            sqlParam[19] = new SqlParameter("@RecFileName", _DispositionUpdate.recfile);
            sqlParam[20] = new SqlParameter("@CallingNo", _DispositionUpdate.CallingNo);
            sqlParam[21] = new SqlParameter("@ReasonID", _DispositionUpdate.ReasonID);

            SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[InsertCallData]", sqlParam);

            _DispositionUpdate.InsertCheckFlag = Convert.ToByte(sqlParam[17].Value);

            return Convert.ToInt64(sqlParam[12].Value);

        }

        public static int ASWATUpdateCallId(string CallId, long CallDataId)
        {
            if (CallDataId == 0 || string.IsNullOrEmpty(CallId))
                return 0;

            string Connectionstring = ConnectionClass.LivesqlConnection();
            string strQuery = string.Empty;
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@CallID", SqlDbType.VarChar, 100);
            SqlParam[0].Value = CallId;
            SqlParam[0].SqlDbType = SqlDbType.VarChar;
            SqlParam[1] = new SqlParameter("@CallDataID", SqlDbType.BigInt);
            SqlParam[1].Value = CallDataId;
            strQuery = "UPDATE MTX.CallDataHistory SET CallID=@CallID WHERE CallDataID=@CallDataID";
            Object Count = SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);
            return Convert.ToInt32(Count);

        }

        public static DataSet GetEmergencyCallableNum(long leadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetEmergencyCallableNum]", sqlParam);
        }

        public static DataSet GetDIDDetails(long callFromNumber)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@DidNo", callFromNumber);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetDIDDetails]", sqlParam);
        }
    }
}

