﻿using System;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;

namespace Helper
{
    public static class CoreCommonMethods
    {
        public static bool IsValidString(string input)
        {
            bool result = false;
            if (!string.IsNullOrEmpty(input) && input != "undefined" && input != "null")
                result = true;
            return result;
        }

        public static string Base64Encode(string plainText)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
            return System.Convert.ToBase64String(plainTextBytes);
        }

        public static string GetEnvironmentVar()
        {
            string Enviornment = string.Empty;
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            Enviornment = Environment.GetEnvironmentVariable("MATRIX_ASPNETCORE_ENVIRONMENT");
            if (String.IsNullOrEmpty(Enviornment) || Enviornment.ToLower() == "production")
                Enviornment = con.GetSection("Communication").GetSection("Environment").Value.ToString();
            return Enviornment;
        }

        public static long IsValidInteger(string input)
        {
            long output = 0;
            try
            {
                if (!string.IsNullOrEmpty(input))
                    long.TryParse(input, out output);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in IsValidInteger: " + ex.ToString());
            }

            return output;
        }

        public static string ReadCookies(string reqCookies)
        {
            string request = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(reqCookies))
                {

                    byte[] data = Convert.FromBase64String(reqCookies);
                    string decodedString = Encoding.UTF8.GetString(data);

                    if (decodedString != null)
                        request = decodedString;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in ReadCookies: " + ex.ToString());
            }

            return request;
        }
    }
}

