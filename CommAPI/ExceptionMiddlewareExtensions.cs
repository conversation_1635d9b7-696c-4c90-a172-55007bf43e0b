﻿
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using System;
using System.Net;
using System.Text.Json;

namespace GlobalErrorHandling.Extensions
{
    public static class ExceptionMiddlewareExtensions
    {
        public static void ConfigureExceptionHandler(this IApplicationBuilder app)
        {

            app.UseExceptionHandler(appError =>
            {
                appError.Run(async context =>
                {
                    context.Response.StatusCode = 401;
                    context.Response.ContentType = "application/json";

                    var contextFeature = context.Features.Get<IExceptionHandlerFeature>();
                    if (contextFeature != null)
                    {
                        //string error = ((ExceptionHandlerFeature)contextFeature) !=null && ((ExceptionHandlerFeature)contextFeature).Path!=null ? ((ExceptionHandlerFeature)contextFeature).Path:string.Empty;
                        LoggingHelper.LoggingHelper.AddloginQueue("", 0, contextFeature.Error.ToString(), "ConfigureExceptionHandler", "OneLead", "ExceptionMiddlewareExtensions", "", "", DateTime.Now, DateTime.Now);

                        await context.Response.WriteAsync(new ErrorDetails()
                        {
                            StatusCode = 401,
                            Message = "UnauthorIzed Response."
                        }.ToString());


                    }
                });
            });
        }
    }
    public class ErrorDetails
    {
        public int StatusCode { get; set; }
        public string Message { get; set; }
        public override string ToString()
        {
            return JsonSerializer.Serialize(this);
        }
    }
}