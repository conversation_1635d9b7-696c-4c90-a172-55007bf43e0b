﻿<?xml version="1.0" encoding="UTF-8"?>
<ConfigSettings>
    <add key="ISErrorLog" value="1"/>
	<add key="ISLog" value="1"/>
	<add key="LogMethod" value="mongo"/>
	<add key="queueloggingon" value="1"></add>
	<add key="IsSQSLogingQueueEnabled" value="false"></add>
	<add key="ISMongoErrorLog" value="1" />
	<add key="ChurnProducts" value="2" />
	<add key="LeadPriorityProduct" value="2,3,7,114,115,117,131,154,101,139" />
	<add key="HealthPaymentGroups" value="1610,1630,3160,3161,3156" />
	<add key="MotorChatGroups" value="1502"></add>
	<add key="ChatIBRenewalGroups" value="682,699,1324,545,137,1443,906,466"></add>
	<add key="HealthFOSGroups" value="1748"></add>
	<add key ="MongoTimeOut" value="10000"/>
	<add key="toomanyattemptrestriction" value="11"></add>
	<add key="ValidOrigins" value="https://progressiveqa.policybazaar.com"></add>
	<add key="FosChurnIds" value="62,63"></add>
	<add key="TotalTalkTime2" value="30000" />
	<add key="TotalTalkTime106" value="30000"/>
	<add key="TotalTalkTime118" value="30000"/>
	<add key="TotalTalkTime130" value="30000"/>
	<add key="TotalTalkTime7" value="30000" />
	<add key="TotalTalkTime115" value="30000" />
	<add key="TotalTalkTime117" value="30000" />
	<add key="TotalTalkTime3" value="30000" />
	<add key="TotalTalkTime131" value="900" />
	<add key="TotalTalkTime101" value="900" />
	<add key="LastTalkTime2" value="120" />
	<add key="LastTalkTime106" value="120"/>
	<add key="LastTalkTime118" value="120" />
	<add key="LastTalkTime130" value="120" />
	<add key="LastTalkTime7" value="120" />
	<add key="LastTalkTime115" value="120" />
	<add key="LastTalkTime117" value="120" />
	<add key="LastTalkTime3" value="120" />
	<add key="LastTalkTime131" value="300" />
	<add key="LastTalkTime101" value="300" />
	<add key="ProspectTalkTime117" value="600" />
	<add key="ProspectTalkTime3" value="300" />
	<add key="TotalTalkTime139" value="241" />
	<add key="ProspectTalkTime139" value="480" />
	<add key="LastTalkTime139" value="241" />
	<add key="Contacted106" value="10"/>
	<add key="Contacted118" value="10"/>
	<add key="Contacted130" value="10"/>
	<add key="Contacted148" value="10" />
	<add key="Contacted155" value="10" />
	<add key="Contacted163" value="10" />
	<add key="LastTalkTime148" value="120" />
	<add key="LastTalkTime155" value="120" />
	<add key="LastTalkTime163" value="120" />
	<add key="Contacted131" value="30" />
	<add key="Contacted101" value="10" />
	<add key="TotalTalkTime163" value="30000" />
	<add key="ProspectTalkTime163" value="600" />
	<add key="TotalTalkTime148" value="30000" />
	<add key="TotalTalkTime155" value="30000" />
	<add key="ProspectTalkTime148" value="600" />
	<add key="TotalTalkTime150" value="30000" />
	<add key="LastTalkTime150" value="180" />
	<add key="ProspectTalkTime150" value="600" />
  <add key="TotalTalkTime114" value="30000"/>
  <add key="LastTalkTime114" value="120"/>
  <add key="ProspectTalkTime114" value="600"/>
	<add key="coreAPI" value="https://apiqa.policybazaar.com/"></add>
	<add key="coreAPIv1" value="https://apiv1qa.policybazaar.com/"></add>
	<add key="producttimeout" value="1000"></add>
	<add key="coreAPIencKey" value="Aqn5RCrH7vDIjdLRDZG6R4sFrTB5Ax7g"></add>
	<add key="coreAPIivKey" value="bO4ujK1eepa0BT95"></add>
	<add key="CallCountInLastHour" value="60"/>
	<add key="BlockedNumberCalling" value="False"/>
	<add key="CheckChatStatus" value="True"/>
	<add key="ProductIDListForChat" value=",1,117,2,115,7,1000,101,"/>
	<add key="UAEdomain" value="tpmatrix.policybazaar.com"/>
	<add key="NRIDialerCode" value="94674"/>
	<add key="DialerCode" value="645740"/>
	<add key="WFHURL_UAE" value="http://10.0.248.103/api/dialer/mob2mobcall.php?"></add>
	<add key="WFHURL" value="https://dialerqaapi.policybazaar.com/api/dialer/mob2mobcall.php?"></add>
	<add key="simPanelGroups" value="1708"></add>
	<add key="simPanelGroupsExp" value="false"></add>
	<add key="coreAPIauthKey" value="fw03VZNVHPln4VS94QljBt31il7LbrhmsBCGTkeI"></add>
	<add key="coreAPIclientKey" value="rYAhR07qvs"></add>
	<add key="ThirdPartyStartTime" value="09:00"></add>
	<add key="ThirdPartyEndTime" value="21:00"></add>
	<add key="replicatimeout" value="3"></add>
	<add key="AmazonSqsActive" value="0"/>
	<add key="AmazonSqsURL" value="https://sqs.ap-south-1.amazonaws.com/721537467949/CallData"></add>
	<add key="dialerdataqueue" value=".\private$\dialerdata"/>
	<add key="RecordingSqsURL" value="https://sqs.ap-south-1.amazonaws.com/721537467949/pbexotel"></add>
	<add key="ManualStampingProds" value=",106,118,130," />
	<add key="minPointDeductTime" value="2"></add>
	<add key="DialerAPI" value="https://dialerqaapi.policybazaar.com/"></add>
	<add key="DialerAPITimeout" value="5000"/>
	<add key="intProductIds" value=",148,150,151,152,153,155,156,157,158,159,160,163,164,167,168,169,170,171,172,173,174,175,176,178,179,180,"></add>
	<add key="BMSAPI" value="https://pbqaserviceapi.policybazaar.com/communication/" />
	<add key="BMSAPIToken" value="EDE67496-6033-466C-B0A8-C25CE9AD1939" />
	<add key="aswatcallingurl" value="https://policybazaar-api.aswat.co/integrations/cti/agents/"></add>
	<add key="aswat_api_key" value="e3c3a2e4-c6a7-46ec-ab4a-c1ae0d7fdc09"></add>
	<add key="aswat_api_token" value="cefa0470-c6fc-11e8-a09b-633c83f7f32c"></add>
	<add key="BMSSqsURL" value="https://sqs.ap-south-1.amazonaws.com/721537467949/DialerHangUpEventQueueQA"></add>
	<add key="BookedSOSReasons" value="42,54,55"></add>
	<add key="RestrictCallingProducts" value="2,7,115"></add>
	<add key="ErrorNInfoLogFilePath" value="D:\Logs\CommunicationLogs\CommunicationService\" />
	<add key="matrixCallSchduleURL" value="http://mobilenotif.policybazaar.com/mobile/notif/matrixCallSchdule/"/>
	<add key="matrixCallSchduleURLtimeout" value="3000"/>
	<add key="RestrictCallingPrds" value="2,7"></add>
	<add key="IsAWSSceretEnabled" value="true" />
	<add key="SecretEnvironment" value="QA_Matrix" />
	<add key="HNIGroupIDs" value="39,828,64,1708,1647,1102,828,1708,1647,2784,1746,2771,1363,2688,2262,2073,2471,2072,2432,2536,2548,2642,2958" />
	<add key="CallBackWaitTime" value="15" />
	<add key="AllowdSmeMarineVirtualNumbers" value="9845478183,9845478180,9845478165,9845475634,9205938502,7411951761,9845478852,9845478826,9289791303,9289791483"/>
	<add key="AllowedSmeWorkmenVirtualNumbers" value="7428483281,9289792447,9289792588,7428483239,7428483264,7428483281,7428483290,7428483299,7428483385"/>
	<add key="AllowedSmeEnggVirtualNumbers" value="9289791667,9289791993,9289792090"/>
	<add key="MarineLS" value="Referral,UpSell"/>
	<add key="WorkmenLS" value="UpSell,Referral,winback"/>
	<add key="CommunicationAPItimeout" value="3000"/>
	<add key="commserviceapi" value="https://commserviceapiqa.policybazaar.com/" />
	<add key="CommToken" value="1EB6FF47-DA47-4B19-BB24-31C4CDE1BB50" />
	<add key="MatrixApiUrl" value="https://qainternalmatrixapi.policybazaar.com/" />
	<add key="MatrixSrc" value="matrix" />
	<add key="MatrixClientKey" value="L6YWNav" />
	<add key="MatrixAuthKey" value="LGsaWLYmF6YWNav" />
	<add key="termevent" value= "https://pbeventqa.policybazaar.com/pbeventtracker/api/event"></add>
	<add key="onCallMobNoHashingKey" value="qrO5I3E0Ztdp1VVv"></add>
	<add key="SameGroupCallAlwdGrps" value= "959"></add>
	<add key="TermEventSqsURL" value="https://sqs.ap-south-1.amazonaws.com/721537467949/pb_event"></add>

</ConfigSettings>