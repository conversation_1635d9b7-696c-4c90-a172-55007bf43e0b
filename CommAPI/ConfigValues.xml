﻿<?xml version="1.0" encoding="UTF-8"?>
<ConfigSettings>
    <add key="ISErrorLog" value="1"/>
	<add key="ISLog" value="1"/>
	<add key="LogMethod" value="mongo"/>
	<add key="queueloggingon" value="1"></add>
	<add key="IsSQSLogingQueueEnabled" value="true"></add>
	<add key="ISMongoErrorLog" value="1" />
	<add key="ChurnProducts" value="2" />
	<add key="LeadPriorityProduct" value="2,3,7,114,115,117,131,154,101,139" />
	<add key="HealthPaymentGroups" value="1610,1630,3160,3161,3156" />
	<add key="MotorChatGroups" value="1502"></add>
	<add key="ChatIBRenewalGroups" value="682,699,1324,545,137,1443,906,466"></add>
	<add key="HealthFOSGroups" value="1748"></add>
	<add key ="MongoTimeOut" value="7000"/>
	<add key="toomanyattemptrestriction" value="11"></add>
	<add key="ValidOrigins" value="https://mobilematrix.policybazaar.com,https://matrixliveapi.policybazaar.com,https://matrix.policybazaar.com,https://tpmatrix.policybazaar.com"></add>
	<add key="FosChurnIds" value="62,63"></add>
	<add key="TotalTalkTime2" value="30000" />
	<add key="TotalTalkTime7" value="30000" />
	<add key="TotalTalkTime115" value="30000" />
	<add key="TotalTalkTime117" value="30000" />
	<add key="TotalTalkTime3" value="30000" />
	<add key="TotalTalkTime131" value="900" />
	<add key="TotalTalkTime101" value="900" />
	<add key="LastTalkTime2" value="120" />
	<add key="LastTalkTime7" value="120" />
	<add key="LastTalkTime115" value="120" />
	<add key="LastTalkTime117" value="120" />
	<add key="LastTalkTime3" value="120" />
	<add key="LastTalkTime131" value="300" />
	<add key="LastTalkTime101" value="300" />
	<add key="LastTalkTime106" value="120"/>
	<add key="LastTalkTime118" value="120" />
	<add key="LastTalkTime130" value="120" />
	<add key="TotalTalkTime106" value="30000"/>
	<add key="TotalTalkTime118" value="30000"/>
	<add key="TotalTalkTime130" value="30000"/>
	<add key="Contacted106" value="10"/>
	<add key="Contacted118" value="10"/>
	<add key="Contacted130" value="10"/>
	<add key="ProspectTalkTime117" value="600" />
	<add key="ProspectTalkTime3" value="300" />
	<add key="TotalTalkTime139" value="241" />
	<add key="ProspectTalkTime139" value="480" />
	<add key="LastTalkTime139" value="241" />
	<add key="Contacted148" value="10" />
	<add key="Contacted155" value="10" />
	<add key="Contacted163" value="10" />
	<add key="LastTalkTime148" value="120" />
	<add key="LastTalkTime155" value="120" />
	<add key="LastTalkTime163" value="120" />
	<add key="Contacted131" value="30" />
	<add key="Contacted101" value="10" />
	<add key="TotalTalkTime163" value="30000" />
	<add key="ProspectTalkTime163" value="600" />
	<add key="TotalTalkTime148" value="30000" />
	<add key="TotalTalkTime155" value="30000" />
	<add key="ProspectTalkTime148" value="600" />
	<add key="TotalTalkTime150" value="30000" />
	<add key="LastTalkTime150" value="180" />
    <add key="TotalTalkTime114" value="30000"/>
    <add key="LastTalkTime114" value="120"/>
    <add key="ProspectTalkTime114" value="600"/>
	<add key="ProspectTalkTime150" value="600" />
	<add key="DialerAPI" value="https://dialerapi.policybazaar.com/"></add>
	<add key="DialerAPITimeout" value="3000"/>
	<add key="coreAPI" value="https://cs.policybazaar.com/"></add>
	<add key="coreAPIv1" value="https://apiv1.policybazaar.com/"></add>
	<add key="producttimeout" value="1000"></add>
	<add key="coreAPIencKey" value="mGMxVKHNIpGHMFB6KFq1pQcAu1Thscbf"></add>
	<add key="coreAPIivKey" value="th6JI3aXVaO3tSk0"></add>
	<add key="CallCountInLastHour" value="60"/>
	<add key="BlockedNumberCalling" value="False"/>
	<add key="CheckChatStatus" value="True"/>
	<add key="ProductIDListForChat" value=",1,117,2,115,7,1000,101,"/>
	<add key="UAEdomain" value="https://tpmatrix.policybazaar.com/,https://tpmatrix.policybazaar.com,tpmatrix.policybazaar.com"/>
	<add key="NRIDialerCode" value="94674"/>
	<add key="DialerCode" value="645740"/>
	<add key="WFHURL_UAE" value="http://************/api/dialer/mob2mobcall.php?"></add>
	<add key="WFHURL1" value="http://***********/api/dialer/mob2mobcall.php?"></add>
  <add key="WFHURL" value="https://dialerapi.policybazaar.com/api/dialer/mob2mobcall.php?"></add>
	<add key="simPanelGroups" value="1708"></add>
	<add key="simPanelGroupsExp" value="false"></add>
	<add key="coreAPIauthKey" value="nlGpH1UxWGzIJfRXXFlyYPtf814HETXWX09tahQJ"></add>
	<add key="coreAPIclientKey" value="OlWfN8v7wF"></add>
	<add key="ThirdPartyStartTime" value="09:00"></add>
	<add key="ThirdPartyEndTime" value="21:00"></add>
	<add key="replicatimeout" value="3"></add>
	<add key="AmazonSqsActive" value="1"/>
	<add key="AmazonSqsURL" value="https://sqs.ap-south-1.amazonaws.com/************/CallData"></add>
	<add key="dialerdataqueue" value=".\private$\dialerdata"/>
	<add key="RecordingSqsURL" value="https://sqs.ap-south-1.amazonaws.com/************/pbexotel"></add>
	<add key="ManualStampingProds" value=",106,118,130," />
	<add key="minPointDeductTime" value="2"></add>
	<add key="intProductIds" value=",148,150,151,152,153,155,156,157,158,159,160,163,164,167,168,169,170,171,172,173,174,175,176,178,179,180,"></add>
	<add key="BMSAPI" value="https://pbserviceapi.policybazaar.com/communication/" />
	<add key="BMSAPIToken" value="EDE67496-6033-466C-B0A8-C25CE9AD1939" />
    <add key="aswatindiacallingurl" value="https://paisabazaar-api.aswat.co/"></add>
	<add key="aswat_india_api_key" value="1fc7245e-5bbd-405e-b28d-663e096955ef"></add>
	<add key="aswat_india_access_token" value="43d3d750-6358-11ea-ae24-dfe838abd242"></add>
    <add key="aswatcallingurl" value="https://policybazaar-api.aswat.co/"></add>
	<add key="aswat_api_key" value="e3c3a2e4-c6a7-46ec-ab4a-c1ae0d7fdc09"></add>
	<add key="aswat_api_token" value="d5465496-b5f6-4d6f-bf8a-e4b37422b778"></add>
    <add key="InternationalDialerCode" value=",971,1,"/>
	<add key="IsAWSSceretEnabled" value="true" />
	<add key="SecretEnvironment" value="Prod_Matrix" />
	<add key="BMSSqsURL" value="https://sqs.ap-south-1.amazonaws.com/************/DialerHangUpEventQueue"></add>
	<add key="BookedSOSReasons" value="42,54,55"></add>
	<add key="RestrictCallingProducts" value="2,7,115"></add>
	<add key="ErrorNInfoLogFilePath" value="D:\Logs\CommunicationLogs\CommunicationService\" />
	<add key="matrixCallSchduleURL" value="http://mobilenotif.policybazaar.com/mobile/notif/matrixCallSchdule/"/>
	<add key="matrixCallSchduleURLtimeout" value="3000"/>
	<add key="RestrictCallingPrds" value="2,7"></add>
	<add key="HNIGroupIDs" value="39,828,64,1708,1647,1102,828,1708,1647,2784,1746,2771,1363,2688,2262,2073,2471,2072,2432,2536,2548,2642,2958,2728" />
	<add key="CallBackWaitTime" value="15" />
	<add key="CommunicationAPItimeout" value="3000"/>
	<add key="TotalTalkTime195" value="30000"/>
	<add key="LastTalkTime195" value="120"/>
	<add key="ProspectTalkTime195" value="600"/>
	<add key="commserviceapi" value="https://commserviceapi.policybazaar.com/" />
	<add key="CommToken" value="C368C1B8-7C6B-4767-B813-075406DD0A42" />
	<add key="AllowdSmeMarineVirtualNumbers" value="9845478183,9845478180,9845478165,9845475634,9205938502,7411951761,9845478852,9845478826,9289791303,9289791483"/>
	<add key="AllowedSmeWorkmenVirtualNumbers" value="7428483281,9289792447,9289792588,7428483239,7428483264,7428483281,7428483290,7428483299,7428483385"/>
	<add key="AllowedSmeEnggVirtualNumbers" value="9289791667,9289791993,9289792090"/>
	<add key="MarineLS" value="Referral,UpSell"/>
	<add key="WorkmenLS" value="UpSell,Referral,winback"/>

	<add key="MatrixApiUrl" value="https://internalmatrixapi.policybazaar.com/" />
	<add key="MatrixSrc" value="matrix" />
	<add key="MatrixClientKey" value="L6YWNav" />
	<add key="MatrixAuthKey" value="LGsaWLYmF6YWNav" />
	<add key="termevent" value= "https://pbevent.policybazaar.com/pbeventtracker/api/event"></add>
	<add key="SameGroupCallAlwdGrps" value= "1181,1182,1471,3153,3151,3152,3310,3309,3308,3332,3386,3537,3538,3539,1256"></add>
	<add key="TermEventSqsURL" value="https://sqs.ap-south-1.amazonaws.com/************/pb_event"></add>

</ConfigSettings>