WITH CTE AS ( 
    select LD.CustomerID as CustomerID 
    from sqldb_athena.pbcroma_crm_Leaddetails LD 
    LEFT JOIN sqldb_athena.pbcroma_CRM_InvalidMobileNumbers INV 
        ON LD.customerid=INV.CustID 
    WHERE LD.pb_date BETWEEN '2025-04-01' AND '2025-04-02'  -- Use partition column first
        AND CreatedON BETWEEN '2025-04-01' AND '2025-04-02' 
        AND ProductID=7 
        AND ParentID IS NULL 
        AND INV.id IS NULL
), 
Calldata AS (
    SELECT distinct LD.CustomerID as CustomerID, 
        LD.CustomerID-600000 as cust_id_1,
        CDH.CallDataID as CallDataID,
        CDH.CallDate as CallDate,
        CDH.CallType as CallType,
        CDH.talktime as talktime,
        CASE WHEN CDH.talktime > 0 THEN 1 ELSE 0 END AS IsAnswered, 
        ld.ProductID as ProductID,
        CASE WHEN (date_diff('minute', parse_datetime(ED.eventdate, 'yyyy-MM-dd HH:mm:ss'), 
                             parse_datetime(CDH.CallDate, 'yyyy-MM-dd HH:mm:ss')) 
                  BETWEEN -30 AND 30) THEN 1 ELSE 0 END AS IsCallback
    FROM sqldb_athena.pbcroma_CRM_Leaddetails LD  
    INNER JOIN CTE C ON LD.CustomerID=C.CustomerID 
    INNER JOIN sqldb_athena.pbcroma_mtx_calldatahistory CDH 
        ON LD.LeadID=CDH.LeadID
        AND CDH.pb_date BETWEEN '2021-05-01' AND '2025-03-15'  -- Use partition column in JOIN condition
    LEFT JOIN sqldb_athena.pbcroma_crm_eventdetails ED 
        ON CDH.LeadID=ED.LeadID
    WHERE LD.ParentID IS NULL 
        AND LD.pb_date BETWEEN '2021-05-01' AND '2025-03-15'  -- Use partition column first
        AND LD.CreatedON BETWEEN '2021-05-01' AND '2025-03-15'
), 
validcustomer AS (
    SELECT CustomerID
    FROM Calldata
    GROUP BY CustomerID
    HAVING count(1) > 1
)
SELECT CustomerID, cust_id_1, CallDataID, CallDate, CallType, talktime, IsAnswered, ProductID
FROM Calldata
WHERE CustomerID IN (SELECT CustomerID FROM validcustomer)
