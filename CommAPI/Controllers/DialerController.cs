﻿using System;
using System.Collections.Generic;
using CommunicationBLL;
using Helper;
using Microsoft.AspNetCore.Mvc;
using PropertyLayers;

namespace CommAPI.Controllers
{
    [CommAuth]
    [ApiController]
    [Route("onelead/api/[controller]/[action]")]
    public class DialerController : ControllerBase
    {
        private readonly IDialerBLL objBLL;

        public DialerController(IDialerBLL prioritizationBLL)
        {
            objBLL = prioritizationBLL;
        }

        [HttpPost]
        public DialerAsteriskData updateAgentLoginStatus(PredictiveAgentStatus oPredictiveAgentStatus)
        {
            oPredictiveAgentStatus.Source = Request != null ? Request.Headers["source"] : string.Empty;
            DialerAsteriskData res = null;
            if (oPredictiveAgentStatus != null && !string.IsNullOrEmpty(oPredictiveAgentStatus.AgentCode))
                res = objBLL.updateAgentLoginStatus(oPredictiveAgentStatus);

            return res;

        }

        [HttpPost]
        public int QueueAgentsLogoutBulk(List<string> Queues)
        {
            try {
                return objBLL.QueueAgentsLogoutBulk(Queues);
            }
            catch {
                return 0;
            }
        }
        [HttpPost]
        public bool InsertUpdateDialerData(DialerDispDetails _DispositionUpdate)
        {
            bool result = false;
            DateTime dtreq = DateTime.Now;
            try
            {
                return objBLL.UpdateOBCallDispositionDetails(_DispositionUpdate);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(_DispositionUpdate.CallId, Convert.ToInt64(_DispositionUpdate.LeadID), ex.ToString(), "InsertUpdateDialerData", "DialerController", "OneLeadPriority", string.Empty, string.Empty, dtreq, DateTime.Now);
            }
            return result;
        }

        [HttpGet("{uid}/{CallingNo}/{LeadID}")]
        public ResponseData<bool> UpdateCustomerConnect(Int64 uid, Int64 CallingNo, Int64 LeadID)
        {
            string EmpId = (string)((HttpContext.Request != null && HttpContext.Request.Query.ContainsKey("EmpId")) && CoreCommonMethods.IsValidString(Convert.ToString(HttpContext.Request.Query["EmpId"])) ? Convert.ToString(HttpContext.Request.Query["EmpId"]) : "");
            return objBLL.UpdateCustomerConnect(uid, CallingNo, LeadID, EmpId);
        }
    }
}

