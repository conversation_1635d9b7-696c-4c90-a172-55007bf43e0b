﻿using CommunicationBLL;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using PropertyLayers;

namespace CommAPI.Controllers
{
        [ApiController]
        [Route("Calling/api/[controller]/[action]")]

        public class LeadController : ControllerBase
        {
        private readonly IDialerBLL objBLL;        

        public LeadController(IDialerBLL prioritizationBLL)
        {
            objBLL = prioritizationBLL;
        }

        [HttpGet]
        public ResponseAPI HealthCheck()
        {
            ResponseAPI Response = new ResponseAPI();
            Response.status = true;
            Response.message = "Success";

            return Response;
        }

        [HttpPost]
        public bool InsertUpdateDialerData(DialerDispDetails _DispositionUpdate)
        {
            bool result = false;
            DateTime dtreq = DateTime.Now;
            try
            {
                return objBLL.UpdateOBCallDispositionDetails(_DispositionUpdate);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(_DispositionUpdate.CallId, Convert.ToInt64(_DispositionUpdate.LeadID), ex.ToString(), "InsertUpdateDialerData", "LeadController", "OneLeadPriority", string.Empty, string.Empty, dtreq, DateTime.Now);
            }
            return result;
        }

        //[HttpGet]
        //public bool InsertCallData(string CallID, string LeadID, string? AgentID, string? Duration, string? talkTime, string? status, string? callDate, string? callType, string? Context, string? Disposition, string? uid, string? dst, string? channel, string? action, string? hanguptime, string? IP, string? callRequestType, string? IsBMS, string? recfile, string? t_type, string? CallingNo, string? rectype)
        //{
        //    bool result = false;
        //    DateTime dtreq = DateTime.Now;
        //    ILeadPrioritizationBLL objLP = new LeadPrioritizationBLL();
        //    try
        //    {                
        //        result = objLP.PushCallDatainQueue(CallID, LeadID, AgentID, Duration, talkTime, status, callDate, callType, Context, Disposition, uid, dst, channel, action, hanguptime, IP, callRequestType, IsBMS, recfile, t_type, CallingNo, rectype);
        //    }
        //    catch (Exception ex)
        //    {
        //        LoggingHelper.LoggingHelper.AddloginQueue(CallID, Convert.ToInt64(LeadID), ex.ToString(), "InsertCallData", "LeadPrioritizationController", "OneLeadPriority", string.Empty, string.Empty, dtreq, DateTime.Now);
        //    }
        //    return result;
        //}

        //[HttpPost]
        //public AddLeadValidation ValidateAddLeadToPriorityQueue(UserNext5Leads priorityLead)
        //{
        //    AddLeadValidation result = new AddLeadValidation();
        //    DateTime dtreq = DateTime.Now;
        //    ILeadPrioritizationBLL objLP = new LeadPrioritizationBLL();
        //    try
        //    {
        //        if (priorityLead != null && priorityLead.Leads[0].LeadId > 0)
        //        {
        //            return objLP.ValidateAddLeadToPriorityQueue(priorityLead);
        //        }
        //        else
        //        {
        //            result.message = "Invalid LeadId";
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(priorityLead.Leads[0].LeadId),
        //            priorityLead.Leads[0].LeadId, ex.ToString(),
        //            "ValidateAddLeadToPriorityQueue", "OneLeadPriority", "LeadController",
        //             JsonConvert.SerializeObject(priorityLead), JsonConvert.SerializeObject(result),
        //            dtreq, DateTime.Now);
        //    }
        //    return result;
        //}

        [CommAuth]
        [HttpGet("{LeadId}")]
        public ResponseData<DialerLeadDetailModel>? GetLeadBasisInfo(Int64 LeadId)
        {
            if (LeadId == 0)
                return null;

            return new ResponseData<DialerLeadDetailModel>()
            {
                Data = objBLL.GetLeadBasisInfo(LeadId, out bool status),
                Status = status,
                Message = string.Empty
            };
        }
    }
}
