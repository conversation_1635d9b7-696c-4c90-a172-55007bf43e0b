﻿using System;
using System.Diagnostics;
using System.Runtime.Caching;
using Microsoft.AspNetCore.Mvc;
using PropertyLayers;
using ReadXmlProject;

namespace CommAPI.Controllers
{
    [ApiController]
    [Route("onelead/api/[controller]/[action]")]
    public class IndexController: ControllerBase
    {
        [HttpGet]
        public ResponseAPI HealthCheck()
        {
            ResponseAPI Response = new ResponseAPI();
            Response.status = true;
            Response.message = "Success";

            return Response;
        }

        [HttpGet]
        public IActionResult NonAuthorised()
        {
            Debug.WriteLine("Invalid Token dubug.");
            return new ContentResult { StatusCode = 401, Content = "Invalid Token", ContentType = "text/plain" };
        }
        [HttpGet("{Key}")]
        public bool removeCache(string Key)
        {
            if (MemoryCache.Default[Key] != null)
            {
                MemoryCache.Default.Remove(Key);
                return true;
            }
            return false;
        }
        [HttpGet]
        public IActionResult NonAccess()
        {
            return new ContentResult { StatusCode = 401, Content = "you don't have permission", ContentType = "text/plain" };
        }

        [HttpGet("{Key}")]
        public ResponseData<dynamic> GetDataByKey(string Key)
        {
            ResponseData<dynamic> res = new() { Data = null, Message = "Something went wrong", Status = false };

            string passKey = Request != null ? Request.Headers["AuthKey"] : string.Empty;
            if (passKey != "DASJr3#r3#rfs324:DF/[fsd--4")
            {
                return res;
            }

            DateTime dt = DateTime.Now;
            try
            {
                if (MemoryCache.Default[Key] != null)
                {
                    dynamic obj = MemoryCache.Default.Get(Key);
                    if (obj != null)
                    {
                        res.Data = obj;
                        res.Message = "success";
                        res.Status = true;
                    }
                    else
                    {
                        res.Data = null;
                        res.Message = "Data not found";
                        res.Status = false;
                    }
                }

                else
                {
                    res.Data = null;
                    res.Message = "key not found";
                    res.Status = false;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, Convert.ToInt64(Key), ex.ToString(), "GetDataByKey", "FOS", "FOSBLL", "", "", dt, DateTime.Now);
            }
            return res;

        }
        [HttpGet]
        public ResponseData<List<string>> GetAllKeys()
        {
            ResponseData<List<string>> res = new ResponseData<List<string>>() { Data = null, Message = "something went wrong", Status = false };

            string passKey = Request != null ? Request.Headers["AuthKey"] : string.Empty;
            if (passKey != "DASJr3#r3#rfs324:DF/[fsd--4") {
                return res;
            }

            DateTime dt = DateTime.Now;
            try
            {
                var cacheKeys = MemoryCache.Default.Select(kvp => kvp.Key).ToList();
                if (cacheKeys != null)
                {
                    res.Data = cacheKeys;
                    res.Message = "success";
                    res.Status = true;
                }
                else
                {
                    res.Data = null;
                    res.Message = "not found";
                    res.Status = false;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "GetAllKeys", "FOS", "FOSBLL", "", "", dt, DateTime.Now);
            }
            return res;
        }
    }
}

