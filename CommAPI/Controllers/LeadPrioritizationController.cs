﻿using CommunicationBLL;
using Google.Apis.Sheets.v4.Data;
using Helper;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using PropertyLayers;
using StackExchange.Redis;

namespace CommAPI.Controllers
{
    //[CommAuth]
    [ApiController]
    [Route("onelead/api/[controller]/[action]")]
    public class LeadPrioritizationController : ControllerBase
    {
        private readonly ILeadPrioritizationBLL objBLL;

        public LeadPrioritizationController(ILeadPrioritizationBLL prioritizationBLL)
        {
            objBLL = prioritizationBLL;
        }

        [HttpGet("{UserID}/{ProductID}")]
        public PriorityModel? GetOneLead(string UserID, string ProductID, string? Source="")
        {
            PriorityModel? priorityModel = null;
            
            string AgentId = Request != null ? Request.Headers["AgentId"] : string.Empty;
            if (CoreCommonMethods.IsValidString(AgentId) && CoreCommonMethods.IsValidString(ProductID))
                priorityModel = objBLL.GetOneLead(Convert.ToInt64(AgentId), Convert.ToInt16(ProductID), Source);


            return priorityModel;
        }

        [HttpGet("{UserId}/{ProductId}")]
        public List<PriorityModel>? GetAgentAllLeads(string UserId, string ProductId)
        {
            List<PriorityModel>? priorityModel = null;
            string AgentId = Request != null ? Request.Headers["AgentId"] : string.Empty;
            string source = Request != null ? Request.Headers["source"] : string.Empty;
            if (CoreCommonMethods.IsValidString(AgentId) && CoreCommonMethods.IsValidString(ProductId))
            {

                bool showCallableLeads = Convert.ToBoolean(HttpContext.Request.Query["showCallableLeads"]);
                priorityModel = objBLL.GetAgentAllLeads(Convert.ToInt64(AgentId), Convert.ToInt16(ProductId), showCallableLeads, source);
            }
            return priorityModel;
        }

        [HttpPost]
        public bool MarkRetentionLeads(List<PriorityModel> leads)
        {
            bool status = false;
            if (leads != null && leads.Count > 0)
            {
                status = objBLL.MarkRetentionLeads(leads);
            }

            return status;
        }
        [HttpGet("{LeadId}")]
        public string GetDNCDetails(string LeadId)
        {
            if (string.IsNullOrEmpty(LeadId))
                return "";

            return objBLL.GetDNCDetails(Convert.ToInt64(LeadId));
        }
        //Called by Dialer Team at the action of "agentAnswered" and "hangup"
        [HttpGet]
        public bool InsertCallData(string CallID, string LeadID, string? AgentID, string? Duration, string? talkTime, string? status, string? callDate, string? callType, string? Context, string? Disposition, string? uid, string? dst, string? channel, string? action, string? hanguptime, string? IP, string? callRequestType, string? IsBMS, string? recfile, string? t_type, string? CallingNo, string? rectype)
        {
            bool result = false;
            DateTime dtreq = DateTime.Now;
            try
            {
                result = objBLL.PushCallDatainQueue(CallID, LeadID, AgentID, Duration, talkTime, status, callDate, callType, Context, Disposition, uid, dst, channel, action, hanguptime, IP, callRequestType, IsBMS, recfile, t_type, CallingNo, rectype);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(CallID, Convert.ToInt64(LeadID), ex.ToString(), "InsertCallData", "LeadPrioritizationController", "OneLeadPriority", string.Empty, string.Empty, dtreq, DateTime.Now);
            }
            return result;
        }
        [HttpPost]
        public AddLeadValidation ValidateAddLeadToPriorityQueue(UserNext5Leads priorityLead)
        {
            AddLeadValidation result = new AddLeadValidation();
            DateTime dtreq = DateTime.Now;
            try
            {
                if (priorityLead != null && priorityLead.Leads[0].LeadId > 0)
                {
                    return objBLL.ValidateAddLeadToPriorityQueue(priorityLead);
                }
                else
                {
                    result.message = "Invalid LeadId";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(priorityLead.Leads[0].LeadId),
                    priorityLead.Leads[0].LeadId, ex.ToString(),
                    "ValidateAddLeadToPriorityQueue", "OneLeadPriority", "LeadPrioritizationController",
                     JsonConvert.SerializeObject(priorityLead), JsonConvert.SerializeObject(result),
                    dtreq, DateTime.Now);
            }
            return result;
        }
        [HttpGet("{UserID}")]
        public int GetReleaseLeadsCount(Int64 UserID)
        {
            string AgentId = Request != null ? Request.Headers["AgentId"] : string.Empty;
            if (!string.IsNullOrEmpty(AgentId))
                UserID = Convert.ToInt64(AgentId);
            return objBLL.GetReleaseLeadsCount(UserID);
        }
        [HttpGet("{LeadId}/{SubStatusID}/{UserID}")]
        public bool INSERTLeadReopenTrack(long LeadId, int SubStatusID, Int64 UserID)
        {
            return objBLL.INSERTLeadReopenTrack(LeadId, SubStatusID, UserID);
        }
        [HttpPost]
        public void ReleaseRequest(ReleaseLeadRequest objReleaseLeadRequest)
        {
            objBLL.ReleaseRequest(objReleaseLeadRequest);
        }
        [HttpGet("{LeadID}/{ProductID}")]
        public UnAnsweredSummary GetUnAnsweredSummary(string LeadID, string ProductID)
        {
            return objBLL.GetUnAnsweredSummary(LeadID, ProductID);
        }
        [HttpGet("{LeadId}")]
        public ResponseAPI IsCallBackAllowed(string LeadId, string? CBTime)
        {
            return objBLL.IsCallBackAllowed(LeadId, CBTime);
        }
        [HttpGet("{UserId}/{LastDays}")]
        public List<NotContactedLeads> GetNotContactedLeads(string UserId, string LastDays)
        {
            return objBLL.GetNotContactedLeads(UserId, LastDays);
        }

        [HttpPost]
        public ResponseAPI SkipCustomer(SkipCustomerRequest skipCustomerRequest)
        {
            string AgentId = Request != null ? Request.Headers["AgentId"] : string.Empty;
            if (!string.IsNullOrEmpty(AgentId))
                skipCustomerRequest.UserId = Convert.ToInt64(AgentId);

            ResponseAPI response = new()
            {
                message = "",
                status = false
            };

            if ((skipCustomerRequest.SkipDuration == null || skipCustomerRequest.SkipDuration == 0) && string.IsNullOrEmpty(skipCustomerRequest.SkipReason))
            {
                response.message = "SkipReason or SkipDuration is required";
                response.status = false;
            }
            else if ((skipCustomerRequest.SkipDuration != null && skipCustomerRequest.SkipDuration != 0) && !string.IsNullOrEmpty(skipCustomerRequest.SkipReason))
            {
                response.message = "Either SkipReason or SkipDuration could be provided, not both.";
                response.status = false;
            }
            else if (skipCustomerRequest.CustID == 0 && skipCustomerRequest.ExcludeLeadId == 0)
            {
                response.message = "CustID or ExcludeLeadId is required";
                response.status = false;
            }
            else
            {
                response.message = "";
                response.status = objBLL.SkipCustomer(skipCustomerRequest);
            }

            return response;
        }
        [HttpPost]
        public bool PushDncData(PriorityModel oPriorityModel)
        {
            return objBLL.PushDncData(oPriorityModel);
        }
        [HttpGet("{LeadId}/{UserID}/{EmployeeID}")]
        public ResponseAPI CallRelease(string LeadId, string UserID, string EmployeeID)
        {
            return objBLL.CallRelease(LeadId,UserID,EmployeeID);
        }
        [HttpGet("{UserId}/{productID}/{RoleID}")]
        public List<ReleasePointData> GetReleaseLeads(string UserID, string productID, string RoleID)
        {
            return objBLL.GetReleaseLeads(UserID, productID, RoleID);
        }
        [HttpGet("{UserID}")]
        public List<PriorityModel> GetUserAssignedLeads(string UserID)
        {
            LeadPrioritizationBLL oLeadPrioritizationBLL = new LeadPrioritizationBLL();
            return oLeadPrioritizationBLL.GetUserAssignedLeads(Convert.ToInt64(UserID));
        }        
        [HttpGet("{UserId}")]
        public List<PriorityModel> GetStarLeads(string UserId)
        {
            LeadPrioritizationBLL oLeadPrioritizationBLL = new LeadPrioritizationBLL();
            return oLeadPrioritizationBLL.GetStarLeads(Convert.ToInt64(UserId));
        }
        [HttpGet("{UserId}/{productID}")]
        public List<PriorityModel> GetExpiringLeads(string UserId, string productID)
        {
            LeadPrioritizationBLL oLeadPrioritizationBLL = new LeadPrioritizationBLL();
            return oLeadPrioritizationBLL.GetExpiringLeads(Convert.ToInt64(UserId), Convert.ToByte(productID));
        }
        [HttpPost]
        public List<Int64> CheckLeadTimeZone(List<Int64> objLeadList)
        {
            LeadPrioritizationBLL oLeadPrioritizationBLL = new LeadPrioritizationBLL();
            return oLeadPrioritizationBLL.CheckLeadTimeZone(objLeadList);
        }
        [HttpGet("{UserId}")]
        public long GetTLCallingNo(string UserId)
        {
            LeadPrioritizationBLL oLeadPrioritizationBLL = new LeadPrioritizationBLL();
            return oLeadPrioritizationBLL.GetTLCallingNo(UserId);            
        }
        [HttpGet("{UserId}/{productID}")]
        public List<PriorityModel> GetLastCalledLeads(string UserId, string productID)
        {
            LeadPrioritizationBLL oLeadPrioritizationBLL = new LeadPrioritizationBLL();
            return oLeadPrioritizationBLL.GetLastCalledLeads(Convert.ToInt64(UserId), Convert.ToInt16(productID));
        }
        [HttpPost]
        public ResponseAPI StarLead(Int64 UserId, Int16 productID, Int64 LeadID, byte Star)
        {
            LeadPrioritizationBLL oLeadPrioritizationBLL = new LeadPrioritizationBLL();
            return oLeadPrioritizationBLL.StarLead(UserId, productID, LeadID, Star);
        }
        [HttpGet("{LeadId}/{SkipDuration}/{UserID}")]
        public bool SkipLead(string LeadId, string SkipDuration, string UserID, bool IsMins)
        {
            long leadId = 0;
            DateTime ReqTime = DateTime.Now;
           
            Int16 skipDur = Convert.ToInt16(SkipDuration);
            if (IsMins == false)
                skipDur = Convert.ToInt16(skipDur * 60);

            leadId = Convert.ToInt64(LeadId);
            if (leadId == 0)// some false call
                return true;
            LeadPrioritizationBLL oLeadPrioritizationBLL = new LeadPrioritizationBLL();

            return oLeadPrioritizationBLL.SkipLead(leadId, skipDur, Convert.ToInt64(UserID));
            
        }
        [HttpPost]
        public int SetRealTimeLeadStatus(LeadCallDetailsDTO _LeadCallDetailsDTO)
        {
            LeadPrioritizationBLL oLeadPrioritizationBLL = new LeadPrioritizationBLL();
            return oLeadPrioritizationBLL.SetRealTimeLeadStatus(_LeadCallDetailsDTO);
        }
        [HttpPost]
        public Boolean updateagentstatus(PredictiveAgentStatus obj, string onCall)
        {
            LeadPrioritizationBLL oLeadPrioritizationBLL = new LeadPrioritizationBLL();
            return oLeadPrioritizationBLL.updateagentstatus(obj, Convert.ToBoolean(onCall));
        }
        [HttpPost]
        public List<RemoveNext5Leads> GetInvalidLeadsFromNext5Leads([FromBody] RemoveNext5Leads obj)
        {
            return objBLL.GetInvalidLeadsFromNext5Leads(obj);
        }
        [HttpPost]
        public bool RemoveInvalidLeadsFromNext5Leads([FromBody] RemoveNext5Leads obj)
        {
            return objBLL.RemoveInvalidLeadsFromNext5Leads(obj);
        }

        [HttpPost]
        public ResponseAPI PushRenewalCallIntent([FromBody] RenewalIntentRequest request)
        {
            ResponseAPI response = new ResponseAPI();
            DateTime dtreq = DateTime.Now;
            
            try
            {
                // Validate request
                if (request == null || request.LeadId <= 0 || request.Data == null || request.Data.Count == 0)
                {
                    response.status = false;
                    response.message = "Invalid request. LeadId and at least one Category are required.";
                    return response;
                }
                
                bool success = objBLL.ProcessLeadByCategory(request);
                
                response.status = success;
                response.message = success ? "Renewal intent processed successfully." : "Failed to process renewal intent.";
            }
            catch (Exception ex)
            {
                response.status = false;
                response.message = "Error processing renewal intent.";
                LoggingHelper.LoggingHelper.AddloginQueue(
                    request.LeadId.ToString(), 
                    request.LeadId, 
                    ex.ToString(),
                    "PushRenewalCallIntent", 
                    "LeadPrioritizationController", 
                    "OneLeadPriority",
                    JsonConvert.SerializeObject(request), 
                    JsonConvert.SerializeObject(response),
                    dtreq, 
                    DateTime.Now
                );
            }
            
            return response;
        }
    }
}

