﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "ValidHeaders": [ "agentid", "asterisktoken" ],
  "ValidOrigins": [ "https://progressiveqa.policybazaar.com", "localhost", "https://mobilematrix.policybazaar.com", "https://matrix.policybazaar.com", "https://matrixliveapi.policybazaar.com", "https://mobilematrixapi.policybazaar.com", "https://claim.policybazaar.com", "https://pbsupport.policybazaar.com", "https://matrixdashboard.policybazaar.com", "https://verification.policybazaar.com", "https://bms.policybazaar.ae", "https://pbmeet.policybazaar.com" ],
  "AllowedHosts": "*",
  "Communication": {
    "Environment": "LIVE",

    "ConnectionString": {
      "DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;Application Name=OneLeadMatrix;",
      "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=PBCROMA;User Id=pbcroma;Password=*****************;Application Name=OneLeadMatrix;",
      //"LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=BackofficeSys;Password=***********;MultiSubnetFailover=True;",
      "LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=Matrix_APP;Password=******$@!T@ad#@;MultiSubnetFailover=True;Application Name=OneLeadMatrix;"
    },

    "ProductDBConnection": {
      "DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;Application Name=OneLeadMatrix;",
      "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=PBCROMA;User Id=pbcroma;Password=*****************;Application Name=OneLeadMatrix;",
      "LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=BackofficeSys;Password=***********;MultiSubnetFailover=True;Application Name=OneLeadMatrix;"
    },
    "ReplicaConnectionString": {
      "DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;Application Name=OneLeadMatrix;",
      "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=PBCROMA;User Id=pbcroma;Password=*****************;Application Name=OneLeadMatrix;",
      "LIVE1": "Data Source=PBSQL-PROD-SEC2.etechaces.com;MultisubnetFailover=True;Database=PBCROMA;user Id=Matrix_APP;Password=******$@!T@ad#@;MultiSubnetFailover=True;Application Name=OneLeadMatrix;",
      "LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=Matrix_APP;Password=******$@!T@ad#@;MultiSubnetFailover=True;Application Name=OneLeadMatrix;"
    },
    "RedisConnection": {
      "DEV": "matrixredis-qa-new-001.matrixredis-qa-new.kskv7l.aps1.cache.amazonaws.com:6379",
      "UAT": "matrixredis-qa-new-001.matrixredis-qa-new.kskv7l.aps1.cache.amazonaws.com:6379",
      "LIVE": "master.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379,replica.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379"
    },
    "MatrixRedisConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "matrixredis-qa-new-001.matrixredis-qa-new.kskv7l.aps1.cache.amazonaws.com:6379",
      // "LIVE": "matrixprod.kskv7l.ng.0001.aps1.cache.amazonaws.com:6379,matrixprod-ro.kskv7l.ng.0001.aps1.cache.amazonaws.com:6379"
      //"LIVE": "customerrevist.kskv7l.ng.0001.aps1.cache.amazonaws.com:6379,customerrevist-ro.kskv7l.ng.0001.aps1.cache.amazonaws.com:6379"
      "LIVE": "master.matrixonelead-new.kskv7l.aps1.cache.amazonaws.com:6379,replica.matrixonelead-new.kskv7l.aps1.cache.amazonaws.com: 6379"
    },
    "MongoDBConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "mongodb://oneLeadUsr:0neLea98jnjdhg3!@20.80.40.117:27017/?authSource=oneLeadDB;connectTimeoutms=8000;socketTimeoutMS=8000",
      "LIVE": "***************************************************************************************************************************;replicaSet=rs3;readPreference=secondary;connectTimeoutms=8000;socketTimeoutMS=8000"


    },
    "MongoGridFSConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "*********************************************************************************;connectTimeoutms=40000&amp;socketTimeoutMS=8000",
      "LIVE": "master.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379,replica.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379"
    },
    "MongoLogDBConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "**********************************************************************;connectTimeoutms=8000;socketTimeoutMS=8000",
      "LIVE": "************************************************************************************************************************************************************************************************"

    },
    "ChatDBConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "************************************************************************",
      "LIVE": "master.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379,replica.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379"
    },
    "OneLeadDBConnection": {
      "DEV": "******************************************************************",
      "UAT": "mongodb://oneLeadUsr:0neLea98jnjdhg3!@20.80.40.117:27017/?authSource=oneLeadDB;connectTimeoutms=8000;socketTimeoutMS=8000",
      //"UAT": "****************************************************************************",
      //"UAT": "*****************************************************************************************************************",

      "LIVE": "***********************************************************************************************************************************************************************************************************************************************"
    },
    "RealTimeDBConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "*******************************************************************",
      "LIVE": "master.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379,replica.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379"
    },
    "KafkaBootstrapServers": {
      "DEV": "20.80.73.72:9092",
      "UAT": "20.80.73.72:9092",
      "LIVE": "10.80.25.42:9092,10.80.25.101:9092,10.80.25.140:9092"
    },
    "KafkaUserName": {
      "DEV": "bmskfk",
      "UAT": "bmskfk",
      "LIVE": "bmskfk"
    },
    "KafkaPassword": {
      "DEV": "bkls76298764",
      "UAT": "bkls76298764",
      "LIVE": "bms76298764"
    },
    "AWSSecretEnvironment": {
      "DEV": "QA_Matrix",
      "UAT": "QA_Matrix",
      "LIVE": "Prod_Matrix"
    },
    "IsAWSSceretEnabled": {
      "DEV": "true",
      "UAT": "true",
      "LIVE": "true"
    },
    "RedisPassword": {
      "DEV": "hfS4X265vASb83F1LKags7BV",
      "UAT": "hfS4X265vASb83F1LKags7BV",
      "LIVE": "Fyt31c7B8gsS8B6a65vKJ"
    },
    "MatrixRedisPassword": {
      "DEV": "hfS4X265vASb83F1LKags7BV",
      "UAT": "hfS4X265vASb83F1LKags7BV",
      "LIVE": "kjbdA45B189HGhs09asnBV"
    },

    "RealTimeDB": "RealTimeDB",
    "MongoDBCommunicationDB": "communicationD",
    "MongoGridFSDB": "communicationD",
    "MongoDBLogging": "logger",
    "ChatDB": "rocketchat_test",
    "ChatDBcommon": "rocketchat_test",
    "OneLeadDB": "oneLeadDB",
    "SaltKey": "PSVJQRk9QTEpNVU1DWUZCRVFGV1VVT0ZOV1RRU1NaWQ=",
    "InitializationVectorKey": "WVdsRkxWRVpaVUZOYVdsaA==",
    "SQSLogingQueueUrl": "https://sqs.ap-south-1.amazonaws.com/721537467949/LoggingQueue",
    "CJ_Key": "PB9MUI18RLIghJ2NA6TH0JI73U1NaWQASASDADADAAS=",
    "CJ_IV": "YUJAGTH1MH3AYT9UAKEA0YLWISDDSDDASASDASDAASD=",
    "ConfigKey": "OneLeadConfigValues"

  }

}
