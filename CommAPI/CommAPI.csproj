<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <ThreadPoolMinThreads>2000</ThreadPoolMinThreads>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.2.3" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.9" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
    <None Remove="Helpers\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\PropertyLayers\PropertyLayers.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\CommunicationBLL\CommunicationBLL.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\Helper\Helper.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Update="ConfigValues.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="QA_ConfigValues.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Helpers\" />
  </ItemGroup>
</Project>
