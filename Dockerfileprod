FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build-env
WORKDIR /app

# Copy csproj and restore as distinct layers
COPY . ./
WORKDIR /app/CommAPI
RUN dotnet restore

# Copy everything else and build
RUN dotnet publish -c Release -o out

# Build runtime image
FROM mcr.microsoft.com/dotnet/aspnet:6.0
RUN cp /usr/share/zoneinfo/Asia/Kolkata /etc/localtime	
RUN rm -rf /etc/localtime	
RUN ln -s /usr/share/zoneinfo/Asia/Kolkata /etc/localtime
# Install the agent
RUN apt-get update && apt-get install -y wget ca-certificates gnupg \
&& echo 'deb http://apt.newrelic.com/debian/ newrelic non-free' | tee /etc/apt/sources.list.d/newrelic.list \
&& wget https://download.newrelic.com/548C16BF.gpg \
&& apt-key add 548C16BF.gpg \
&& apt-get update \
&& apt-get install -y newrelic-dotnet-agent \
&& rm -rf /var/lib/apt/lists/*
WORKDIR /app
#RUN mkdir AppData
COPY --from=build-env /app/CommAPI/out .
ENTRYPOINT ["dotnet", "CommAPI.dll"]
