﻿using Helper;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using ReadXmlProject;
using System;
using System.Net;
using System.Runtime.Caching;
using System.Text;

namespace DataAccessLibrary
{
    public class ConnectionClass
    {
        static readonly object _obj = new();
        public static string LivesqlConnection()
        {
            DateTime dt = DateTime.Now;
            string ConnectionObj;
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            string Key = string.Empty;

            try
            {
                Key = $"{RedisCollection.LiveWebConfig()}";
                ConnectionObj = getSecretKey("LivesqlConnection");
                ConnectionObj = ConnectionObj.EndsWith(";") ? ConnectionObj + "Application Name=OneLeadMatrix;" : ConnectionObj + ";Application Name=OneLeadMatrix;";

            }
            catch (Exception ex)
            {
                string Enviornment = CoreCommonMethods.GetEnvironmentVar();
                ConnectionObj = con.GetSection("Communication").GetSection("ConnectionString").GetSection(Enviornment).Value.ToString();
                CommonCache.GetOrInsertIntoCache(ConnectionObj, Key, 720);
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "LivesqlConnection", "OneLead", "AmazonSecret", "", "", dt, DateTime.Now);
            }
            return ConnectionObj;
        }

        public static string ReplicasqlConnection()
        {

            string ConnectionObj;
            DateTime Requesttime = DateTime.Now;
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            string Key = string.Empty;

            try
            {
                Key = $"{RedisCollection.ReplicaWebConfig()}";
                ConnectionObj = getSecretKey("ReplicaConnectionString");
                ConnectionObj = ConnectionObj.EndsWith(";") ? ConnectionObj + "Application Name=OneLeadMatrix;" : ConnectionObj + ";Application Name=OneLeadMatrix;";

            }
            catch (Exception ex)
            {
                string Enviornment = CoreCommonMethods.GetEnvironmentVar();
                ConnectionObj = con.GetSection("Communication").GetSection("ReplicaConnectionString").GetSection(Enviornment).Value.ToString();
                CommonCache.GetOrInsertIntoCache(ConnectionObj, Key, 720);
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "ReplicaConnectionString", "OneLead", "AmazonSecret", "", "", Requesttime, DateTime.Now);
            }

            return ConnectionObj;
        }

        public static string ProductDBsqlConnection()
        {
            string Enviornment = string.Empty;
            string _con = string.Empty;
            string DbConnection = string.Empty;
            IConfiguration con = Custom.ConfigurationManager.AppSetting;

            Enviornment = CoreCommonMethods.GetEnvironmentVar();

            DbConnection = con.GetSection("Communication").GetSection("ProductDBConnection").GetSection(Enviornment).Value.ToString();
            return DbConnection;
        }

        public static string getSecretKey(string MethodName)
        {
            string ConnectionObj = string.Empty;
            string Connectionstring = string.Empty;
            StringBuilder sb = new StringBuilder();
            bool logEnabled = false;
            DateTime Requesttime = DateTime.Now;
            string LogMethod = "getSecretKey";
            IConfiguration con = Custom.ConfigurationManager.AppSetting;


            string IsAWSSceretEnabled = "IsAWSSceretEnabled".AppSettings();
            if (CoreCommonMethods.IsValidString(IsAWSSceretEnabled) && Convert.ToBoolean(IsAWSSceretEnabled))
            {
                string Key = $"{RedisCollection.AWSSceret()}";
                string LivewebKey = $"{RedisCollection.LiveWebConfig()}";
                string ReplicawebKey = $"{RedisCollection.ReplicaWebConfig()}";

                if (MemoryCache.Default[Key] != null)
                {
                    ConnectionObj = Convert.ToString(MemoryCache.Default[Key]);
                }
                else if (MethodName.ToLower() == "replicaconnectionstring" && MemoryCache.Default[ReplicawebKey] != null)
                {
                    ConnectionObj = Convert.ToString(MemoryCache.Default[ReplicawebKey]);
                    return ConnectionObj;
                }
                else if (MethodName.ToLower() == "livesqlconnection" && MemoryCache.Default[LivewebKey] != null)
                {
                    ConnectionObj = Convert.ToString(MemoryCache.Default[LivewebKey]);
                    return ConnectionObj;
                }
                else
                {
                    lock (_obj)
                    {
                        if (MemoryCache.Default[Key] != null)
                        {
                            ConnectionObj = Convert.ToString(MemoryCache.Default[Key]);
                        }
                        else
                        {

                            logEnabled = true;
                            LogMethod = "ReadSecret";
                            sb.Append(" ==== read secret === ");
                            sb.Append(" ==== IPAddress === " + GetIPAddress());

                            string SecretEnvironment = "SecretEnvironment".AppSettings();
                            sb.Append(" ,SecretEnvironment" + SecretEnvironment);

                            if (string.IsNullOrEmpty(SecretEnvironment))
                                SecretEnvironment = "QA_Matrix";

                            ConnectionObj = AmazonSecret.GetSecret(SecretEnvironment);

                            if (CoreCommonMethods.IsValidString(ConnectionObj))
                            {
                                sb.Append(" ====  Insert Cache=== ");
                                CommonCache.GetOrInsertIntoCache(ConnectionObj, Key, 720);
                            }

                        }
                    }

                }

                if (!string.IsNullOrEmpty(ConnectionObj))
                {
                    sb.Append("=========Enter ConnectionObj  is not empty===========");
                    dynamic obj = JsonConvert.DeserializeObject(ConnectionObj);
                    if (obj != null && obj.ProductionConnectionString != null)
                    {
                        if (MethodName.ToLower() == "replicaconnectionstring")
                            Connectionstring = obj.ReplicaConnectionString.Value;
                        else if (MethodName.ToLower() == "livesqlconnection")
                            Connectionstring = obj.ProductionConnectionString.Value;
                    }
                }
                else
                {
                    sb.Append("=========read database===========");
                    LogMethod = "ReadDataBase";
                    logEnabled = true;
                    string Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    Connectionstring = con.GetSection("Communication").GetSection("ConnectionString").GetSection(Enviornment).Value.ToString();
                }
            }
            else
            {
                sb.Append("=========Read DataBase ===========");
                LogMethod = "ReadDataBase";
                logEnabled = true;
                string Enviornment = CoreCommonMethods.GetEnvironmentVar();
                Connectionstring = con.GetSection("Communication").GetSection("ReplicaConnectionString").GetSection(Enviornment).Value.ToString();
            }


            if (logEnabled)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(string.Empty, 0, string.Empty, LogMethod, "OneLead", "", sb.ToString(), "", Requesttime, DateTime.Now);
            }

            return Connectionstring;

        }

        public static string GetIPAddress()
        {
            string ipaddress = string.Empty;
            try
            {
                string strHostName = System.Net.Dns.GetHostName();
                IPHostEntry ipHostInfo = Dns.GetHostEntry(strHostName);
                IPAddress ipAddress = ipHostInfo.AddressList[0];
                ipaddress = ipAddress.ToString();
            }
            catch (Exception ex)
            {

            }
            return ipaddress;
        }
    }
}
