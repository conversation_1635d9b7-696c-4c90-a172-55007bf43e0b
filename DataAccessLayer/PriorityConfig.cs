﻿using DataHelper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Runtime.Caching;
using System.Text;
using PropertyLayers;
using Redis;
using Helper;
using MongoDB.Driver;
using MongoDB.Driver.Builders;
using System.Collections;

namespace DataAccessLibrary
{
    public class PriorityConfig
    {
        public static LeadPriorityConstants getConfig()
        {
            LeadPriorityConstants objt = new LeadPriorityConstants();
            List<int> priorityGroup = new List<int>();
            try
            {
                string Enviornment = CoreCommonMethods.GetEnvironmentVar();
                string path = Environment.CurrentDirectory + "/LeadPriorityConstants.json";
                ObjectCache CacheConfig = MemoryCache.Default;
                objt = (LeadPriorityConstants)CacheConfig.Get("priorityconfig");
                if (objt == null)
                {
                    /*
                    string Key = RedisCollection.LeadPriorityConstants();
                    string ob = RedisHelper.GetRedisData(Key);
                    */
                    CacheItemPolicy objCachePolicies = new CacheItemPolicy();
                    objCachePolicies.ChangeMonitors.Add(new HostFileChangeMonitor(new List<string> { path }));
                    if (true)
                    {

                        var Json = System.IO.File.ReadAllText(path);
                        var data = JsonConvert.DeserializeObject<LeadPriorityConstants>(Json);

                        string Connectionstring = ConnectionClass.LivesqlConnection();
                        string strQuery = "SELECT UserGroupID from CRM.UserGroupMaster (NOLOCK) WHERE IsOneLead = 1";
                        SqlParameter[] SqlParam = new SqlParameter[0];
                        var obj = SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);
                        if (obj != null)
                        {
                            var r = obj.Tables[0].AsEnumerable().Select(p => p.Field<Int16>("UserGroupID")).ToList();
                            data.PriorityGroups = r;
                        }

                        //------------For Substatus Id and Name------------------------------//

                        string str = "SELECT SubStatusID,SubStatusName from CRM.substatusmaster (NOLOCK) WHERE isactive=1";
                        SqlParameter[] _sqlParam = new SqlParameter[0];
                        var oSubStatus = SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.Text, str, _sqlParam);
                        if (oSubStatus != null)
                        {
                            var ab = oSubStatus.Tables[0].AsEnumerable().ToDictionary<DataRow, Int16, string>(row => row.Field<Int16>("SubStatusID"),
                                row => row.Field<string>("SubStatusName"));
                            data.subStatusList = ab;
                        }

                        CacheConfig.Add("priorityconfig", data, objCachePolicies);
                        //RedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(data), new TimeSpan(12,0,0));
                        return data;
                    }
                    /*
                    else
                    {
                        objt = JsonConvert.DeserializeObject<LeadPriorityConstants>(ob);
                        CacheConfig.Add("priorityconfig", objt, objCachePolicies);
                        return objt;
                    }
                    */
                }
                else
                    return objt;
            }
            catch (Exception ex)
            {
                return objt;
            }
        }

        public static List<Engine> GetAllEnginesByProduct(int ProductId)
        {
            try
            {
                List<Engine> engines = new();
                string Key = $"{RedisCollection.LeadRankRE_Engine()}_{ProductId}";

                if (MemoryCache.Default[Key] != null)
                {
                    engines = (List<Engine>)MemoryCache.Default.Get(Key);
                }
                else
                {
                    MongoHelper OneLeadDb = new(SingletonClass.OneLeadDB());
                    IMongoSortBy _Sort = SortBy.Ascending("_id");
                    var query = Query.And(Query<Engine>.EQ(p => p.ProductId, ProductId), Query<Engine>.EQ(p => p.IsActive, 1));
                    engines = OneLeadDb.GetDocuments<Engine>(query, MongoCollection.LeadRankRE_Engines(), _Sort, 500).ToList();

                    if (engines.Count > 0)
                        CommonCache.GetOrInsertIntoCache(engines, Key, 60);
                }
                return engines;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public static List<AllocationRule> GetRulesForEngine(string engineName, int productId)
        {
            List<AllocationRule> rules = new();
            List<AllocationRule> SortedRules = new();
            List<Engine> engineList = GetAllEnginesByProduct(productId);
            Engine engine = engineList.Find((engine) => engine.EngineName == engineName);

            if (engine == null)
            {
                return SortedRules;
            }
            string Key = $"{RedisCollection.LeadRankRE_Rules()}:{engine._id}";

            if (MemoryCache.Default[Key] != null)
                SortedRules = (List<AllocationRule>)MemoryCache.Default.Get(Key);
            else
            {
                MongoHelper OneLeadDb = new(SingletonClass.OneLeadDB());
                IMongoSortBy _Sort = SortBy.Ascending("_id");
                var RuleFilter = Query.And(Query<AllocationRule>.In(p => p._id, engine.Rules), Query<AllocationRule>.EQ(p => p.IsActive, 1));
                rules = OneLeadDb.GetDocuments<AllocationRule>(RuleFilter, MongoCollection.LeadRankRE_Rules(), _Sort, 2000).ToList();

                engine.Rules.ForEach((ruleId) =>
                {
                    var matchingRule = rules.Find(rule => rule._id == ruleId);
                    if (matchingRule != null)
                    {
                        SortedRules.Add(matchingRule);
                    }
                });

                if (SortedRules.Count > 0)
                    CommonCache.GetOrInsertIntoCache(SortedRules, Key, 1 * 60);
            }
            return SortedRules;
        }
    }
}
