﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.IO;
using System.Net;
using PropertyLayers;
namespace DataAccessLibrary
{
    public class CommonAPICall
    {
        public static string getAPICall(string URL, int timeout)
        {
            if (string.IsNullOrEmpty(URL))
                return string.Empty;

            System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
            client.Timeout = TimeSpan.FromMilliseconds(timeout);
            var response = client.GetAsync(URL).Result;
            return response.Content.ReadAsStringAsync().Result;
        }
        public static string getAPICall(string URL, int timeout, string AuthKey)
        {
            System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
            client.Timeout = TimeSpan.FromMilliseconds(timeout);
            if (!string.IsNullOrEmpty(AuthKey))
                client.DefaultRequestHeaders.Add("Authorization", AuthKey);
            var response = client.GetAsync(URL).Result;
            return response.Content.ReadAsStringAsync().Result;
        }

        public static void getAPICallAsync(string URL, int timeout, string AuthKey)
        {
            System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
            client.Timeout = TimeSpan.FromMilliseconds(timeout);
            if (!string.IsNullOrEmpty(AuthKey))
                client.DefaultRequestHeaders.Add("Authorization", AuthKey);
            client.GetAsync(URL);
        }

        public static string DialerAgentRTSCheck(string URL1, string URL2, int timeout)
        {
            System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
            client.Timeout = TimeSpan.FromMilliseconds(timeout);
            var response = client.GetAsync(URL1).Result;
            response = client.GetAsync(URL2).Result;
            return response.Content.ReadAsStringAsync().Result;
        }

        public static void PostAPICall(string URL, int timeout, string Json, string AuthKey, Dictionary<string, string> objHeaders = null)
        {
            if (string.IsNullOrEmpty(URL))
                return;

            System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
            client.Timeout = TimeSpan.FromMilliseconds(timeout);
            StringContent content = new StringContent(Json, Encoding.UTF8, "application/json");
            if (!string.IsNullOrEmpty(AuthKey))
                client.DefaultRequestHeaders.Add("Authorization", AuthKey);
            else if (objHeaders != null && objHeaders.Count > 0)
            {
                foreach (var key in objHeaders.Keys)
                {
                    client.DefaultRequestHeaders.Add(key, objHeaders[key]);
                }
            }
            client.PostAsync(URL, content);
        }
        public static void PostAPICall_Sync(string URL, int timeout, string Json, string AuthKey)
        {
            if (string.IsNullOrEmpty(URL))
                return;

            System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
            client.Timeout = TimeSpan.FromMilliseconds(timeout);
            StringContent content = new StringContent(Json, Encoding.UTF8, "application/json");
            if (!string.IsNullOrEmpty(AuthKey))
                client.DefaultRequestHeaders.Add("Authorization", AuthKey);
            var r = client.PostAsync(URL, content).Result;
        }

        public static void getAPICallAsync(string URL, int timeout)
        {
            if (!string.IsNullOrEmpty(URL))
            {
                System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
                client.Timeout = TimeSpan.FromMilliseconds(timeout);
                client.GetAsync(URL);
            }
        }

        public static string PostAPICallWithResult(string URL, int timeout, string Json, string AuthKey, Dictionary<string, string> objHeaders = null, string keyname = null)
        {
            string result = string.Empty;
            DateTime reqTime = DateTime.Now;
            try
            {
                if (string.IsNullOrEmpty(URL))
                    return string.Empty;
                System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
                client.Timeout = TimeSpan.FromMilliseconds(timeout);
                StringContent content = new StringContent(Json, Encoding.UTF8, "application/json");
                if (!string.IsNullOrEmpty(AuthKey))
                    client.DefaultRequestHeaders.Add("Authorization", AuthKey);
                else if (objHeaders != null && objHeaders.Count > 0)
                {
                    foreach (var key in objHeaders.Keys)
                    {
                        client.DefaultRequestHeaders.Add(key, objHeaders[key]);
                    }
                }
                HttpResponseMessage response;
                response = client.PostAsync(URL, content).Result;
                using (Stream stream = response.Content.ReadAsStreamAsync().Result)
                {
                    StreamReader reader = new StreamReader(stream, Encoding.UTF8);
                    result = reader.ReadToEnd();
                    if (reader != null) reader.Close();
                }
            }
            catch (Exception ex)
            {
                var test = ex.StackTrace.ToString();
                try { test = ex.ToString(); } catch { }
                LoggingHelper.LoggingHelper.AddloginQueue(keyname, 0, test, "PostAPICallWithResult", "MatrixCore", "CommonAPICall", Json, URL, reqTime, DateTime.Now);
            }
            return result;
        }
        public static string getAPICallWithResult(string URL, int timeout, Dictionary<string, string> objHeaders = null)
        {
            System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
            client.Timeout = TimeSpan.FromMilliseconds(timeout);
            if (objHeaders != null && objHeaders.Count > 0)
            {
                foreach (var key in objHeaders.Keys)
                {
                    client.DefaultRequestHeaders.Add(key, objHeaders[key]);
                }
            }
            var response = client.GetAsync(URL).Result;
            return response.Content.ReadAsStringAsync().Result;
        }

        public static string PostAPICallWithResult_FormData(string URL, int timeout, string Json, string AuthKey)
        {
            string result = string.Empty;
            try
            {
                if (string.IsNullOrEmpty(URL))
                    return string.Empty;

                //var postData = "From=**********";
                //postData += "&To=**********";
                //postData += "&CallerId=***********";
                var nvc = new List<KeyValuePair<string, string>>();
                nvc.Add(new KeyValuePair<string, string>("From", "**********"));
                nvc.Add(new KeyValuePair<string, string>("To", "**********"));
                nvc.Add(new KeyValuePair<string, string>("CallerId", "***********"));

                var client = new HttpClient();
                client.BaseAddress = new Uri("https://policybazaar4:<EMAIL>");
                var request = new HttpRequestMessage(HttpMethod.Post, "/v1/Accounts/policybazaar4/Calls/connect");

                request.Content = new FormUrlEncodedContent(nvc);
                var response = client.SendAsync(request).Result;

                //var request = (HttpWebRequest)WebRequest.Create(Uri.EscapeUriString(URL));
                //var postData = "From=**********";
                //postData += "&To=**********";
                //postData += "&CallerId=***********";
                //var data = Encoding.ASCII.GetBytes(postData);

                //request.Method = "POST";
                //request.ContentType = "application/x-www-form-urlencoded";
                //request.ContentLength = data.Length;

                //using (var stream = request.GetRequestStream())
                //{
                //    stream.Write(data, 0, data.Length);
                //}

                //var response = (HttpWebResponse)request.GetResponse();

                //var responseString = new StreamReader(response.GetResponseStream()).ReadToEnd();

                /*
                var nvc = new List<KeyValuePair<string, string>>();
                nvc.Add(new KeyValuePair<string, string>("From", "7042249079"));
                nvc.Add(new KeyValuePair<string, string>("To", "**********"));
                nvc.Add(new KeyValuePair<string, string>("CallerId", "***********"));
                var client = new HttpClient();
                var req = new HttpRequestMessage(HttpMethod.Post, URL) { Content = new FormUrlEncodedContent(nvc) };
                var res = client.SendAsync(req);*/


                //System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
                //client.Timeout = TimeSpan.FromMilliseconds(timeout);
                //StringContent content = new StringContent(Json, Encoding.UTF8, "application/x-www-form-urlencoded");
                //if (!string.IsNullOrEmpty(AuthKey))
                //    client.DefaultRequestHeaders.Add("Authorization", AuthKey);

                //HttpResponseMessage response;
                //response = client.PostAsync(URL, content).Result;
                //using (Stream stream = response.Content.ReadAsStreamAsync().Result)
                //{
                //    StreamReader reader = new StreamReader(stream, Encoding.UTF8);
                //    result = reader.ReadToEnd();
                //    if (reader != null) reader.Close();
                //}
            }
            catch (Exception ex)
            {

            }
            return result;
        }

        public static string PostAPICall_Sync(string URL, int timeout, string Json, string AuthHeader, string AuthKey)
        {
            if (string.IsNullOrEmpty(URL))
                return string.Empty;

            System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
            client.Timeout = TimeSpan.FromMilliseconds(timeout);
            StringContent content = new StringContent(Json, Encoding.UTF8, "application/json");
            if (!string.IsNullOrEmpty(AuthKey))
                client.DefaultRequestHeaders.Add(AuthHeader, AuthKey);
            var response = client.PostAsync(URL, content).Result;
            return response.Content.ReadAsStringAsync().Result;
        }

        public static string PostAPICall_Sync(string URL, int timeout, string Json, Dictionary<string, string> AuthHeader)
        {
            if (string.IsNullOrEmpty(URL))
                return string.Empty;

            System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
            client.Timeout = TimeSpan.FromMilliseconds(timeout);

            StringContent content = new StringContent(Json, Encoding.UTF8, "application/json");
            if (AuthHeader != null && AuthHeader.Count > 0)
            {
                foreach (KeyValuePair<string, string> item in AuthHeader)
                {
                    client.DefaultRequestHeaders.Add(item.Key, item.Value);
                }
            }

            var response = client.PostAsync(URL, content).Result;
            return response.Content.ReadAsStringAsync().Result;
        }

        public static string PostApiCallSync(string url, int timeout, MultipartFormDataContent content, Dictionary<string, string> headers)
        {
            if (string.IsNullOrEmpty(url))
                return string.Empty;

            var client = new HttpClient()
            {
                Timeout = TimeSpan.FromMilliseconds(timeout)
            };

            if (headers != null && headers.Count > 0)
            {
                foreach (KeyValuePair<string, string> item in headers)
                {
                    client.DefaultRequestHeaders.Add(item.Key, item.Value);
                }
            }

            var response = client.PostAsync(url, content).Result;
            return response.Content.ReadAsStringAsync().Result;
        }

        public static string PutAPICall_Aswat(string URL, int timeout, string Json, Dictionary<string, string> AuthHeader)
        {
            if (string.IsNullOrEmpty(URL))
                return string.Empty;

            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;

            System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
            client.Timeout = TimeSpan.FromMilliseconds(timeout);

            client.DefaultRequestHeaders.Accept.Clear();
            StringContent content = new StringContent(Json, Encoding.UTF8, "application/json");
            if (AuthHeader != null && AuthHeader.Count > 0)
            {
                foreach (KeyValuePair<string, string> item in AuthHeader)
                {
                    client.DefaultRequestHeaders.Add(item.Key, item.Value);
                }
            }


            var response = client.PutAsync(URL, null);

            response.Wait();
            var result = response.Result;
            if (result.IsSuccessStatusCode)
            {
                return result.Content.ReadAsStringAsync().Result;
            }
            return "";
        }

        public static string getAPICall_Aswat(string URL, int timeout, string Json, Dictionary<string, string> AuthHeader)
        {
            if (string.IsNullOrEmpty(URL))
                return string.Empty;

            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;

            System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
            client.Timeout = TimeSpan.FromMilliseconds(timeout);

            client.DefaultRequestHeaders.Accept.Clear();
            StringContent content = new StringContent(Json, Encoding.UTF8, "application/json");
            if (AuthHeader != null && AuthHeader.Count > 0)
            {
                foreach (KeyValuePair<string, string> item in AuthHeader)
                {
                    client.DefaultRequestHeaders.Add(item.Key, item.Value);
                }
            }


            var response = client.GetAsync(URL);

            response.Wait();
            var result = response.Result;
            if (result.IsSuccessStatusCode)
            {
                return result.Content.ReadAsStringAsync().Result;
            }
            return result.StatusCode.ToString();
        }

        public static string PostAPICall_Aswat(string URL, int timeout, string Json, Dictionary<string, string> AuthHeader)
        {
            try
            {
                if (string.IsNullOrEmpty(URL))
                    return string.Empty;

                ServicePointManager.Expect100Continue = true;
                ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;

                System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
                client.Timeout = TimeSpan.FromMilliseconds(timeout);

                client.DefaultRequestHeaders.Accept.Clear();
                StringContent content = new StringContent(Json, Encoding.UTF8, "application/json");
                if (AuthHeader != null && AuthHeader.Count > 0)
                {
                    foreach (KeyValuePair<string, string> item in AuthHeader)
                    {
                        client.DefaultRequestHeaders.Add(item.Key, item.Value);
                    }
                }


                var response = client.PostAsync(URL, null);

                response.Wait();
                var result = response.Result;
                if (result.IsSuccessStatusCode)
                {
                    string res = result.Content.ReadAsStringAsync().Result;
                    return res;
                }
                return result.StatusCode.ToString();
            }
            catch (Exception ex)
            {
                return ex.ToString();
            }
        }

        
        public static string WebRequestMethod(string requestUrl, string JSONRequest, string JSONmethod, int timeout, string JSONContentType, Dictionary<object, object> Headers = null)
        {
            //string _requestString = string.Empty;
            string _objRespons = string.Empty;
            StringBuilder oStringBuilder = new StringBuilder();
            try
            {
                ServicePointManager.Expect100Continue = true;
                ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;
                HttpWebRequest request = (HttpWebRequest) HttpWebRequest.Create(requestUrl);
                request.Timeout = timeout;
                request.Method = JSONmethod;
                request.ContentType = JSONContentType; // "application/json";
                if (Headers != null)
                {
                    foreach (var header in Headers)
                    {
                        request.Headers.Add(header.Key.ToString(), header.Value.ToString());
                    }
                }

                var encoding = new UTF8Encoding();
                if (JSONmethod == "POST")
                {

                    byte[] bytes = encoding.GetBytes(JSONRequest);
                    request.ContentLength = bytes.Length;
                    using (Stream requestStream = request.GetRequestStream())
                        requestStream.Write(bytes, 0, bytes.Length);
                }
                WebResponse webResponse = request.GetResponse();
                using (Stream webStream = webResponse.GetResponseStream())
                {
                    if (webStream != null)
                    {
                        using (StreamReader responseReader = new StreamReader(webStream))
                        {
                            _objRespons = responseReader.ReadToEnd();
                            return _objRespons;
                        }
                    }
                    return null;
                }
            }
            catch (Exception ex)
            {
                return ex.Message.ToString();
            }
        }

        public static string CallAPI(string requestUrl, string JSONRequest, string JSONmethod, int timeout, string JSONContentType, Dictionary<object, object> Headers = null)
        {
            //string _requestString = string.Empty;
            string _objRespons = string.Empty;
            StringBuilder oStringBuilder = new StringBuilder();

            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;
            HttpWebRequest request = (HttpWebRequest)HttpWebRequest.Create(requestUrl);
            request.Timeout = timeout;
            request.Method = JSONmethod;
            request.ContentType = JSONContentType; // "application/json";
            if (Headers != null)
            {
                foreach (var header in Headers)
                {
                    request.Headers.Add(header.Key.ToString(), header.Value.ToString());
                }
            }

            var encoding = new UTF8Encoding();
            if (JSONmethod == "POST")
            {

                byte[] bytes = encoding.GetBytes(JSONRequest);
                request.ContentLength = bytes.Length;
                using (Stream requestStream = request.GetRequestStream())
                    requestStream.Write(bytes, 0, bytes.Length);
            }
            WebResponse webResponse = request.GetResponse();
            using (Stream webStream = webResponse.GetResponseStream())
            {
                if (webStream != null)
                {
                    using (StreamReader responseReader = new StreamReader(webStream))
                    {
                        _objRespons = responseReader.ReadToEnd();
                        return _objRespons;
                    }
                }
                return null;
            }

        }
        public static string CallAPI_New(string requestUrl, string JSONRequest, string JSONmethod, int timeout, string JSONContentType, Dictionary<object, object> Headers = null)
        {
            //string _requestString = string.Empty;
            ServicePointManager.Expect100Continue = true;
            //ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            System.Net.Http.HttpClient client = new System.Net.Http.HttpClient();
            client.Timeout = TimeSpan.FromMilliseconds(timeout);            
            if (Headers != null)
            {
                foreach (var header in Headers)
                {
                    client.DefaultRequestHeaders.Add(header.Key.ToString(), header.Value.ToString());
                }
            }

            if (JSONmethod == "POST")
            {
                StringContent content = new StringContent(JSONRequest, Encoding.UTF8, JSONContentType);
                var response= client.PostAsync(requestUrl, content).Result;
                return response.Content.ReadAsStringAsync().Result;
            }
            else
            {
                var response = client.GetAsync(requestUrl).Result;
                return response.Content.ReadAsStringAsync().Result;
            }           

        }

    }
}
