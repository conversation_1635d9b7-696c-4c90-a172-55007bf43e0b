﻿using DataAccessLibrary;
using DataHelper;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Reflection.Emit;

namespace DataAccessLayer
{
    public class ChatData
    {
        public static LeadDetailResponse getLeadDetailsforChat(long LeadID)
        {
            DateTime Reqdt = DateTime.Now;
            try
            {
                SqlParameter[] sqlparm = new SqlParameter[1];
                sqlparm[0] = new SqlParameter("@LeadID", LeadID);
                var ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[getLeadCustomerDetails]", sqlparm);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    LeadData objLeadData = new LeadData();
                    objLeadData.LeadID = long.Parse(Convert.ToString(ds.Tables[0].Rows[0]["W_LeadID"]));
                    objLeadData.ParentLeadID = long.Parse(Convert.ToString(ds.Tables[0].Rows[0]["LeadID"])); // Parent Lead                                
                    if (!string.IsNullOrEmpty(Convert.ToString(ds.Tables[0].Rows[0]["CustomerID"])))
                    {
                        objLeadData.CustID = Convert.ToInt64(Convert.ToString(ds.Tables[0].Rows[0]["CustomerID"]));
                    }
                    objLeadData.CustomerName = CultureInfo.CurrentCulture.TextInfo.ToTitleCase(Convert.ToString(ds.Tables[0].Rows[0]["CustomerName"]).ToLower());
                    objLeadData.ProductID = Convert.ToInt16(ds.Tables[0].Rows[0]["ProductID"]);
                    
                    return new LeadDetailResponse() { LeadData = objLeadData };
                }
                else
                    return null;
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(LeadID), LeadID, ex.ToString(), "getLeadDetailsforChat", "ChatData", "OneLead", Convert.ToString(LeadID), "", Reqdt, DateTime.Now);
                return null;
            }
        }
    }
}
