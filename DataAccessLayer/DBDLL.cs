﻿using System;
using System.Data;
using DataAccessLibrary;
using DataHelper;
using System.Data.SqlClient;

namespace DataAccessLayer
{
	public class DBDLL
	{
		public static DataSet GetArchieveData()
		{
            SqlParameter[] sqlParam = Array.Empty<SqlParameter>();
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.GetDBArchieveSchedular", sqlParam);
        }

		public static DataSet GetArchieveTableRecords(string query)
		{
            SqlParameter[] sqlParam = Array.Empty<SqlParameter>();
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.Text, query, sqlParam);
        }

        public static int ExecuteNonQuery(string query)
        {
            SqlParameter[] sqlParam = Array.Empty<SqlParameter>();
            return SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.Text, query, sqlParam);
        }
    }
}

