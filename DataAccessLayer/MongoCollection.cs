﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using MongoDB.Driver.Linq;
using PropertyLayers;
namespace DataAccessLibrary
{
    public class MongoCollection
    {
        public static string TemplateCollection()
        {
            return "TemplateMaster";
        }
        public static string TicketCollection()
        {
            return "TicketDetails";
        }
        public static string TicketHistoryCollection()
        {
            return "TicketHistory";
        }
        public static string TicketAttachmentCollection()
        {
            return "TicketAttachements";
        }
        public static string TicketIssueMaster()
        {
            return "TicketIssueMaster";
        }
        public static string TransferCollection()
        {
            return "CallTransferData";
        }
        public static string LPDataCollection()
        {
            return "LPData";
        }
        public static string LastAssignedIDCollection()
        {
            return "LastAssignedID";
        }
        public static string CustProfileLog()
        {
            return "CustProfileLog";
        }
        public static string Settings()
        {
            return "Settings";
        }
        
        
        public static string ChatUserCollection()
        {
            return "users";
        }
        public static string ChatloginHistoryCollection()
        {
            return "rocketchat_loginhistory";
        }
        public static string ChatDepartmentCollection()
        {
            return "rocketchat_livechat_department";
        }
        public static string ChatAgentDepartmentCollection()
        {
            return "rocketchat_livechat_department_agents";
        }
        public static string ChatRoomCollection()
        {
            return "rocketchat_room";
        }
        public static string UsersCollection()
        {
            return "users";
        }
        public static string ChatMessageCollection()
        {
            return "rocketchat_message";
        }
        public static string ChatusersSessions()
        {
            return "usersSessions";
        }
        public static string ChatEnquiry()
        {
            return "rocketchat_livechat_inquiry";
        }
        public static string ChatSubscription()
        {
            return "rocketchat_subscription";
        }
        public static string ChatOfflineCollection()
        {
            return "rocketchat_OfflineMessage";
        }
        public static string InternalIps()
        {
            return "InternalIps";
        }

        public static string LeadPriorityPositionLog()
        {
            return "LPPositionLog";
        }
        public static string ChatWelcomeMessageCollection()
        {
            return "rocketchat_livechat_WelcomeMessage";
        }
        public static string ChatSettingsCollection()
        {
            return "rocketchat_settings";
        }
        public static string CJEventLogCollection()
        {
            return "CJEventLog";
        }

        internal static string WhatsAppMessageCollection()
        {
            return "WhatsAppMessage";
        }

        internal static string MessengerCollection()
        {

            return "MessengerChatMsgs";
        }        

        public static string UserNonWorkedLeads()
        {
            return "UserNonWorkedLeads";
        }

        public static string Next5Leads_OneLead()
        {
            return "Next5Leads_OneLead";
        }

        public static string ChatBotUserInput()
        {
            return "rocketchat_livechat_Chatbot_UserInput";
        }

        public static string ratingCollection()
        {
            return "rocketchat_rating";
        }

        public static string onlinecustomerTracking()
        {
            return "onlinecustomerTracking";
        }

        public static string ctcrevisitleadrankmapping()
        {
            return "ctcrevisitleadrankmapping";
        }

        public static string ctcrevisitleadqueue()
        {
            return "ctcrevisitleadqueue";
        }


        public static string RetainerPredictive()
        {
            return "RetainerPredictiveDialingData";
        }

        internal static string PredictiveAgentStatus()
        {
            return "PredictiveAgentStatus";
        }

        public static string ResetLPDataCollection()
        {
            return "ResetLPData";
        }

        public static string APIKeySettings()
        {
            return "APIKeySettings";
        }
        
        public static string customerRevisit()
        {
            return "customerRevisit";
        }

        public static string CustomerAuthenticate()
        {
            return "CustomerAuthenticate";
        }

        public static string ConfigValues()
        {
            return "APIKeySettings";
        }
        public static string ConfigFiles()
        {
            return "ConfigFiles";
        }

        public static string QuoteSharedTracking()
        {
            return "QuoteSharedTracking";
        }
        public static string ACLConfigValues()
        {
            return "APIKeySettings_ACL";
        }

        internal static string CustomerNANCAttempts()
        {
            return "CustomerNANCAttempts";
        }

        public static string LeadRankRE_Engines()
        {
            return "LeadRankRE_Engines";
        }

        public static string LeadRankRE_Rules()
        {
            return "LeadRankRE_Rules";
        }

        public static string HealthRenewalUnassistedPilotLeads()
        {
            return "HealthRenewalUnassistedPilotLeads";
        }

        internal static string RenewalCallAnalysis()
        {
            return "RenewalCallAnalysis";
        }
    }
}
