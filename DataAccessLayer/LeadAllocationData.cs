﻿using DataAccessLibrary;
using DataHelper;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Helper;
using PropertyLayers;
using Newtonsoft.Json;

namespace DataAccessLayer
{
    public class LeadAllocationData
    {
        public static int AssignLead(long LeadID, string EmpCode, Int16 CallType, Int64 AssignedBy = 124)
        {
            DateTime Reqdt = DateTime.Now;
            try
            {
                string Connectionstring = ConnectionClass.LivesqlConnection();
                SqlParameter[] SqlParam = new SqlParameter[5];
                SqlParam[0] = new SqlParameter("@LeadID", LeadID);
                SqlParam[1] = new SqlParameter("@EmployeeId", EmpCode);
                SqlParam[2] = new SqlParameter("@InboundGroupId", 0);
                SqlParam[3] = new SqlParameter("@CreatedBy", AssignedBy);
                SqlParam[4] = new SqlParameter("@CallType", CallType);
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[CTC].[ConvertOutboundtoInboundNAssignment]", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(LeadID), LeadID, ex.ToString(), "AssignLead", "LeadAllocationData", "OneLead", EmpCode.ToString(), "", Reqdt, DateTime.Now);
            }
            return 1;
        }
        public static int AssignLeadAndUpdateCounter(long LeadID, string EmpCode, Int64 AssignedBy = 124)
        {
            DateTime Reqdt = DateTime.Now;
            try
            {
                string Connectionstring = ConnectionClass.LivesqlConnection();
                SqlParameter[] SqlParam = new SqlParameter[2];
                SqlParam[0] = new SqlParameter("@LeadID", LeadID);
                SqlParam[1] = new SqlParameter("@EmpCode", EmpCode);
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[LeadAssignAndUpdateCounter]", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(LeadID), LeadID, ex.ToString(), "AssignLeadAndUpdateCounter", "LeadAllocationData", string.Empty, EmpCode.ToString(), "", Reqdt, DateTime.Now);
            }
            return 1;
        }
        public static bool AddLeadToPriorityQueue(UserNext5Leads priorityLead)
        {
            try
            {
                UserNext5Leads userdata = new UserNext5Leads();

                priorityLead.Leads[0].ts = DateTime.Now;
                if (priorityLead.Leads[0].ReasonId != 33)
                    priorityLead.Leads[0].Priority = 1;

                string Key = RedisCollection.Next5Leads() + ":" + priorityLead.UserId;
                string obj = MatrixRedisHelper.GetRedisData(Key);

                if (obj == null)
                {
                    MatrixRedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(priorityLead), new TimeSpan(7, 0, 0));
                }
                else
                {
                    userdata = JsonConvert.DeserializeObject<UserNext5Leads>(obj);
                    if (userdata.Leads == null)
                        userdata.Leads = new List<Next5WidgetLead>();

                    if (userdata.Leads.Find(x => x.LeadId == priorityLead.Leads[0].LeadId) == null)
                        userdata.Leads.Add(priorityLead.Leads[0]);
                    else
                    {
                        var updateLead = userdata.Leads.Find(x => x.LeadId == priorityLead.Leads[0].LeadId);
                        updateLead.Priority = priorityLead.Leads[0].Priority;
                        updateLead.CallStatus = priorityLead.Leads[0].CallStatus;
                    }

                    MatrixRedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(userdata), new TimeSpan(7, 0, 0));
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", priorityLead.Leads[0].LeadId, ex.ToString(), "AddLeadToPriorityQueue", "LeadAllocationData", "124", "", "", DateTime.Now, DateTime.Now);
                return false;
            }

            return true;
        }
        public static void UpdateCallStatusOnCallInitiate(long LeadID, string Status, Int64 userId)
        {
            DateTime requestTime = DateTime.Now;
            try
            {
                string Key = RedisCollection.Next5Leads() + ":" + userId;
                var obj = MatrixRedisHelper.GetRedisData(Key);
                if (obj != null)
                {
                    UserNext5Leads userdata = JsonConvert.DeserializeObject<UserNext5Leads>(obj);
                    if (userdata.Leads != null && userdata.Leads.Count > 0)
                    {
                        var updateLead = userdata.Leads.Find(x => x.LeadId == LeadID);
                        if (updateLead != null)
                        {
                            updateLead.CallStatus = Status;
                            MatrixRedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(userdata), new TimeSpan(7, 0, 0));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadID.ToString(), LeadID, ex.ToString(), "UpdateCallStatusOnCallInitiate", "LeadAllocationData", "OneLead", "", "", requestTime, DateTime.Now);
            }
        }

        public static void AddAutoComments(long leadId, Int64 Userid, string comment = "On Call")
        {
            UserNext5Leads userdata = GetNext5LeadInfoData(Userid);
            if (userdata != null && userdata.Leads != null && userdata.Leads.Count > 0)
            {
                Next5WidgetLead data = userdata.Leads.Find(x => x.LeadId == leadId);
                dynamic parentId = leadId;
                if (data != null)
                {
                    UpdateComments(data.LeadId, data.CustomerId, data.ProductId, 7, comment, parentId);
                }
            }
        }
        private static void UpdateComments(long leadId, long customerId, short productId, int eventType, string comments, long parentId)
        {
            SqlParameter[] sqlParam = new SqlParameter[7];
            sqlParam[0] = new SqlParameter("@CustomerId", customerId);
            sqlParam[1] = new SqlParameter("@ProductId", productId);
            sqlParam[2] = new SqlParameter("@ParentLeadId", parentId);
            sqlParam[3] = new SqlParameter("@Comment", comments);
            sqlParam[4] = new SqlParameter("@UserId", 124);
            sqlParam[5] = new SqlParameter("@EventType", eventType);
            sqlParam[6] = new SqlParameter("@PrimaryLeadId", leadId);
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SaveComment_CV]", sqlParam);
        }
        private static UserNext5Leads GetNext5LeadInfoData(Int64 userId)
        {
            try
            {
                string Key = RedisCollection.Next5Leads() + ":" + userId;
                var obj = MatrixRedisHelper.GetRedisData(Key);
                if (obj != null)
                {
                    UserNext5Leads userdata = JsonConvert.DeserializeObject<UserNext5Leads>(obj);
                    return userdata;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", userId, ex.ToString(), "GetNext5LeadInfoData", "LeadAllocationData", "OneLeadPriority", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return null;
        }
        public static DataSet GetC2CDetails(long C2CID, long @LeadID)
        {
            try
            {
                string Connectionstring = ConnectionClass.LivesqlConnection();
                SqlParameter[] SqlParam = new SqlParameter[2];
                SqlParam[0] = new SqlParameter("@C2CID", C2CID);
                SqlParam[1] = new SqlParameter("@LeadID", LeadID);
                return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[CTC].[GetC2CDetails]", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", C2CID, ex.ToString(), "GetC2CDetails", "LeadAllocationData", "OneLeadCallingData", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
                return null;
            }
        }
    }
}
