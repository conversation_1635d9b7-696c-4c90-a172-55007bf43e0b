﻿using Helper;
using Newtonsoft.Json;
using PropertyLayers;
//using ReadXmlProject;
using Redis;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace DataAccessLibrary
{
    public class PredictiveAgentStatusRedis
    {
        #region Private Methods
        private static string GetPredictiveAgentKey(string Collection,string Id)
        {
            string Key = string.Empty;
            try
            {
                if (!(string.IsNullOrEmpty(Id) && string.IsNullOrEmpty(Collection)))
                {
                    Key = $"{Collection?.Trim()}:{Id?.Trim()}";
                }
            }
            catch (Exception ex)
            {
               LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(Id), ex.ToString(), "GetPredictiveAgentKey", "GetPredictiveAgentKey", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return Key;
        }

        private static PredictiveAgentStatus GetPredictiveAgents(string AgentId)
        {
            PredictiveAgentStatus obj = null;
            try
            {
                if (!string.IsNullOrEmpty(AgentId))
                {
                    string Key =$"{RedisCollection.PredictiveAgent()}:{AgentId.Trim()}";
                    var result = RedisHelper.GetRedisData(Key);
                    if (!string.IsNullOrEmpty(result))
                        obj = JsonConvert.DeserializeObject<PredictiveAgentStatus>(result);
                }
            }
            catch (Exception ex)
            {
               LoggingHelper.LoggingHelper.AddloginQueue("",Convert.ToInt32(AgentId), ex.ToString(), "GetPredictiveAgents", "PredictiveAgentStatusRedis", "OneLeadPriority", "", "", DateTime.Now, DateTime.Now);
            }
            return obj;
        }
        
        
        public static PredictiveAgentStatus GetAgentDetails(string AgentId)
        {
            PredictiveAgentStatus AgentDetail = GetPredictiveAgents(AgentId);
            
            return AgentDetail;
        }
        #endregion

        
        public static string GetAppToken(string AgentId)
        {
            string result = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(AgentId))
                {
                    string Key = $"{RedisCollection.AppKey()}:{AgentId.Trim()}";
                    result = RedisHelper.GetRedisData(Key);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(AgentId), ex.ToString(), "GetAppToken", "CommAPI", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public static string GetMatrixToken(string AgentId)
        {
            string result = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(AgentId))
                {
                    string Key = GetPredictiveAgentKey(RedisCollection.PredictiveAsteriskToken(), AgentId);
                    result = RedisHelper.GetRedisData(Key);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(AgentId), ex.ToString(), "GetMatrixToken", "CommAPI", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public static bool updateAgentStatusByRedis(PredictiveAgentStatus predictiveAgentStatus, bool onCall = false)
        {
            string exMsg = String.Empty;
            DateTime requestTime = DateTime.Now;
            PredictiveAgentStatus update = null;

            try
            {
                if (predictiveAgentStatus == null)
                    return false;

                Int64 agentId = 0;
                if (!string.IsNullOrEmpty(predictiveAgentStatus.UserId))
                    agentId = Convert.ToInt64(predictiveAgentStatus.UserId);
                if (agentId == 0)
                    agentId = GetUserIdByRedis(predictiveAgentStatus?.AgentCode);
                if (agentId == 0)
                    return false;

                if (predictiveAgentStatus.status != "HANGUP")
                    update = GetPredictiveAgents(Convert.ToString(agentId));
               
                if (onCall)
                {
                    if (update != null)
                    {
                        if (!string.IsNullOrEmpty(update.status))
                        {
                            if (update.status.ToLower().Equals(predictiveAgentStatus.status.ToLower()))
                            {
                                update._updatedAt = DateTime.Now;
                            }
                            else
                            {
                                update.status = predictiveAgentStatus.status;
                                update._updatedAt = DateTime.Now;
                                update.LastUpdatedOn = DateTime.Now;
                            }
                        }
                    }
                }
                else if (predictiveAgentStatus._updatedAt != DateTime.MinValue)
                {
                    if (update != null)
                    {
                        if (predictiveAgentStatus.status != "HANGUP")
                            update.status = predictiveAgentStatus.status;
                        update.IsCustAnswered = predictiveAgentStatus.IsCustAnswered;
                        // update.Document = predictiveAgentStatus.Document;
                        update._updatedAt = predictiveAgentStatus._updatedAt;
                        update.LeadId = predictiveAgentStatus.LeadId;
                        update.opensv = predictiveAgentStatus.opensv;
                        update.LastUpdatedOn = predictiveAgentStatus._updatedAt;
                    }

                    // Update Agent Call Records
                    PredictiveAgentCalls predictiveAgentCalls = GetAgentCallRecord(agentId.ToString());
                    if (predictiveAgentCalls != null)
                    {
                        predictiveAgentCalls.LeadId = predictiveAgentStatus.LeadId;
                        predictiveAgentCalls.CallType = predictiveAgentStatus.CallType;
                        predictiveAgentCalls.TotalCalls += predictiveAgentStatus.TotalCalls;
                        predictiveAgentCalls.TotalTalkTime += predictiveAgentStatus.TotalTalkTime;
                        predictiveAgentCalls.TotalConnectedCalls += predictiveAgentStatus.TotalTalkTime > 0 ? 1 : 0;
                        predictiveAgentCalls.TotalUniqueCalls += predictiveAgentStatus.TotalUniqueCalls;
                        predictiveAgentCalls.CallId = predictiveAgentStatus.CallId;
                    }
                    else
                    {
                        predictiveAgentCalls = new PredictiveAgentCalls()
                        {
                            LeadId = predictiveAgentStatus.LeadId,
                            CallType = predictiveAgentStatus.CallType,
                            TotalCalls = predictiveAgentStatus.TotalCalls,
                            TotalTalkTime = predictiveAgentStatus.TotalTalkTime,
                            TotalConnectedCalls = predictiveAgentStatus.TotalConnectedCalls,
                            TotalUniqueCalls = predictiveAgentStatus.TotalUniqueCalls,
                            CallId = predictiveAgentStatus.CallId,
                            UniqueVCCount = predictiveAgentStatus.UniqueVCCount,
                            Callableleads = predictiveAgentStatus.Callableleads,
                            OpenLeadCount = predictiveAgentStatus.OpenLeadCount,
                            FutureCB = predictiveAgentStatus.FutureCB
                        };
                    }
                    SetAgentCallRecord(agentId.ToString(), predictiveAgentCalls);
                    // return true;

                }
                else
                {                    
                    if (update != null)
                    {
                        update.status = predictiveAgentStatus.status;
                        if (predictiveAgentStatus?.status?.ToUpper() == "HOLD")
                        {
                            update.holdtime = DateTime.Now;
                        }
                        else if (predictiveAgentStatus?.status?.ToUpper() == "LOGOUT")
                        {
                            update.logouttime = DateTime.Now;
                        }
                    }
                }

                if (update != null && predictiveAgentStatus.status != "HANGUP")
                {
                    if (predictiveAgentStatus.lastActiveTime != DateTime.MinValue)
                    {
                        update.lastActiveTime = predictiveAgentStatus.lastActiveTime;
                    }

                    update.updatedBy = "updateAgentStatusByRedis";

                    RedisHelper.SetRedisData(GetPredictiveAgentKey(RedisCollection.PredictiveAgent(), Convert.ToString(agentId)), JsonConvert.SerializeObject(update), new TimeSpan(8, 0, 0));

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                exMsg = ex.ToString();
                return false;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(predictiveAgentStatus.AgentCode, 0, exMsg, "updateAgentStatusByRedis", "PredictiveAgentStatusRedisOneLead", "OneLeadPriority", JsonConvert.SerializeObject(predictiveAgentStatus), JsonConvert.SerializeObject(update), requestTime, DateTime.Now);
            }
        }

        public static Int64 GetUserIdByRedis(string EmpCode)
        {
            string UserID = "0";
            try
            {
                if (!string.IsNullOrEmpty(EmpCode))
                {
                    string Key = $"{RedisCollection.PredictiveAgentCode()}:{EmpCode.Trim()}";
                    UserID = RedisHelper.GetRedisData(Key);
                    if (string.IsNullOrEmpty(UserID))
                    {
                        UserID = "0";
                    }
                    return Convert.ToInt64(UserID);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(EmpCode, 0, ex.ToString(), "GetUserIdByRedis", "CommAPI", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return Convert.ToInt64(UserID);
        }

        private static PredictiveAgentCalls GetAgentCallRecord(string AgentId)
        {
            PredictiveAgentCalls obj = null;
            try
            {
                if (!string.IsNullOrEmpty(AgentId))
                {
                    var Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentCallTrack(), AgentId);
                    var callData = RedisHelper.GetRedisData(Key);
                    if (!string.IsNullOrEmpty(callData))
                    {
                        obj = JsonConvert.DeserializeObject<PredictiveAgentCalls>(callData);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(0), ex.ToString(), "GetAgentCallRecord", "PredictiveAgentStatusRedis", "OneLeadPriority", "", "", DateTime.Now, DateTime.Now);
            }
            return obj;
        }

        private static void SetAgentCallRecord(string AgentId, PredictiveAgentCalls record)
        {
            try
            {
                if (record != null && !string.IsNullOrEmpty(AgentId))
                {
                    var Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentCallTrack(), AgentId);
                    RedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(record), new TimeSpan(8, 0, 0));
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(0), ex.ToString(), "GetAgentCallRecord", "PredictiveAgentStatusRedis", "OneLeadPriority", "", "", DateTime.Now, DateTime.Now);
            }
        }

        public static bool updateAgentLoginRedis(PredictiveAgentStatus objPredictiveAgentStatus)
        {
            StringBuilder sb = new StringBuilder();
            string strException = string.Empty;
            bool obj = false;
            DateTime requestTime = DateTime.Now;
            try
            {
                string Key = string.Empty;

                if (!string.IsNullOrEmpty(objPredictiveAgentStatus?.AgentId))
                {
                    sb.Append("Status: " + objPredictiveAgentStatus.status);
                    sb.AppendLine(" Process: " + objPredictiveAgentStatus.Process + " - Agent: " + objPredictiveAgentStatus.AgentId);

                    //Set Remaining Time 
                    PredictiveAgentStatus AgentData = GetPredictiveAgents(objPredictiveAgentStatus?.AgentId);
                    if (AgentData?.remainingpausetime != null)
                    {
                        objPredictiveAgentStatus.remainingpausetime = AgentData.remainingpausetime;
                        objPredictiveAgentStatus.lastpausetime = AgentData.lastpausetime;
                    }

                    if(!string.IsNullOrEmpty(objPredictiveAgentStatus.status) && objPredictiveAgentStatus.status.ToUpper()!="IDLE")
                    {
                        if (AgentData != null && !string.IsNullOrEmpty(AgentData.AgentIP))
                        {
                            objPredictiveAgentStatus.AgentIP = AgentData.AgentIP;
                            objPredictiveAgentStatus.Asterisk_Url = AgentData.Asterisk_Url;
                            objPredictiveAgentStatus.CallingCompany = AgentData.CallingCompany;
                        }
                    }

                    // Add AgentData In Redis
                    //objPredictiveAgentStatus.LastUpdatedOn = AgentData?.LastUpdatedOn != null ? AgentData.LastUpdatedOn : DateTime.MinValue;
                    objPredictiveAgentStatus.LeadId = string.IsNullOrEmpty(AgentData?.LeadId) ? string.Empty : AgentData.LeadId;
                    Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgent(), objPredictiveAgentStatus.AgentId);
                    RedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(objPredictiveAgentStatus), new TimeSpan(8, 0, 0));

                    // Add AgentIP In Redis
                    //Key = GetPredictiveAgentKey(RedisCollection.AgentIP(), PredictiveAgentStatus?.AgentIP);
                    //RedisHelper.SetRedisData(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));

                    // Add AgentCode In Redis
                    Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentCode(), objPredictiveAgentStatus.AgentCode.ToUpper());
                    RedisHelper.SetRedisData(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));

                    //Add DIDno in redis
                    if (!string.IsNullOrEmpty(objPredictiveAgentStatus.DIDNo))
                    {
                        Key = GetPredictiveAgentKey(RedisCollection.AgentPhone(), objPredictiveAgentStatus.DIDNo);
                        RedisHelper.SetRedisData(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));
                    }

                    // Add Context In Redis ZAdd
                    if (objPredictiveAgentStatus.IsMultiQueue)
                    {
                        foreach (var multiQueue in objPredictiveAgentStatus.MultiQueue)
                        {
                            if (!string.IsNullOrEmpty(multiQueue))
                            {
                                Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentContext(), Convert.ToString(multiQueue).ToLower());
                                RedisHelper.SetZAdd(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));
                            }
                        }
                    }
                    else if (!string.IsNullOrEmpty(objPredictiveAgentStatus.Context))
                    {
                        Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentContext(), Convert.ToString(objPredictiveAgentStatus.Context).ToLower());
                        RedisHelper.SetZAdd(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));
                    }

                    if (!string.IsNullOrEmpty(objPredictiveAgentStatus.OfflineQueue))
                    {
                        Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentContext(), Convert.ToString(objPredictiveAgentStatus.OfflineQueue).ToLower());
                        RedisHelper.SetZAdd(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));
                    }

                    if (objPredictiveAgentStatus.TL != null && objPredictiveAgentStatus.TL.Count > 0)
                    {
                        foreach (var TL in objPredictiveAgentStatus.TL)
                        {
                            if (!string.IsNullOrEmpty(TL))
                            {
                                Key = GetPredictiveAgentKey(RedisCollection.PredictiveAgentManagerIndex(), Convert.ToString(TL));
                                RedisHelper.SetZAdd(Key, objPredictiveAgentStatus.AgentId, new TimeSpan(8, 0, 0));
                                sb.AppendLine("Manager: " + Convert.ToString(TL) + " - Agent: " + objPredictiveAgentStatus.AgentId);
                            }
                        }
                    }

                    #region Get/Set Agent calls In Redis
                    PredictiveAgentCalls predictiveAgentCalls = GetAgentCallRecord(objPredictiveAgentStatus.AgentId);
                    if (predictiveAgentCalls != null)
                    {
                        predictiveAgentCalls.TotalCalls += objPredictiveAgentStatus.TotalCalls;
                        predictiveAgentCalls.TotalTalkTime += objPredictiveAgentStatus.TotalTalkTime;
                    }
                    else
                    {
                        predictiveAgentCalls = new PredictiveAgentCalls()
                        {
                            TotalCalls = objPredictiveAgentStatus.TotalCalls,
                            TotalTalkTime = objPredictiveAgentStatus.TotalTalkTime,
                            TotalConnectedCalls = objPredictiveAgentStatus.TotalConnectedCalls,
                            TotalUniqueCalls = objPredictiveAgentStatus.TotalUniqueCalls,
                        };
                    }
                    SetAgentCallRecord(objPredictiveAgentStatus.AgentId, predictiveAgentCalls);
                    #endregion

                    #region Add AsteriskToken In Redis
                    if (!string.IsNullOrEmpty(objPredictiveAgentStatus?.AsteriskToken))
                    {
                        Key = GetPredictiveAgentKey(RedisCollection.PredictiveAsteriskToken(), Convert.ToString(objPredictiveAgentStatus.AgentId));
                        RedisHelper.SetRedisData(Key, objPredictiveAgentStatus.AsteriskToken, new TimeSpan(8, 0, 0));
                    }
                    #endregion

                    if (objPredictiveAgentStatus.RoleId == "13" && objPredictiveAgentStatus?.ProcessId > 0)
                    {
                        OneLeadObj _oneLeadObj = new OneLeadObj() { GroupId = objPredictiveAgentStatus.GroupId, ProcessId = objPredictiveAgentStatus.ProcessId, NoLeadPopUp = false };
                        Key = GetPredictiveAgentKey(RedisCollection.OneLeadObj(), Convert.ToString(objPredictiveAgentStatus.AgentId));
                        MatrixRedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(_oneLeadObj), new TimeSpan(8, 0, 0));
                    }
                }
                sb.Append("End");
                return true;
            }
            catch (Exception ex)
            {
                strException = ex.Message.ToString();
                return false;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(objPredictiveAgentStatus?.AgentCode, 0, strException, "updateAgentLoginStatusByRedis", "oneLead", "CommAPI", JsonConvert.SerializeObject(objPredictiveAgentStatus), sb.ToString(), requestTime, DateTime.Now);
            }
        }
    }
}
