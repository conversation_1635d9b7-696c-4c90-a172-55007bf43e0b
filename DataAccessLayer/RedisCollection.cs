﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace DataAccessLibrary
{
    public class RedisCollection
    {
        internal static string RevisitCustomers()
        {
            return "OnlineCustomer";
        }

        internal static string RevisitEnquiry()
        {
            return "OnlineEnquiry";
        }

        internal static string VisitLeadId()
        {
            return "VisitLeadId";
        }

        public static string Next5Leads()
        {
            return "Next5Leads";
        }

        internal static string LeadAgentIndex()
        {
            return "LeadId.AgentId.index";
        }

        internal static string RedisKey()
        {
            return "LeadId";
        }

        internal static string CheckIVRFeedBack()
        {
            return "CheckIVRFeedBack";
        }

        internal static string IsCallBackAllowed()
        {
            return "IsCBAllowed";
        }

        internal static string TimeZone()
        {
            return "TimeZone";
        }

        internal static string LeadPriorityConstants()
        {
            return "priorityconfig";
        }
        public static string PredictiveAgent()
        {
            return "PredictiveAgent";
        }
        internal static string PredictiveAgentCode()
        {
            return "PredictiveAgentCode";
        }
        internal static string PredictiveAgentManagerIndex()
        {
            return "AgentId.Manager.Index";
        }
        public static string PredictiveAsteriskToken()
        {
            return "AsteriskToken";
        }
        internal static string PredictiveAgentCallTrack()
        {
            return "PredictiveAgentCallTrack";
        }
        internal static string AgentIP()
        {
            return "AgentIP";
        }
        internal static string PredictiveAgentContext()
        {
            return "AgentId.Context.Index";
        }
        public static string RealTimeDataContext()
        {
            return "ServerCallStatus";
        }
        public static string MobileStatus()
        {
            return "Mobile";
        }

        internal static string AgentPhone()
        {
            return "AgentPhone";
        }
        public static string CallingList()
        {
            return "CallingList";
        }

        public static string IsWhatsAPPAllowed()
        {
            return "IsWhatsAPPAllowed";
        }

        internal static string AWSSceret()
        {
            return "AWSSceret";
        }
        public static string AppKey()
        {
            return "AppToken";
        }

        public static string MongoConfig()
        {
            return "MongoConfig";
        }

        internal static string OneLeadObj()
        {
            return "OneLeadObj";
        }
        public static string ACLMongoConfig()
        {
            return "ACLMongoConfig";
        }
        public static string LiveWebConfig()
        {
            return "Livewebconfig";
        }
        public static string ReplicaWebConfig()
        {
            return "Replicawebconfig";
        }
        public static string SentimentCoolingPeriod()
        {
            return "SentimentCoolingPeriod";
        }

        public static string LeadRankRE_Engine()
        {
            return "LeadRankRE_Engine";
        }

        public static string LeadRankRE_Rules()
        {
            return "LeadRankRE_Rule";
        }

    }
}
