﻿using DataHelper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Runtime.Caching;
using System.Text;
using PropertyLayers;
using Redis;
using MongoDB.Driver.Builders;
using MongoDB.Driver;
using MongoDB.Bson;
using Helper;
using Amazon;

namespace DataAccessLibrary
{
    public class MongoDLL
    {

        public static List<SysConfigData> GetConfiValueFromMongo(string Source)
        {
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery varquery = null;
                varquery = Query.EQ("source", Source);
                var documents = _CommDB.GetDocuments<SysConfigData>(varquery, MongoCollection.ConfigValues());
                return documents;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetConfiValueFromMongo." + ex.ToString() + "Source "+ Source);
                return null;
            }
        }

        public static Dictionary<string, SysConfigData> GetConfiValueFromMongo()
        {
            Dictionary<string, SysConfigData> sysConfigs = null;
            try
            {
                IMongoFields Field = Fields.Include("source", "authKey", "clientKey").Exclude("_id");
                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                var data = objCommDB.GetDocuments<SysConfigData>(null, Field, MongoCollection.ConfigValues());

                sysConfigs = data.GroupBy(data => data.source.ToLower()).ToDictionary(group => group.Key, group => group.First());
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetConfiValueFromMongo." + ex.ToString());
                return null;
            }
            return sysConfigs;
        }
        public static Dictionary<string, object> GetConfiFromMongo_SV(string key)
        {
            Dictionary<string, object> result = new Dictionary<string, object>();
            try
            {
                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery varquery = Query.EQ("_id", key);
                var value = objCommDB.FindOneDocument<BsonDocument>(varquery, MongoCollection.ConfigFiles());
                result = (Dictionary<string, object>)BsonTypeMapper.MapToDotNetValue(value);
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetConfiFromMongo_SV." + ex.ToString());
                return null;
            }
        }

        public static void UpdateDocument(IMongoQuery query, IMongoUpdate updateQuery)
        {
            MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
            objCommDB.UpdateDocument(query, updateQuery, DataAccessLibrary.MongoCollection.LPDataCollection());
        }

        public static PriorityModel GetLeadDetails(Int64 LeadId)
        {
            List<PriorityModel> oPriorityModel = new List<PriorityModel>();
            try
            {
                IMongoFields Field = Fields.Include("_id", "CustName", "ProductID", "CustID");
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoSortBy OrderBy = SortBy.Descending("LeadCreatedOn");
                var query = Query<PriorityModel>.EQ(p => p.LeadID, LeadId);
                var lstPriorityModel = _CommDB.GetDocuments<PriorityModel>(query, MongoCollection.LPDataCollection(), OrderBy, Field, 0, 0).ToList();
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                    return lstPriorityModel[0];
                else return null;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadId.ToString(), LeadId, ex.ToString(), "GetPriorityModelMongo_Error", "LeadPrioritizationDLL", "", "", "", DateTime.Now, DateTime.Now);
                return null;
            }
        }

        public static bool IsValidateCustomer(string EncryptLeadId, string Token)
        {
            bool result = false;
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery varquery = Query.And(Query.EQ("encryptLeadId", EncryptLeadId), Query.EQ("Token", Convert.ToInt64(Token)));
                IMongoFields Field = Fields.Include("encryptLeadId", "Token");
                var value = _CommDB.FindOneDocument<BsonDocument>(varquery, MongoCollection.CustomerAuthenticate(), Field);
                if (value != null)
                    result = true;

            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in IsValidateCustomer." + ex.ToString());
                result = false;
            }
            return result;
        }
        public static long getExistRecord(string EncryptLeadId)
        {
            long result = 0;
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery varquery = Query.And(Query.EQ("_id", EncryptLeadId));
                IMongoFields Field = Fields.Include("_id", "Token");
                var value = _CommDB.FindOneDocument<BsonDocument>(varquery, MongoCollection.CustomerAuthenticate(), Field);
                if (value != null)
                    result= Convert.ToInt64(value.GetValue("Token"));

            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in getExistRecord." + ex.ToString());
                return result;
            }
            return result;
        }

        public static List<ACLConfigData> GetACLMongoConfig(IMongoFields Field, IMongoQuery varquery)
        {
            MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
            var data = objCommDB.GetDocuments<ACLConfigData>(varquery, Field, MongoCollection.ACLConfigValues());
            return data;

        }


    }
}
