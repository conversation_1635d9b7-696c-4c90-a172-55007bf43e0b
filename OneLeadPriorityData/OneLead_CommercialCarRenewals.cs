﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PropertyLayers;
using Helper;
using DataAccessLibrary;
using DataAccessLayer;

namespace OneLeadPriorityData
{
    public class OneLead_CommercialCarRenewals : IOneLead
    {
        readonly productPriorityconstant _LeadPriorityConstants;
        readonly Int16 _productID;
        readonly OneLeadData _OneLeadData;
        Int16 counter;
        readonly Int16 _GroupID = 0;
        readonly OneLeadParams _oneLeadParams;

        public OneLead_CommercialCarRenewals(short productID, Int16 GroupID, OneLeadParams oneLeadParams)
        {
            // TODO: Complete member initialization
            this._productID = productID;
            this._GroupID = GroupID;
            _LeadPriorityConstants = getPriorityConfigByProduct(productID);
            _OneLeadData = new OneLeadData(productID, _LeadPriorityConstants, counter);
            _oneLeadParams = oneLeadParams;
        }

        // Getting Top PriorityLead say 5Leads
        public List<PriorityModel> getOneLead(Int16 ProductId, Int64 UserId, Int16 priorityleadCount, bool productCheck = false)
        {
            List<PriorityModel> lstPriorityModel = new List<PriorityModel>();
            List<PriorityModel> lstAgentAssignedLeads = LeadPrioritizationDLL.GetAgentActiveLeads(UserId, ProductId, true, _oneLeadParams, productCheck, _GroupID);// getting Agent all Active Leads
            
            List<PriorityModel> lstRest1stAttemptLeads = new List<PriorityModel>();

            lstPriorityModel = getPriorityQueue(lstPriorityModel, lstAgentAssignedLeads, lstRest1stAttemptLeads, priorityleadCount);

            return lstPriorityModel;
        }
        // Get all active Leads of an agent   


        public List<PriorityModel> getBookedLead(Int16 ProductId, Int64 UserId, Int16 priorityleadCount)
        {
            return new List<PriorityModel>();
        }

        // get agent priority queue
        public List<PriorityModel> getPriorityQueue(List<PriorityModel> lstPriorityModel, List<PriorityModel> lstAgentAssignedLeads, List<PriorityModel> lstRest1stAttemptLeads, Int16 PriorityQueueSize, Int16 ProductID = 0, Int64 UserID = 0)
        {
            try
            {
                foreach (var prioritySequence in _LeadPriorityConstants.prioritySequence)
                {
                    List<PriorityModel>? _filteredleads = null;
                    switch (prioritySequence.ToUpper())
                    {
                        case "ACTIVEREVISIT": // active revisit
                            _filteredleads = _OneLeadData.GetActiveRevisitLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTACTIVEREVISIT": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptActiveRevisit(lstAgentAssignedLeads, counter); // 2nd attempt act revisit 
                            break;
                        case "ACTIVENEW": // active revisit
                            _filteredleads = _OneLeadData.GetActiveNewLead(lstAgentAssignedLeads, counter);  // active new
                            break;
                        case "2NDATTEMPTACTIVENEW": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptActiveNew(lstAgentAssignedLeads, counter);  // 2nd attempt active new
                            break;
                        case "PAYMENTCB": // active revisit
                            _filteredleads = _OneLeadData.GetPaymentCBLead(lstAgentAssignedLeads, counter);  // payment cb
                            break;
                        case "2NDATTEMPTPCB": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptPCBLeads(lstAgentAssignedLeads, counter);  // 2nd attempt payment cb
                            break;
                        case "EMAILREVISIT":
                            _filteredleads = _OneLeadData.GetEmailRevisitLeads(lstAgentAssignedLeads, counter);  // Email Revisit
                            break;
                        case "2NDATTEMPTEMAILREVISIT":
                            _filteredleads = _OneLeadData.Get2ndAttemptEmailRevisit(lstAgentAssignedLeads, counter);  // 2nd attempt Email Revisit
                            break;
                        case "PAYMENTFAILURE":
                            _filteredleads = OneLeadData.GetPaymentFailureLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTPAYMENTFAILURE":
                            _filteredleads = _OneLeadData.Get2ndAttemptPaymentFailureLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "ACTIVECB": // active revisit
                            _filteredleads = _OneLeadData.GetActiveCBLeads(lstAgentAssignedLeads, counter);  // active call back
                            break;
                        case "2NDATTEMPTACTIVECB": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptActiveCB(lstAgentAssignedLeads, counter);  // 2nd atmpactive call back
                            break;
                        case "PASSIVENEW": // active revisit
                            _filteredleads = GetPassiveNewLead(lstAgentAssignedLeads, counter); // passive new
                            break;
                        case "2NDATTEMPTPASSIVENEW": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptPassiveNew(lstAgentAssignedLeads, counter); // 2nd atmp passive new
                            break;
                        case "PASSIVEREVISIT": // active revisit
                            _filteredleads = _OneLeadData.GetPassiveRevisitLead(lstAgentAssignedLeads, counter); // passive revisit
                            break;
                        case "2NDATTEMPTPASSIVEREVISIT": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptPassiveRevisit(lstAgentAssignedLeads, counter); // 2nd atmp passive new
                            break;
                        case "PASSIVECB": // active revisit
                            _filteredleads = _OneLeadData.GetPassiveCBLead(lstAgentAssignedLeads, counter); // passive CALL BACK)
                            break;
                        case "2NDATTEMPTPASSIVECB": // active revisit
                            _filteredleads = _OneLeadData.Get2ndAttemptPassiveCB(lstAgentAssignedLeads, counter); // 2nd atmp passive CALL BACK)
                            break;
                        case "EXPIRY": // active revisit
                            _filteredleads = GetExpiryDateLeads(lstAgentAssignedLeads, counter);// expiryLeads
                            break;
                        case "CALLRELEASE": // active revisit
                            _filteredleads = CallReleaseLeads(lstAgentAssignedLeads, counter);   // call released
                            break;
                        case "UNANSWERED": // active revisit
                            _filteredleads = _OneLeadData.GetUnansweredLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "UNANSWEREDRECENT": // active revisit
                            _filteredleads = GetUnansweredRecentLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "REVISITCTC":
                            _filteredleads = _OneLeadData.GetCTCRevisitLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTREVISITCTC":
                            _filteredleads = _OneLeadData.Get2ndAttemptCTCRevisit(lstAgentAssignedLeads, counter);
                            break;
                        case "MISSEDCB":
                            _filteredleads = GetMissedCBLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "TODAYEXPIRY":
                            _filteredleads = _OneLeadData.GetTodayExpiryLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTTODAYEXPIRY":
                            _filteredleads = _OneLeadData.Get2ndAttemptTodayExpiryLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "EXPIRY0104DAYS":
                            _filteredleads = _OneLeadData.GetExpiryLeads(lstAgentAssignedLeads, counter, 0, 4, true, 0);
                            break;
                        case "EXPIRY0412DAYS":
                            _filteredleads = _OneLeadData.GetExpiryLeads(lstAgentAssignedLeads, counter, 4, 12, true, 1);
                            break;
                        case "EXPIRY1230DAYS":
                            _filteredleads = _OneLeadData.GetExpiryLeads(lstAgentAssignedLeads, counter, 12, 30, true, 2);
                            break;
                        case "EXPIRY3045DAYS":
                            _filteredleads = _OneLeadData.GetExpiryLeads(lstAgentAssignedLeads, counter, 30, 45, true, 4);
                            break;
                        case "SERVICECB":
                            _filteredleads = _OneLeadData.GetServiceCBLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "REST": // active revisit
                            _filteredleads = GetRest_1Leads(lstAgentAssignedLeads, counter);
                            lstRest1stAttemptLeads.AddRange(_filteredleads.Where(p => !lstPriorityModel.Exists(X => X.LeadID == p.LeadID)));
                            break;
                    }

                    if (_filteredleads != null && _filteredleads.Count > 0)
                        lstPriorityModel.AddRange(_filteredleads.Where(p => !lstPriorityModel.Exists(X => X.LeadID == p.LeadID)));
                    if (lstPriorityModel.Count >= PriorityQueueSize)
                        return lstPriorityModel;

                    counter += 1;
                }

            }
            catch (Exception)
            {
                

            }

            finally
            {
                LeadPrioritizationDLL.UpdateRestFlag(lstRest1stAttemptLeads);
            }

            return lstPriorityModel;
        }        
        
        //yesterday missed callbacks
        private List<PriorityModel> GetMissedCBLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstMissedCBLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstMissedCBLeads = lstPriorityModel.Where(x => 
                            (x.CallBack != null)
                        && (DateTime.Now.Date > x.CallBack.CBtime.Date) //  yesterday callbacks 
                        && (x.Call == null || x.Call.calltime < x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1))) //no call after callback
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.MissedCB, counter))
                  .OrderByDescending(x => Math.Abs(x.CallBack.CBtime.Subtract(DateTime.Now).Seconds)).ToList();// sorting by nearest callback
                }
            }
            catch (Exception)
            {

            }

            return lstMissedCBLeads;
        }

        //Get ALL active Revisit in last 30 mintso
        private List<PriorityModel> GetActiveRevisitLeads(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lstActiveRevisitLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstActiveRevisitLeads = lstPriorityModel
                                      .Where(x => (x.Revisit != null) && (DateTime.Now.Subtract(x.Revisit.ts).TotalMinutes < ActiveRevisitShowTime)// revisit in last 30 min
                                          // && (x.Call == null ? true : (x.Call.TalkTime == 0 ? DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC : true))// no NANC In last 30 min
                                            && (x.Call == null || (x.Revisit.ts > x.Call.calltime.AddSeconds(TimeDiffDailer + x.Call.TalkTime))) // No call attempt after revisitTime
                                            && (x.SkippingTime < x.Revisit.ts))
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.ActiveRevisit, counter))
                  .OrderByDescending(x => x.Revisit.ts).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstActiveRevisitLeads;
        }

        //Get PassiveNewLead
        public List<PriorityModel> GetPassiveNewLead(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstPassiveNewLead = new List<PriorityModel>();
            try
            {
                Int16 ActiveNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstPassiveNewLead = lstPriorityModel.Where(x => ((DateTime.Now.Subtract(x.LeadCreatedOn).TotalMinutes > ActiveNewLeadShowTime)// after 30 mints
                                                               && (x.Call == null))
                                                               )// no call attempts
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.PassiveNew, counter))
                  .OrderBy(x => (x.PrevPolicyExpDate != DateTime.MinValue) ? x.PrevPolicyExpDate : x.LeadCreatedOn).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstPassiveNewLead;
        }

        // GetPassiveRevistLead
        private List<PriorityModel> GetPassiveRevisitLead(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lstPassiveRevisitLead = new List<PriorityModel>();
            try
            {
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short PassivEnd = _LeadPriorityConstants.PassiveRevisitEndMints;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstPassiveRevisitLead = lstPriorityModel.Where(x => (x.Revisit != null)
                                                               && (DateTime.Now.Subtract(x.Revisit.ts).TotalMinutes > ActiveRevisitShowTime)// revisit Time over by 30 mints
                                                               && (DateTime.Now.Subtract(x.Revisit.ts).TotalMinutes < PassivEnd)  //  before 24 hrs
                                                               && (x.Call == null || (x.Call.calltime.AddSeconds(TimeDiffDailer + x.Call.TalkTime) < x.Revisit.ts))) // no call after revisitTime
                                                                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.PassiveRevisit, counter))
                                           .OrderByDescending(x => x.Revisit.ts).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstPassiveRevisitLead;
        }

        
        //GetUnansweredLeads
        private List<PriorityModel> GetUnansweredLeads(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lstUnansweredLeads = new List<PriorityModel>();
            try
            {
                DateTime CurrentTime = DateTime.Now;
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(DateTime.Now);
                byte NANCMaxAttemInDiffShifts = _LeadPriorityConstants.NANCMaxAttemInDiffShifts;
                byte NANCLastCallDayGap = _LeadPriorityConstants.NANCLastCallDayGap;
                byte NANCMax1DayAttempt = _LeadPriorityConstants.NANCMax1DayAttempt;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                byte UnAnsweredDaysGap = _LeadPriorityConstants.UnAnsweredDaysGap;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstUnansweredLeads = lstPriorityModel.Where(x => (x.Call != null)
                                                          && (x.Call.TotalTT == 0)
                                                          && (x.PrevPolicyExpDate == DateTime.MinValue || x.PrevPolicyExpDate.Subtract(CurrentTime).TotalDays <= 45)
                                                          && (x.CallBack == null || DateTime.Now.Subtract(x.CallBack.CBtime).TotalSeconds > 0)// no future CB ..To handle existing CB Lead with tt =0
                                                          && (x.Call.TalkTime == 0 && DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// Not visible if NANC in last 30 mints
                                                          && (CurrentTime.Subtract(x.Call.calltime).TotalDays > UnAnsweredDaysGap ||  //no shift check for lead called more than 2 days earlier 
                                                                                      (  
                                                                                      ((x.Call.NANC_Attempts < NANCMaxAttemInDiffShifts) && ((x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < NANCMax1DayAttempt) && (x.Call.Shifts == null || !x.Call.Shifts.Contains(CurrentCallShift)))
                                                                                       || (x.Call.NANC_Attempts == NANCMaxAttemInDiffShifts && DateTime.Now.Date > x.Call.calltime.Date)// 7th Call on next day                                                                                       
                                                                                       || (x.Call.NANC_Attempts > NANCMaxAttemInDiffShifts && DateTime.Now.Date.Subtract(x.Call.calltime.Date).TotalDays >= NANCLastCallDayGap)// 8th and 9th Call on alternate days
                                                                                       )
                                                                                      )
                                                          )
                                                         .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.UnansweredLeads, counter)).OrderByDescending(x => x.LeadCreatedOn).ToList();//neverAnsweredCall
                }
                //lstUnansweredLeads.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.UnansweredLeads);// setting the leadCategory
            }
            catch (Exception)
            {

            }
            return lstUnansweredLeads;
        }        

        // Get ActiveRevisit for 2nd Attempt
        private List<PriorityModel> Get2ndAttemptActiveRevisit(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lst2ndAttemptActiveRevisit = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_Revisit;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptActiveRevisit = lstPriorityModel.Where(x => (x.Revisit != null && x.Call != null) && (x.Call.TalkTime == 0)    // last Attempt NANC
                                                                       //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt
                                                                       && (x.Revisit.ts <= x.Call.calltime.AddSeconds(TimeDiffDailer) && x.Call.calltime.AddSeconds(TimeDiffDailer) <= x.Revisit.ts.AddMinutes(ActiveRevisitShowTime)) // last NANC Attempt was within 30 mints of  the revisit time
                                                                       && (x.Call.NANC_Attempts % 2 == 1)// odd attempts
                                                                       && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC))// last NANC call was before 30 mints
                        //.Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptActvRevisit)).OrderByDescending(x => x.Revisit.ts).ToList();
                                                             .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptActvRevisit, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lst2ndAttemptActiveRevisit;
        }

        // Get ActiveNew for 2nd Attempt
        private List<PriorityModel> Get2ndAttemptActiveNew(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lst2ndAttemptActiveNew = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_New;
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptActiveNew = lstPriorityModel.Where(x => (x.Call != null) && (x.Call.TalkTime == 0)    // last Attempt NANC
                                                                    //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt
                                                                    && (x.Call.calltime.AddSeconds(TimeDiffDailer) >= x.LeadCreatedOn && x.Call.calltime.AddSeconds(TimeDiffDailer) <= x.LeadCreatedOn.AddMinutes(ActivNewLeadShowTime)) // last  NANC attempt was within 30 mints of leadCreation
                                                                    && (x.Call.NANC_Attempts == 1 && x.Call.CallAttempts == 1)
                                                                    && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC))// last NANC call was before 30 mints  
                        //.Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptActvNew)).OrderByDescending(x => x.LeadCreatedOn).ToList();
                                                            .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptActvNew, counter)).OrderBy(x => x.Call.calltime).ToList();
                }

            }
            catch (Exception)
            {

            }
            return lst2ndAttemptActiveNew;
        }       

        //Get Passive Revisit for 2nd Attempt
        private List<PriorityModel> Get2ndAttemptPassiveRevisit(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lst2ndAttemptPassiveRevisit = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short PassEnd = _LeadPriorityConstants.PassiveRevisitEndMints;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptPassiveRevisit = lstPriorityModel.Where(x => (x.Call != null && x.Revisit != null) && (x.Call.TalkTime == 0)  // last Attempt NANC
                                                                        //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt
                                                                        && (x.IsLastBucketRest == false) // lastly not in RestBucket
                                                                        && (x.Call.NANC_Attempts % 2 == 1)// odd attempts
                                                                        && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC Was before 30 mints (means moving out of bucket)
                                                                        && (x.Revisit.ts.AddMinutes(ActiveRevisitShowTime) <= x.Call.calltime.AddSeconds(TimeDiffDailer))  // last Attempt was after  30 min of Revisit time
                                                                        && (x.Revisit.ts.AddMinutes(PassEnd) >= x.Call.calltime.AddSeconds(TimeDiffDailer)))  //last attemmpt between 30 min to 24 hrs after revisit time
                        //.Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptPasvRevisit)).OrderByDescending(x => x.Revisit.ts).ToList();
                                                                       .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptPasvRevisit, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lst2ndAttemptPassiveRevisit;
        }

        //Get Passive New for 2nd Attempt
        private List<PriorityModel> Get2ndAttemptPassiveNew(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lst2ndAttemptPassiveNew = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptPassiveNew = lstPriorityModel.Where(x => (x.Call != null) && (x.Call.TalkTime == 0)   // last Attempt NANC
                                                                     //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt
                                                                     && (x.IsLastBucketRest == false) // lastly not in RestBucket
                                                                     && (x.Call.NANC_Attempts == 1 && x.Call.CallAttempts == 1)
                                                                     && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC Was before 30 mints (means moving out of bucket)
                                                                     && (x.LeadCreatedOn.AddMinutes(ActivNewLeadShowTime) <= x.Call.calltime.AddSeconds(TimeDiffDailer)))// last Attempt was done after  30 min of createdOn time
                                                                    .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptPasvNew, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lst2ndAttemptPassiveNew;
        }
        
        //GetRestLeads
        private List<PriorityModel> GetRest_1Leads(List<PriorityModel> lstPriorityModel, Int16 counter)// eventually means  Leads on which no work is done today
        {
            List<PriorityModel> lstRestLeads = new List<PriorityModel>();
            DateTime ct = DateTime.Now;
            try
            {
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;
                byte RestLeadLastCall = _LeadPriorityConstants.RestLeadLastCall;
                byte NANCMaxAttemInDiffShifts = _LeadPriorityConstants.NANCMaxAttemInDiffShifts;
                byte NANCLastCallDayGap = _LeadPriorityConstants.NANCLastCallDayGap;
                byte NANCMax1DayAttempt = _LeadPriorityConstants.NANCMax1DayAttempt;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(DateTime.Now);
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {

                    lstRestLeads = lstPriorityModel
                                        .Where(x =>
                                                ((x.Call == null || ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap) // NOT CALLED IN lAST 12 HRS ( not called toady)
                                                && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                                && (x.PrevPolicyExpDate > DateTime.MinValue)
                                                && (
                                                    //After expiry date
                                                    (ct.Date.Subtract(x.PrevPolicyExpDate.Date).TotalDays > 0 && ct.Date.Subtract(x.PrevPolicyExpDate.Date).TotalDays <= 45
                                                     && (x.Call == null || ct.Date.Subtract(x.Call.calltime.Date).TotalDays > 1)
                                                     )

                                                    //before policy expire
                                                    //||
                                                    //(x.PrevPolicyExpDate.Date.Subtract(ct.Date).TotalDays > 0 && x.PrevPolicyExpDate.Date.Subtract(ct.Date).TotalDays <= 7
                                                    //    && (x.Call == null || ct.Date.Subtract(x.Call.calltime.Date).TotalDays > 0)
                                                    //    && (x.SkippingTime != null || x.SkippingTime < x.CallReleaseTime || ct.Subtract(x.SkippingTime).TotalMinutes > x.SkipDurationHrs)
                                                    //)
                                                )
                                            )
                                        ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.RestLeads_1, counter))
                                        .OrderByDescending(x => x.PrevPolicyExpDate).ToList();

                }

            }
            catch (Exception)
            {

            }
            return lstRestLeads;
        }


        public List<PriorityModel> GetExpiryDateLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstTodayExpiry = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                Int16 TodyCreatdExprGap = _LeadPriorityConstants.TodayCreatedExprTimeGap;
                Int16 BefTodyCreatdExprGap = _LeadPriorityConstants.BeforeTodayCreatedExprTimeGap;
                byte ExpiryCount = _LeadPriorityConstants.ExpiryLeadsShowCount;
                DateTime ct = DateTime.Now;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstTodayExpiry = lstPriorityModel
                                       .Where(x => (x.PrevPolicyExpDate.Date == ct.Date || x.PrevPolicyExpDate.Date == ct.AddDays(1).Date)// Expiring today or tomorrow
                                          && (x.CallBack == null || x.CallBack.CBtime < ct) // no future call back
                                           && (x.Call != null && ct.Subtract(x.Call.calltime).TotalMinutes > TodyCreatdExprGap) // for today created check                                           
                                           )
                                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.RecentExpiry, counter))
                                        .OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstTodayExpiry;
        }

        public productPriorityconstant getPriorityConfigByProduct(Int16 productID)
        {
            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            var obj = _LeadPriorityConstants.PriorityConstant.Where(p => (p.productID == productID) && (p.subProduct.ToUpper() == "RENEWAL")).SingleOrDefault();
            if (obj == null)
                throw new NotImplementedException("getPriorityConfigByProduct config is empty");
            return obj;
        }

        public static List<PriorityModel> CallReleaseLeads(List<PriorityModel> lstAgentActiveLeads, Int16 counter)
        {
            List<PriorityModel> lstReleasedCallleads = new List<PriorityModel>();
            try
            {
                DateTime ct = DateTime.Now;
                List<PriorityModel> lstAgentLeads = new List<PriorityModel>(lstAgentActiveLeads);
                LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
                if (lstAgentActiveLeads != null && lstAgentActiveLeads.Count > 0)
                {
                    lstReleasedCallleads = lstAgentLeads
                                       .Where(x => (x.CallReleaseCount > 0)
                                                    && (x.SkippingTime < x.CallReleaseTime) //  so that if release after skipping then it should come in priority
                                              ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.CallReleasedLeads, counter)).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lstReleasedCallleads;
        }

        
        //GetUnansweredLeads
        private List<PriorityModel> GetUnansweredRecentLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstUnansweredLeads = new List<PriorityModel>();
            try
            {
                DateTime CurrentTime = DateTime.Now;
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(DateTime.Now);
                byte NANCMaxAttemInDiffShifts = _LeadPriorityConstants.NANCMaxAttemInDiffShifts;
                byte NANCLastCallDayGap = _LeadPriorityConstants.NANCLastCallDayGap;
                byte NANCMax1DayAttempt = _LeadPriorityConstants.NANCMax1DayAttempt;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                byte UnAnsweredDaysGap = _LeadPriorityConstants.UnAnsweredDaysGap;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstUnansweredLeads = lstPriorityModel.Where(x => (x.Call == null || (x.Call.TotalTT == 0 && x.Call.NANC_Attempts <= 4))
                                                          && (x.CallBack == null || DateTime.Now.Subtract(x.CallBack.CBtime).TotalSeconds > 0)// no future CB ..To handle existing CB Lead with tt =0
                                                          && (x.Call == null || (x.Call.TalkTime == 0 && DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC))// Not visible if NANC in last 30 mints
                                                          && (x.Call == null || CurrentTime.Subtract(x.Call.calltime).TotalDays > UnAnsweredDaysGap ||  //no shift check for lead called more than 2 days earlier 
                                                                                      (
                                                                                      ((x.Call.NANC_Attempts < NANCMaxAttemInDiffShifts) && ((x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < NANCMax1DayAttempt) && (x.Call.Shifts == null || !x.Call.Shifts.Contains(CurrentCallShift)))
                                                                                       || (x.Call.NANC_Attempts == NANCMaxAttemInDiffShifts && DateTime.Now.Date > x.Call.calltime.Date)// 7th Call on next day                                                                                       
                                                                                       || (x.Call.NANC_Attempts > NANCMaxAttemInDiffShifts && DateTime.Now.Date.Subtract(x.Call.calltime.Date).TotalDays >= NANCLastCallDayGap)// 8th and 9th Call on alternate days
                                                                                       )
                                                                                      )
                                                          )
                                                         .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.UnansweredRecentLeads, counter)).OrderByDescending(x => x.LeadCreatedOn).ToList();//neverAnsweredCall
                }
            }
            catch (Exception)
            {

            }
            return lstUnansweredLeads;
        }
    }
}
