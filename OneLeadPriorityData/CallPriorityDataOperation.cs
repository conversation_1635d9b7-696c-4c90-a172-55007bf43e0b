﻿using System;
using PropertyLayers;
using MongoDB.Driver;
using MongoDB.Driver.Builders;
using DataAccessLayer;

namespace OneLeadPriorityData
{
	public class CallPriorityDataOperation
	{
        Int16 ValidUnasnweredAttempt = -1;
        public CallPriorityDataOperation()
		{
			
		}
		public bool IsFirstCalloftheDay(PriorityModel reqPriorityModel, PriorityModel respPriorityModel)
		{
			if (respPriorityModel.Call == null || (respPriorityModel.Call.calltime.Date != reqPriorityModel.Call.calltime.Date))
				return true;
			else
				return false;
		}

        public void AnsweredCall(PriorityModel reqPriorityModel, PriorityModel respPriorityModel, UpdateBuilder<PriorityModel> update,CallData _Call)
        {
            _Call.TodayAnswered =Convert.ToInt16(_Call.TodayAnswered + 1);
            _Call.TodaysAttempt= Convert.ToInt16(_Call.TodaysAttempt + 1);
            _Call.LastConnectedCall = reqPriorityModel.Call.calltime; // set last call connect time                                                   
            if (reqPriorityModel.Call.TalkTime >= 15)
            {
                _Call.LastNminuteNANCeAttempts= 0;
            }
        }

        public void NotAnsweredCall(PriorityModel reqPriorityModel, PriorityModel respPriorityModel, UpdateBuilder<PriorityModel> update)
        {
			if(IsFirstCalloftheDay(reqPriorityModel, respPriorityModel))
			{
                update = update.Set(p => p.Call.TodayAnswered, 0);

                if (IsValidNANC(reqPriorityModel))
                {
                    ValidUnasnweredAttempt = 1;
                    update = update.Set(p => p.Call.TodaysNANCAttempt, 1);
                    update = update.Set(p => p.Call.LastNminuteNANCeAttempts, 1);
                    update = update.Set(p => p.Call.TodaysAttempt, 1);// updating today's attempt                                    
                }
                else
                {
                    ValidUnasnweredAttempt = 0;
                    update = update.Set(p => p.Call.TodaysNANCAttempt, 0);
                    update = update.Set(p => p.Call.LastNminuteNANCeAttempts, 0);
                }
            }
            else
            {
                if (IsValidNANC(reqPriorityModel))
                {
                    ValidUnasnweredAttempt = 1;
                    update = update.Set(p => p.Call.TodaysNANCAttempt, respPriorityModel.Call.TodaysNANCAttempt + 1)// updating today's NANCattempt                                          
                                   .Set(p => p.Call.TodaysAttempt, respPriorityModel.Call.TodaysAttempt + 1);// updating the Today's attempt
                    if (respPriorityModel.Call.LastNminuteNANCeAttempts == 0)
                        update = update.Set(p => p.Call.LastNminuteNANCeAttempts, 1);
                    else if (reqPriorityModel.Call.LastCallTime != DateTime.MinValue && (reqPriorityModel.Call.calltime - reqPriorityModel.Call.LastCallTime).TotalMinutes < 30)
                        update = update.Set(p => p.Call.LastNminuteNANCeAttempts, respPriorityModel.Call.LastNminuteNANCeAttempts + 1);
                    else if (respPriorityModel.Call.LastNminuteNANCeAttempts >= 2)
                        update = update.Set(p => p.Call.LastNminuteNANCeAttempts, respPriorityModel.Call.LastNminuteNANCeAttempts + 1);
                }                
            }
            if(respPriorityModel.CallReleaseCount > 0)
                update = update.Set(p => p.CallReleaseCount, respPriorityModel.CallReleaseCount - 1); //reducing release count            
        }

        private bool IsValidNANC(PriorityModel reqPriorityModel)
        {
            string[] _dispositions = { "403", "404", "408", "500" };
            if (reqPriorityModel.Call != null && reqPriorityModel.Call.Duration > 2 && reqPriorityModel.Call.TalkTime < 15)
            {
                if (!string.IsNullOrEmpty(reqPriorityModel.Call.Disposition) && _dispositions.Contains(reqPriorityModel.Call.Disposition))
                {
                    return false;
                }
                if (!string.IsNullOrEmpty(reqPriorityModel.Call.Disposition) && reqPriorityModel.Call.Disposition == "503" && reqPriorityModel.Call.Duration < 10)
                {
                    return false;
                }

                return true;
            }
            else
                return false;
        }

        private void steValidInvalidNANC(PriorityModel reqPriorityModel, PriorityModel respPriorityModel, UpdateBuilder<PriorityModel> update)
        {
            // weekly attempts
            Int16 WeekAttempts = 0;
            try
            {
                if (IsValidNANC(reqPriorityModel))
                {
                    Int16 currentWeek = Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(reqPriorityModel.Call.calltime.Date.Subtract(Convert.ToDateTime(respPriorityModel.User.FirstAssignedOn).Date).TotalDays) / 7) + 1);
                    if (respPriorityModel.Call == null || (respPriorityModel.Call != null && currentWeek != respPriorityModel.Call.Current_Week))
                    {
                        update = update.Set(p => p.Call.Week_Attempt, 1)
                                        .Set(p => p.Call.Current_Week, currentWeek);
                        WeekAttempts = 1;
                    }
                    else
                    {
                        if (respPriorityModel.Call != null)
                        {
                            update = update.Set(p => p.Call.Week_Attempt, respPriorityModel.Call.Week_Attempt + 1)
                                            .Set(p => p.Call.Current_Week, currentWeek);
                            WeekAttempts = Convert.ToInt16(respPriorityModel.Call.Week_Attempt + 1);
                        }
                        else
                        {
                            update = update.Set(p => p.Call.Week_Attempt, 1)
                                            .Set(p => p.Call.Current_Week, currentWeek);
                            WeekAttempts = 1;
                        }
                    }
                }
                else if (respPriorityModel.Call != null && reqPriorityModel.Call.TalkTime >= 240)
                {
                    CallData _CallData = LeadPrioritizationDLL.SetAnsWeekAttempts(respPriorityModel);

                    update = update.Set(x => x.Call.Current_Week, _CallData.Current_Week)
                                   .Set(p => p.Call.Week_Attempt, _CallData.Week_Attempt);

                    WeekAttempts = _CallData.Week_Attempt;
                }
            }
            catch (Exception ex)
            {
                //LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(respPriorityModel.LeadID), 0, ex.ToString(), "WeekAttempts", "LeadPrioritizationBLL", "Communication", JsonConvert.SerializeObject(respPriorityModel), string.Empty, DateTime.Now, DateTime.Now);
            }
        }
    }
}

