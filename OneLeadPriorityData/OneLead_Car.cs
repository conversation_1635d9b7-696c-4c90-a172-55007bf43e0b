﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PropertyLayers;
using Helper;
using DataAccessLibrary;
using DataAccessLayer;

namespace OneLeadPriorityData
{
    public class OneLead_Car : IOneLead
    {
        readonly productPriorityconstant _LeadPriorityConstants;
        readonly Int16 _productID;
        readonly OneLeadData _OneLeadData;
        Int16 counter = 0;
        readonly Int16 _GroupID = 0;
        readonly OneLeadParams _oneLeadParams;

        public OneLead_Car(short productID, Int16 GroupID, OneLeadParams oneLeadParams)
        {
            // TODO: Complete member initialization
            if (productID != 117)
                productID = 117;
            this._GroupID = GroupID;
            this._productID = productID;
            _LeadPriorityConstants = getPriorityConfigByProduct(productID);
            _OneLeadData = new OneLeadData(productID, _LeadPriorityConstants, counter);
            _oneLeadParams = oneLeadParams;
        }


        // Getting Top PriorityLead say 5Leads
        public List<PriorityModel> getOneLead(Int16 ProductId, Int64 UserId, Int16 priorityleadCount, bool productCheck = false)
        {
            List<PriorityModel> lstPriorityModel = new List<PriorityModel>();
            //Int16 PriorityQueueSize = _LeadPriorityConstants.PriorityQueueSize;
            //if (priorityleadCount == 1)
            //    PriorityQueueSize = 1;
            //var priorityQueuelist=_LeadPriorityConstants
            List<PriorityModel> lstAgentAssignedLeads = LeadPrioritizationDLL.GetAgentActiveLeads(UserId, ProductId, false, _oneLeadParams, productCheck, _GroupID);// getting Agent all Active Leads
            List<PriorityModel> lstRest1stAttemptLeads = new List<PriorityModel>();

            lstPriorityModel = getPriorityQueue(lstPriorityModel, lstAgentAssignedLeads, lstRest1stAttemptLeads, priorityleadCount);
            return lstPriorityModel;

        }
        // Get all active Leads of an agent        


        // get agent priority queue
        public List<PriorityModel> getPriorityQueue(List<PriorityModel> lstPriorityModel, List<PriorityModel> lstAgentAssignedLeads, List<PriorityModel> lstRest1stAttemptLeads, Int16 PriorityQueueSize, Int16 ProductID = 0, Int64 UserID = 0)
        {
            try
            {
                foreach (var prioritySequence in _LeadPriorityConstants.prioritySequence)
                {
                    List<PriorityModel>? _filteredleads = null;
                    switch (prioritySequence.ToUpper())
                    {
                        case "ACTIVEREVISIT":
                            _filteredleads = _OneLeadData.GetActiveRevisitLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTACTIVEREVISIT":
                            _filteredleads = _OneLeadData.Get2ndAttemptActiveRevisit(lstAgentAssignedLeads, counter); // 2nd attempt act revisit 
                            break;
                        case "ACTIVENEW":
                            _filteredleads = _OneLeadData.GetActiveNewLead(lstAgentAssignedLeads, counter);  // active new
                            break;
                        case "2NDATTEMPTACTIVENEW":
                            _filteredleads = _OneLeadData.Get2ndAttemptActiveNew(lstAgentAssignedLeads, counter);  // 2nd attempt active new
                            break;
                        case "PAYMENTCB":
                            _filteredleads = _OneLeadData.GetPaymentCBLead(lstAgentAssignedLeads, counter);  // payment cb
                            break;
                        case "2NDATTEMPTPCB":
                            _filteredleads = _OneLeadData.Get2ndAttemptPCBLeads(lstAgentAssignedLeads, counter);  // 2nd attempt payment cb
                            break;
                        case "EMAILREVISIT":
                            _filteredleads = _OneLeadData.GetEmailRevisitLeads(lstAgentAssignedLeads, counter);  // Email Revisit
                            break;
                        case "2NDATTEMPTEMAILREVISIT":
                            _filteredleads = _OneLeadData.Get2ndAttemptEmailRevisit(lstAgentAssignedLeads, counter);  // 2nd attempt Email Revisit
                            break;
                        case "PAYMENTFAILURE":
                            _filteredleads = OneLeadData.GetPaymentFailureLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTPAYMENTFAILURE":
                            _filteredleads = _OneLeadData.Get2ndAttemptPaymentFailureLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "ACTIVECB":
                            _filteredleads = _OneLeadData.GetActiveCBLeads(lstAgentAssignedLeads, counter);  // active call back
                            break;
                        case "2NDATTEMPTACTIVECB":
                            _filteredleads = _OneLeadData.Get2ndAttemptActiveCB(lstAgentAssignedLeads, counter);  // 2nd atmpactive call back
                            break;
                        case "PASSIVENEW":
                            _filteredleads = _OneLeadData.GetPassiveNewLead(lstAgentAssignedLeads, counter); // passive new
                            break;
                        case "2NDATTEMPTPASSIVENEW":
                            _filteredleads = _OneLeadData.Get2ndAttemptPassiveNew(lstAgentAssignedLeads, counter); // 2nd atmp passive new
                            break;
                        case "PASSIVEREVISIT":
                            _filteredleads = _OneLeadData.GetPassiveRevisitLead(lstAgentAssignedLeads, counter); // passive revisit
                            break;
                        case "2NDATTEMPTPASSIVEREVISIT":
                            _filteredleads = _OneLeadData.Get2ndAttemptPassiveRevisit(lstAgentAssignedLeads, counter); // 2nd atmp passive new
                            break;
                        case "PASSIVECB":
                            _filteredleads = _OneLeadData.GetPassiveCBLead(lstAgentAssignedLeads, counter); // passive CALL BACK)
                            break;
                        case "2NDATTEMPTPASSIVECB":
                            _filteredleads = _OneLeadData.Get2ndAttemptPassiveCB(lstAgentAssignedLeads, counter); // 2nd atmp passive CALL BACK)
                            break;
                        case "TODAYEXPIRY":
                            _filteredleads = TodayExpiry(lstAgentAssignedLeads, counter);// expiryLeads
                            break;
                        case "EXPIRY":
                            _filteredleads = GetExpiryDateLeads(lstAgentAssignedLeads, counter);// expiryLeads
                            break;
                        case "CALLRELEASE":
                            _filteredleads = CallReleaseLeads(lstAgentAssignedLeads, counter);   // call released
                            break;
                        case "UNANSWERED":
                            _filteredleads = _OneLeadData.GetUnansweredLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "REVISITCTC":
                            _filteredleads = _OneLeadData.GetCTCRevisitLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTREVISITCTC":
                            _filteredleads = _OneLeadData.Get2ndAttemptCTCRevisit(lstAgentAssignedLeads, counter);
                            break;
                        case "ANSWERED":
                            _filteredleads = _OneLeadData.GetAnsweredLeads(lstAgentAssignedLeads, counter,_GroupID);
                            break;
                        case "PROPOSALERROR":
                            _filteredleads = _OneLeadData.GetProposalErrorLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "RMLEAD":
                            _filteredleads = OneLeadData.GetRMLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "SERVICECB":
                            _filteredleads = _OneLeadData.GetServiceCBLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "REST":
                            _filteredleads = _OneLeadData.GetRest_1Leads(lstAgentAssignedLeads, counter, _GroupID);
                            if (_filteredleads != null)
                                lstRest1stAttemptLeads.AddRange(_filteredleads.Where(p => !lstPriorityModel.Exists(X => X.LeadID == p.LeadID)));
                            break;
                    }

                    if (_filteredleads != null && _filteredleads.Count > 0)
                        lstPriorityModel.AddRange(_filteredleads.Where(p => !lstPriorityModel.Exists(X => X.LeadID == p.LeadID)));
                    if (lstPriorityModel.Count >= PriorityQueueSize)
                        return lstPriorityModel;

                    counter += 1;
                }
            }
            catch (Exception)
            {

            }

            finally
            {
                LeadPrioritizationDLL.UpdateRestFlag(lstRest1stAttemptLeads);
            }

            return lstPriorityModel;
        }


        public List<PriorityModel> getBookedLead(Int16 ProductId, Int64 UserId, Int16 priorityleadCount)
        {
            return new List<PriorityModel>();
        }              
        public List<PriorityModel> GetExpiryDateLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstTodayExpiry = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                Int16 TodyCreatdExprGap = _LeadPriorityConstants.TodayCreatedExprTimeGap;
                Int16 BefTodyCreatdExprGap = _LeadPriorityConstants.BeforeTodayCreatedExprTimeGap;
                byte ExpiryCount = _LeadPriorityConstants.ExpiryLeadsShowCount;
                DateTime ct = DateTime.Now;
                byte RecentExpiryDayGap = _LeadPriorityConstants.RecentExpiryGap;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstTodayExpiry = lstPriorityModel
                                       .Where(x => //(Math.Abs(ct.Subtract(x.PrevPolicyExpDate).TotalDays)  <= RecentExpiryDayGap)
                                           (x.PrevPolicyExpDate.Date == ct.Date || x.PrevPolicyExpDate.Date == ct.AddDays(1).Date || x.PrevPolicyExpDate.Date == ct.AddDays(-1).Date)// Expiring today or tomorrow or expired one day before
                                          && (x.CallBack == null || x.CallBack.CBtime < ct) // no future call back
                                           && (x.Call != null && ct.Subtract(x.Call.calltime).TotalMinutes > TodyCreatdExprGap) // for today created check                                           
                                           )
                                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.RecentExpiry, counter))
                                        .OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstTodayExpiry;
        }

        public List<PriorityModel> TodayExpiry(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstTodayExpiry = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                Int16 TodyCreatdExprGap = _LeadPriorityConstants.TodayCreatedExprTimeGap;
                Int16 BefTodyCreatdExprGap = _LeadPriorityConstants.BeforeTodayCreatedExprTimeGap;
                byte ExpiryCount = _LeadPriorityConstants.ExpiryLeadsShowCount;
                DateTime ct = DateTime.Now;
                byte RecentExpiryDayGap = _LeadPriorityConstants.RecentExpiryGap;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstTodayExpiry = lstPriorityModel
                                       .Where(x => //(Math.Abs(ct.Subtract(x.PrevPolicyExpDate).TotalDays)  <= RecentExpiryDayGap)
                                           (x.PrevPolicyExpDate.Date == ct.Date)// Expiring today or tomorrow or expired one day before
                                               && (x.CallBack == null || x.CallBack.CBtime < ct) // no future call back
                                               && (x.Call != null && ct.Subtract(x.Call.calltime).TotalMinutes > TodyCreatdExprGap) // for today created check                                           
                                           )
                                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.TodayExpiry, counter))
                                        .OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstTodayExpiry;
        }

        public productPriorityconstant getPriorityConfigByProduct(Int16 productID)
        {
            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            var obj = _LeadPriorityConstants.PriorityConstant.Where(p => (p.productID == productID) && (p.subProduct == "")).SingleOrDefault();
            if (obj == null)
                throw new NotImplementedException("getPriorityConfigByProduct config is empty");
            return obj;

        }

        public static List<PriorityModel> CallReleaseLeads(List<PriorityModel> lstAgentActiveLeads, Int16 counter)
        {
            List<PriorityModel> lstReleasedCallleads = new List<PriorityModel>();
            try
            {
                DateTime ct = DateTime.Now;
                List<PriorityModel> lstAgentLeads = new List<PriorityModel>(lstAgentActiveLeads);
                LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
                if (lstAgentActiveLeads != null && lstAgentActiveLeads.Count > 0)
                {
                    lstReleasedCallleads = lstAgentLeads
                                       .Where(x => (x.CallReleaseCount > 0)
                                                    // && (x.Call != null && DateTime.Now.Subtract(x.Call.calltime).TotalMinutes < _LeadPriorityConstants.Releaseleadsshowtime)
                                                    && (x.SkippingTime < x.CallReleaseTime) //  so that if release after skipping then it should come in priority
                                              ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.CallReleasedLeads, counter)).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstReleasedCallleads;
        }

    }
}
