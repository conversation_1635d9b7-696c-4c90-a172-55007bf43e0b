﻿using DataAccessLibrary;
using PropertyLayers;
using ReadXmlProject;
using DataAccessLayer;

namespace OneLeadPriorityData
{
    class OneLeadData
    {
        readonly productPriorityconstant _LeadPriorityConstants;
        readonly Int16 _productID = 0;
        static List<OneLeadScoreMapping> _OneLeadScoreMapping = new();
        readonly Int16 counter;
        //List<Int16> bucketutilizationGroups = new List<short>() { 895, 1488, 1518 };
        List<Int16> bucketutilizationProducts = new List<short>() { 2, 117 };
        List<Int16> HNI2ndAttemptexpGroups = new List<short>() { 39, 64, 1102 };
        List<Int16> HealthrenewalexpGroups = new List<short>() { 723, 493 };
        public OneLeadData(short productID, productPriorityconstant _LeadPriorityConstants, Int16 counter)
        {
            // TODO: Complete member initialization
            this._productID = productID;
            this._LeadPriorityConstants = _LeadPriorityConstants;
            this.counter = counter;
        }


        /**
         * Get only Email revisit leads
         * */
        public List<PriorityModel> GetEmailRevisitLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstActiveRevisitLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstActiveRevisitLeads = lstPriorityModel
                                      .Where(x => (x.Revisit != null)
                                            && (x.Revisit.RevisitType == RevisitType.Email)
                                            && (x.Revisit.ts > DateTime.Now.Date)
                                            && (x.Appointment == null || x.Appointment.AppointmentId == 0 || (AppointmentEnumStatus)x.Appointment.StatusID == AppointmentEnumStatus.Cancelled)
                                            && (x.Call == null || (x.Revisit.ts > x.Call.calltime.AddSeconds(TimeDiffDailer + x.Call.TalkTime))) // No call attempt after revisitTime
                                            && (x.SkippingTime < x.Revisit.ts))
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.EmailRevisit, counter))
                  .OrderByDescending(x => x.Revisit.ts).ToList();
                }
                //lstActiveRevisitLeads.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.ActiveRevisit);// setting the leadCategory
            }
            catch (Exception)
            {

            }

            return lstActiveRevisitLeads;
        }

        /**
         * Get only Bajaj revisit leads
         * */
        public List<PriorityModel> GetBajajRevisitLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstActiveRevisitLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstActiveRevisitLeads = lstPriorityModel
                                      .Where(x => (x.Revisit != null)
                                            && (x.Revisit.RevisitType == RevisitType.BajajCustomer)
                                            && (x.Call == null || (x.Revisit.ts > x.Call.calltime.AddSeconds(TimeDiffDailer + x.Call.TalkTime))) // No call attempt after revisitTime
                                            && (x.SkippingTime < x.Revisit.ts))
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.BajajCustomerRevisit, counter))
                  .OrderByDescending(x => x.Revisit.ts).ToList();
                }
                //lstActiveRevisitLeads.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.ActiveRevisit);// setting the leadCategory
            }
            catch (Exception)
            {

            }
            return lstActiveRevisitLeads;
        }

        /**
         * Get Active revisitleads without Email
         * 
         * */
        public List<PriorityModel> GetActiveRevisitLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 ProductID = 0, Int64 UserID = 0)
        {
            List<PriorityModel> lstActiveRevisitLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                List<Int64> _RevistRestrictionUsers = _LeadPriorityConstants.RevistRestrictionUsers;
                List<RevisitType> Revisit = new List<RevisitType>() { RevisitType.Email, RevisitType.QuoteShared, RevisitType.RevisionShared };
                List<short> RestrictedProducts = new List<short>() { 2, 7, 115 };

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstActiveRevisitLeads = lstPriorityModel
                                      .Where(x => (x.Revisit != null) && (DateTime.Now.Subtract(x.Revisit.ts).TotalMinutes < ActiveRevisitShowTime)// revisit in last 30 min
                                            && (x.Appointment == null || x.Appointment.AppointmentId == 0 || (AppointmentEnumStatus)x.Appointment.StatusID == AppointmentEnumStatus.Cancelled)
                                            && (!Revisit.Contains(x.Revisit.RevisitType))
                                            && (_RevistRestrictionUsers == null || _RevistRestrictionUsers.Count == 0 || x.CallBack == null || (!_RevistRestrictionUsers.Contains(UserID)) || x.Revisit.ts > x.CallBack.CBtime) // condition if future call back is there then do not pick revisit into priority
                                            && (x.CallBack == null || x.Revisit.ts > x.CallBack.CBtime || (!RestrictedProducts.Contains(ProductID)))
                                            && (x.Call == null || (x.Revisit.ts > x.Call.calltime.AddSeconds(TimeDiffDailer + x.Call.TalkTime))) // No call attempt after revisitTime
                                            && (x.SkippingTime < x.Revisit.ts))
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.ActiveRevisit, counter))
                  .OrderByDescending(x => x.Revisit.ts).ToList();
                }
                //lstActiveRevisitLeads.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.ActiveRevisit);// setting the leadCategory
            }
            catch (Exception)
            {

            }
            return lstActiveRevisitLeads;
        }


        // Get Email Revisit for 2nd Attempt
        public List<PriorityModel> Get2ndAttemptEmailRevisit(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID = 0)
        {
            List<PriorityModel> lst2ndAttemptActiveRevisit = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_Revisit;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptActiveRevisit = lstPriorityModel.Where(x =>
                                                                           (x.Revisit != null && x.Call != null && x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysAttempt == 1)
                                                                        && (x.Call.TalkTime == 0)    // last Attempt NANC
                                                                        && (x.Revisit.RevisitType == RevisitType.Email)
                                                                        && (x.Call.ReasonID == (short)LeadCategoryEnum.EmailRevisit)
                                                                        && (x.Appointment == null || x.Appointment.AppointmentId == 0 || (AppointmentEnumStatus)x.Appointment.StatusID == AppointmentEnumStatus.Cancelled)
                                                                        && (x.Revisit.ts <= x.Call.calltime && x.Call.calltime <= x.Revisit.ts.AddMinutes(ActiveRevisitShowTime)) // last NANC Attempt was within 30 mints of  the revisit time                                                                        
                                                                        && (DateTime.Now.Subtract(x.Call.calltime).TotalMinutes > InvisibleTimeNANC)// last NANC call was before 30 mints
                                                                        && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || ((_productID == 2 && x.Call.TodaysAttempt < 2) || (_productID == 117 && x.Call.TodaysAttempt < 1))) // bucket utilization                                                                                                          //.Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptActvRevisit)).OrderByDescending(x => x.Revisit.ts).ToList();
                                                                      )

                                                             .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptEmailRevisit, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lst2ndAttemptActiveRevisit;
        }


        // Get ActiveRevisit for 2nd Attempt without Email
        public List<PriorityModel> Get2ndAttemptActiveRevisit(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 ProductID = 0, Int64 UserID = 0, Int16 _GroupID = 0)
        {
            List<PriorityModel> lst2ndAttemptActiveRevisit = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_Revisit;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                List<Int64> _RevistRestrictionUsers = _LeadPriorityConstants.RevistRestrictionUsers;
                List<RevisitType> Revisit = new List<RevisitType>() { RevisitType.Email, RevisitType.QuoteShared, RevisitType.RevisionShared };
                List<short> RestrictedProducts = new List<short>() { 2, 7, 115 };

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptActiveRevisit = lstPriorityModel.Where(x =>
                                                                          (x.Revisit != null && x.Call != null && x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysAttempt == 1)
                                                                       && (x.Call.TalkTime == 0)
                                                                       && (x.Call.ReasonID == (short)LeadCategoryEnum.ActiveRevisit)
                                                                       && (!Revisit.Contains(x.Revisit.RevisitType))
                                                                       && (_RevistRestrictionUsers == null || _RevistRestrictionUsers.Count == 0 || x.CallBack == null || (!_RevistRestrictionUsers.Contains(UserID)) || x.Revisit.ts > x.CallBack.CBtime) // condition if future call back is there then do not pick revisit into priority
                                                                       && (x.CallBack == null || x.Revisit.ts > x.CallBack.CBtime || (!RestrictedProducts.Contains(ProductID)))
                                                                       && (x.Appointment == null || x.Appointment.AppointmentId == 0 || (AppointmentEnumStatus)x.Appointment.StatusID == AppointmentEnumStatus.Cancelled)
                                                                       && (x.Revisit.ts <= x.Call.calltime && x.Call.calltime <= x.Revisit.ts.AddMinutes(ActiveRevisitShowTime)) // last NANC Attempt was within 30 mints of  the revisit time
                                                                       && (x.Call.NANC_Attempts % 2 == 1)// odd attempts
                                                                       && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)
                                                                       && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || ((_productID == 2 && x.Call.TodaysAttempt < 2) || (_productID == 117 && x.Call.TodaysAttempt < 1))) // bucket utilization
                                                                       )
                                                             .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptActvRevisit, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lst2ndAttemptActiveRevisit;
        }

        // GetPassiveRevistLead
        public List<PriorityModel> GetPassiveRevisitLead(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 ProductID = 0, Int64 UserID = 0, Int16 _GroupID = 0)
        {
            List<PriorityModel> lstPassiveRevisitLead = new List<PriorityModel>();
            try
            {
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short PassivEnd = _LeadPriorityConstants.PassiveRevisitEndMints;
                List<Int64> _RevistRestrictionUsers = _LeadPriorityConstants.RevistRestrictionUsers;
                List<RevisitType> Revisit = new List<RevisitType>() { RevisitType.Email, RevisitType.QuoteShared, RevisitType.RevisionShared };
                List<short> RestrictedProducts = new List<short>() { 2, 7, 115 };

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstPassiveRevisitLead = lstPriorityModel.Where(x => (x.Revisit != null)
                                                               && (!Revisit.Contains(x.Revisit.RevisitType))
                                                               && (_RevistRestrictionUsers == null || _RevistRestrictionUsers.Count == 0 || x.CallBack == null || (!_RevistRestrictionUsers.Contains(UserID)) || x.Revisit.ts > x.CallBack.CBtime) // condition if future call back is there then do not pick revisit into priority
                                                               && (x.CallBack == null || x.Revisit.ts > x.CallBack.CBtime || (!RestrictedProducts.Contains(ProductID)))
                                                               && (DateTime.Now.Subtract(x.Revisit.ts).TotalMinutes > ActiveRevisitShowTime)// revisit Time over by 30 mints
                                                               && (DateTime.Now.Subtract(x.Revisit.ts).TotalMinutes < PassivEnd)  //  before 24 hrs
                                                               && (x.Appointment == null || x.Appointment.AppointmentId == 0 || (AppointmentEnumStatus)x.Appointment.StatusID == AppointmentEnumStatus.Cancelled)
                                                               && (x.Call == null || (x.Call.calltime.AddSeconds(TimeDiffDailer + x.Call.TalkTime) < x.Revisit.ts)) // no call after revisitTime
                                                               && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                                                               )
                                                                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.PassiveRevisit, counter))
                                           .OrderByDescending(x => x.Revisit.ts).ToList();
                }
                //lstPassiveRevisitLead.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.PassiveRevisit);// setting the leadCategory
            }
            catch (Exception)
            {

            }
            return lstPassiveRevisitLead;
        }


        //Get Passive Revisit for 2nd Attempt
        public List<PriorityModel> Get2ndAttemptPassiveRevisit(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 ProductID = 0, Int64 UserID = 0, Int16 _GroupID = 0)
        {
            List<PriorityModel> lst2ndAttemptPassiveRevisit = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_Revisit;
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short PassEnd = _LeadPriorityConstants.PassiveRevisitEndMints;
                List<Int64> _RevistRestrictionUsers = _LeadPriorityConstants.RevistRestrictionUsers;
                List<RevisitType> Revisit = new List<RevisitType>() { RevisitType.Email, RevisitType.QuoteShared, RevisitType.RevisionShared };
                List<short> RestrictedProducts = new List<short>() { 2, 7, 115 };

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptPassiveRevisit = lstPriorityModel.Where(x =>
                                                                           (x.Call != null && x.Revisit != null && x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysAttempt == 1)
                                                                        && (x.Call.TalkTime == 0)  // last Attempt NANC
                                                                        && (x.Call.ReasonID == (short)LeadCategoryEnum.PassiveRevisit)
                                                                        && (!Revisit.Contains(x.Revisit.RevisitType))
                                                                        && (x.Appointment == null || x.Appointment.AppointmentId == 0 || (AppointmentEnumStatus)x.Appointment.StatusID == AppointmentEnumStatus.Cancelled)
                                                                        && (x.IsLastBucketRest == false) // lastly not in RestBucket
                                                                        && (_RevistRestrictionUsers == null || _RevistRestrictionUsers.Count == 0 || x.CallBack == null || (!_RevistRestrictionUsers.Contains(UserID)) || x.Revisit.ts > x.CallBack.CBtime) // condition if future call back is there then do not pick revisit into priority
                                                                        && (x.CallBack == null || x.Revisit.ts > x.CallBack.CBtime || (!RestrictedProducts.Contains(ProductID)))
                                                                        && (DateTime.Now.Subtract(x.Call.calltime).TotalMinutes > InvisibleTimeNANC)// last NANC Was before 30 mints (means moving out of bucket)
                                                                        && (x.Revisit.ts.AddMinutes(ActiveRevisitShowTime) <= x.Call.calltime)  // last Attempt was after  30 min of Revisit time
                                                                        && (x.Revisit.ts.AddMinutes(PassEnd) >= x.Call.calltime)  //last attemmpt between 30 min to 24 hrs after revisit time
                                                                        && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || ((_productID == 2 && x.Call.TodaysAttempt < 2) || (_productID == 117 && x.Call.TodaysAttempt < 1))) // bucket utilization
                                                                        )//.Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptPasvRevisit)).OrderByDescending(x => x.Revisit.ts).ToList();
                                                                       .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptPasvRevisit, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lst2ndAttemptPassiveRevisit;
        }


        //Get ALL ActiveNew Lead in last 30 mints
        public List<PriorityModel> GetActiveNewLead(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstActiveNewLead = new List<PriorityModel>();
            try
            {
                List<PriorityModel> lstAgentLeads = new List<PriorityModel>();
                lstAgentLeads.AddRange(lstPriorityModel);
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstActiveNewLead = lstAgentLeads
                                       .Where(x =>
                                                (
                                                    (x.InvestmentTypeId == 14 && (DateTime.Now.Subtract(x.LeadCreatedOn).TotalMinutes <= (ActivNewLeadShowTime + 2)))
                                                    ||
                                                    (x.InvestmentTypeId != 14 && (DateTime.Now.Subtract(x.LeadCreatedOn).TotalMinutes <= ActivNewLeadShowTime))
                                                )
                                                // visible for 32 mints after leadCreation for PI else 30 mints
                                                && (x.Call == null)
                                                && (x.CustomerCalltime == DateTime.MinValue || x.CustomerCalltime.Date < DateTime.Now.Date || x.CustomerTodayNANC < 2 || DateTime.Now.Subtract(x.CustomerCalltime).TotalMinutes > 30)
                                              )// means lead is not talked yet i.e lead is new 
                                       .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.ActiveNew, counter))
                                       .OrderByDescending(x => x.LeadCreatedOn).ToList();
                }
                //lstActiveNewLead.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.ActiveNew);// setting the leadCategory
            }
            catch (Exception)
            {

            }
            return lstActiveNewLead;
        }


        // Get ActiveNew for 2nd Attempt
        public List<PriorityModel> Get2ndAttemptActiveNew(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 GroupID = 0)
        {
            List<PriorityModel> lst2ndAttemptActiveNew = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_New;
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptActiveNew = lstPriorityModel.Where(x =>
                                                                        (x.Call != null && x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysAttempt == 1)
                                                                    && (x.Call.TalkTime == 0)
                                                                    && (x.Call.ReasonID == (short)LeadCategoryEnum.ActiveNew)
                                                                    && x.Call.calltime.AddSeconds(TimeDiffDailer) >= x.LeadCreatedOn
                                                                    && x.Call.calltime.AddSeconds(TimeDiffDailer) <= x.LeadCreatedOn.AddMinutes(ActivNewLeadShowTime) // last  NANC attempt was within 30 mints of leadCreation
                                                                    && (x.Call.NANC_Attempts == 1 && x.Call.CallAttempts == 1)
                                                                    && (
                                                                            ((!HNI2ndAttemptexpGroups.Contains(GroupID) || x.LeadID % 2 == 0) && DateTime.Now.Subtract(x.Call.calltime).TotalMinutes > InvisibleTimeNANC)
                                                                            ||
                                                                            ((HNI2ndAttemptexpGroups.Contains(GroupID) && x.LeadID % 2 != 0) && DateTime.Now.Subtract(x.Call.calltime).TotalMinutes > 60)
                                                                        )

                                                                    )// last NANC call was before 30 mints  
                                                                     //.Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptActvNew)).OrderByDescending(x => x.LeadCreatedOn).ToList();
                                                            .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptActvNew, counter)).OrderBy(x => x.Call.calltime).ToList();
                }

            }
            catch (Exception)
            {

            }

            return lst2ndAttemptActiveNew;
        }

        //Get PassiveNewLead
        public List<PriorityModel>? GetPassiveNewLead(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 GroupID = 0)
        {
            List<PriorityModel> lstPassiveNewLead = new List<PriorityModel>();
            List<DateTime> todayExpCall = _LeadPriorityConstants.TodayExpCall;
            try
            {
                Int16 ActiveNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstPassiveNewLead = lstPriorityModel.Where(x => ((DateTime.Now.Subtract(x.LeadCreatedOn).TotalMinutes > ActiveNewLeadShowTime)// after 30 mints
                                                               && (x.Call == null)
                                                               && (x.CustomerCalltime == DateTime.MinValue || x.CustomerCalltime.Date < DateTime.Now.Date || x.CustomerTodayNANC < 2 || DateTime.Now.Subtract(x.CustomerCalltime).TotalMinutes > 30)
                                                               )

                                                               //||

                                                               //(
                                                               //x.User.Reassigned
                                                               //&& (DateTime.Now.Subtract(x.User.AssignedOn).TotalMinutes > ActiveNewLeadShowTime)
                                                               //&& (x.Call == null || x.Call.calltime < x.User.AssignedOn))
                                                               )// no call attempts
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.PassiveNew, counter))
                  .OrderByDescending(x => x.LeadCreatedOn).ToList();

                    if (todayExpCall.Contains(DateTime.Now.Date) && lstPassiveNewLead != null && lstPassiveNewLead.Count > 0)
                    {
                        lstPassiveNewLead = lstPassiveNewLead.Where(x => x.LeadCreatedOn.Date == DateTime.Now.Date).ToList();
                    }
                }
                //lstPassiveNewLead.Where(x => x.LeadCategory == LeadCategoryEnum.Default).ToList().ForEach(x => x.LeadCategory = LeadCategoryEnum.PassiveNew);// setting the leadCategory
            }
            catch (Exception)
            {

            }

            return lstPassiveNewLead;
        }

        //Get Passive New for 2nd Attempt
        public List<PriorityModel> Get2ndAttemptPassiveNew(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 GroupID = 0)
        {
            List<PriorityModel> lst2ndAttemptPassiveNew = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_New;
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                DateTime CurrentTime = DateTime.Now;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptPassiveNew = lstPriorityModel.Where(x =>
                                                                        (
                                                                        (x.Call != null && x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysAttempt == 1)
                                                                     && (x.Call.TalkTime == 0)
                                                                     && (x.Call.ReasonID == (short)LeadCategoryEnum.PassiveNew)
                                                                     && (x.IsLastBucketRest == false) // lastly not in RestBucket
                                                                     && (x.Call.NANC_Attempts == 1 && x.Call.CallAttempts == 1)
                                                                     && (
                                                                            ((!HNI2ndAttemptexpGroups.Contains(GroupID) || x.LeadID % 2 == 0) && DateTime.Now.Subtract(x.Call.calltime).TotalMinutes > InvisibleTimeNANC)
                                                                            ||
                                                                            ((HNI2ndAttemptexpGroups.Contains(GroupID) && x.LeadID % 2 != 0) && DateTime.Now.Subtract(x.Call.calltime).TotalMinutes > 60)
                                                                        )
                                                                     && (x.LeadCreatedOn.AddMinutes(ActivNewLeadShowTime) <= x.Call.calltime.AddSeconds(TimeDiffDailer))
                                                                     ))// last Attempt was done after  30 min of createdOn time
                                                                    .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptPasvNew, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lst2ndAttemptPassiveNew;
        }


        //Get ALL Payment Failure Leads
        public static List<PriorityModel> GetPaymentFailureLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstPaymentFailLeads = new List<PriorityModel>();
            try
            {
                List<PriorityModel> lstAgentLeads = new List<PriorityModel>();
                lstAgentLeads.AddRange(lstPriorityModel);
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstPaymentFailLeads = lstAgentLeads
                                       .Where(x => x.PaymentFailureTime > DateTime.MinValue
                                           && (x.Call == null || x.Call.calltime < x.PaymentFailureTime))
                                       .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.PaymentFailure, counter))
                                       .OrderByDescending(x => x.LeadCreatedOn).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lstPaymentFailLeads;
        }

        //Get 2nd attempt Payment Failure Leads
        public List<PriorityModel> Get2ndAttemptPaymentFailureLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lst2ndAttemptPaymentFailLeads = new List<PriorityModel>();
            try
            {
                List<PriorityModel> lstAgentLeads = new List<PriorityModel>();
                lstAgentLeads.AddRange(lstPriorityModel);
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_PaymentFailure;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptPaymentFailLeads = lstAgentLeads
                                       .Where(x => x.PaymentFailureTime > DateTime.MinValue
                                            && (x.Call != null && x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysAttempt == 1)
                                            && (x.Call.ReasonID == (short)LeadCategoryEnum.PaymentFailure)
                                            && (DateTime.Now.Subtract(x.Call.calltime).TotalMinutes > InvisibleTimeNANC)// last NANC Was before 30 mints (means moving out of bucket)
                                            && (x.Call.calltime < x.PaymentFailureTime)
                                            && (x.Call.NANC_Attempts == 1 && x.Call.CallAttempts == 1)
                                           )
                                       .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptPaymentFailure, counter))
                                       .OrderByDescending(x => x.LeadCreatedOn).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lst2ndAttemptPaymentFailLeads;
        }


        /**
         * Get CTC Revisit leads
         * */
        public List<PriorityModel> GetCTCRevisitLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstCTCLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstCTCLeads = lstPriorityModel
                                      .Where(x => (x.Revisit != null)
                                            && (x.Revisit.RevisitType == RevisitType.Ctc)
                                            && (x.Call == null || (x.Revisit.ts > x.Call.calltime.AddSeconds(TimeDiffDailer + x.Call.TalkTime))) // No call attempt after revisitTime
                                            && (x.SkippingTime < x.Revisit.ts))
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.CTCRevisit, counter))
                  .OrderByDescending(x => x.Revisit.ts).ToList();
                }

            }
            catch (Exception)
            {

            }
            return lstCTCLeads;
        }

        // Get CTC Revisit for 2nd Attempt
        public List<PriorityModel> Get2ndAttemptCTCRevisit(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID = 0)
        {
            List<PriorityModel> lst2ndAttemptCTCRevisit = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_Revisit;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptCTCRevisit = lstPriorityModel.Where(x => (x.Revisit != null && x.Call != null && x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysAttempt == 1)
                                                                        && (x.Call.TalkTime == 0)
                                                                        && (x.Call.ReasonID == (short)LeadCategoryEnum.CTCRevisit)
                                                                        && (x.Revisit.RevisitType == RevisitType.Ctc)
                                                                        && (x.Revisit.ts <= x.Call.calltime && x.Call.calltime <= x.Revisit.ts.AddMinutes(ActiveRevisitShowTime)) // last NANC Attempt was within 30 mints of  the revisit time
                                                                        && (x.Call.NANC_Attempts % 2 == 1)// odd attempts
                                                                        && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC call was before 30 mints
                                                                        && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || ((_productID == 2 && x.Call.TodaysAttempt < 2) || (_productID == 117 && x.Call.TodaysAttempt < 1))) // bucket utilization
                                                                        )
                                                             .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptRevisitCTC, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lst2ndAttemptCTCRevisit;
        }


        public List<PriorityModel> GetBookedCBLeads(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lstActiveCBLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                Int16 ActiveCBEndMints = _LeadPriorityConstants.ActiveCBEndTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstActiveCBLeads = lstPriorityModel.Where(x =>
                                            (x.CallBack != null)
                                            && x.IsBooked == true
                                            && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes >= ActiveCBShowTime * -1) //  show before 5 mints till after 15 mints 
                                            && (x.Call == null || (x.Call.calltime.AddSeconds(TimeDiffDailer) < (x.CallBack.ts > x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1) ? x.CallBack.ts : x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1)))))// no  call after Max(CBSetTime,CBTime-15) 
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.BookedCB, counter))
                  .OrderByDescending(x => Math.Abs(x.CallBack.CBtime.Subtract(DateTime.Now).Seconds)).ToList();// sorting by nearest callback
                }
            }
            catch (Exception)
            {

            }

            return lstActiveCBLeads;
        }

        public List<PriorityModel> GetTicketLeads(List<PriorityModel> lstPriorityModel)
        {
            short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
            List<PriorityModel> lstTicketLeads = new List<PriorityModel>();
            try
            {
                lstTicketLeads = lstPriorityModel.Where(x => (x.IsBooked == true)
                                            && (x.Call == null || (x.Ticket != null && x.Ticket.ts > x.Call.calltime.AddSeconds(TimeDiffDailer)))
                                            )
                    .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.TicketUpdate, counter))
                    .OrderByDescending(x => x.BookedTime).ToList();
            }

            catch (Exception)
            {

            }
            return lstTicketLeads;
        }

        public List<PriorityModel> GetStatusChangeLeads(List<PriorityModel> lstPriorityModel)
        {
            short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
            List<PriorityModel> lstTicketLeads = new List<PriorityModel>();
            try
            {
                lstTicketLeads = lstPriorityModel.Where(x => (x.IsBooked == true)
                                            && (x.Call == null || (x.LeadStatus != null && x.LeadStatus.Statustime > x.Call.calltime.AddSeconds(TimeDiffDailer)))
                                            )
                    .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.StatusChange, counter))
                    .OrderByDescending(x => x.LeadStatus.Statustime).ToList();
            }

            catch (Exception)
            {

            }
            return lstTicketLeads;
        }

        public List<PriorityModel> GetBookedRest(List<PriorityModel> lstPriorityModel)
        {
            short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;

            List<PriorityModel> lstRestLeads = new List<PriorityModel>();
            try
            {
                lstRestLeads = lstPriorityModel.Where(x => (x.IsBooked == true)
                                            && (x.CallBack == null || DateTime.Now.Subtract(x.CallBack.CBtime).TotalSeconds >= 0) //no future callback
                                            && (x.Call == null || DateTime.Now.Subtract(x.Call.calltime).Days > 5)
                                            )
                    .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.RestLeads_1, counter))
                    .OrderByDescending(x => x.BookedTime).ToList();
            }

            catch (Exception)
            {

            }
            return lstRestLeads;
        }

        public List<PriorityModel> GetBookedUnanswered(List<PriorityModel> lstPriorityModel)
        {
            List<PriorityModel> lstUnansweredLeads = new List<PriorityModel>();
            short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_Rest;
            short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
            try
            {
                lstUnansweredLeads = lstPriorityModel.Where(x => (x.IsBooked == true)
                                            && (x.Call == null || (x.Call.TalkTime == 0 && DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC))// Not visible if NANC in last 30 mints
                                            && (x.CallBack == null || DateTime.Now.Subtract(x.CallBack.CBtime).TotalSeconds >= 0) //no future callback
                                            )
                    .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.UnansweredRecentLeads, counter))
                    .OrderByDescending(x => x.BookedTime).ToList();
            }
            catch (Exception)
            {

            }
            return lstUnansweredLeads;
        }


        public List<PriorityModel> GetUnansweredLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID = 0, Int64 UserID = 0)
        {
            List<PriorityModel> lstUnansweredLeads = new List<PriorityModel>();
            try
            {
                DateTime CurrentTime = DateTime.Now;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_Rest;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short Week1MaxAttempts = _LeadPriorityConstants.Week1MaxAttempts;
                List<Int16> ExcessiveProducts = new List<short>() { 2, 7, 115, 117 };
                List<Int16> InvestmentLeadrans = new List<short>() { 1, 2, 3, 4 };
                List<Int16> HealthLeadrans = new List<short>() { 231, 232, 233, 234 };
                //DateTime assignTime = Convert.ToDateTime("2019-05-04T18:30:19.349+0000");
                //DateTime lastCalll = Convert.ToDateTime("2019-05-07T13:06:01.113+0000");
                //Int32 TodaysAttempt = 1;
                //bool t = ((CurrentTime.Subtract(Convert.ToDateTime(assignTime).Date).TotalDays < 1 ? ((Convert.ToDateTime(lastCalll).Date == DateTime.Now.Date ? TodaysAttempt : 0) < 4) : ((Convert.ToInt32(CurrentTime.Subtract(Convert.ToDateTime(assignTime).Date).TotalDays) % 2) == 0 ? (Convert.ToDateTime(lastCalll).Date == DateTime.Now.Date ? TodaysAttempt : 0) < 2 : (Convert.ToDateTime(lastCalll).Date == DateTime.Now.Date ? TodaysAttempt : 0) < 1))); //only first week attempts
                //bool tt = (Convert.ToInt32(Convert.ToInt32(CurrentTime.Date.Subtract(Convert.ToDateTime(assignTime).Date).TotalDays) / 7 ) + 1 ) == 1;
                DateTime ct = DateTime.Now;

                bool IsWeekend = ct.DayOfWeek == DayOfWeek.Saturday || ct.DayOfWeek == DayOfWeek.Sunday;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstUnansweredLeads = lstPriorityModel.Where(x => (x.Call != null)
                                                          && (x.Call.TotalTT == 0)
                                                          && (x.Call.TalkTime == 0)
                                                          && (x.CallBack == null || CurrentTime.Subtract(x.CallBack.CBtime).TotalSeconds > 0)// no future CB ..To handle existing CB Lead with tt =0
                                                          && (CurrentTime.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC) // Not visible if NANC in last 150 mints
                                                          && ((Convert.ToInt32(Convert.ToInt32(CurrentTime.Date.Subtract(Convert.ToDateTime(x.User.FirstAssignedOn).Date).TotalDays) / 7) + 1) == 1) //only allowed in 1st week
                                                          && (!LeadPrioritizationDLL.IsTermHNIExp(x, _GroupID) || x.User.FirstAssignedOn.Date != DateTime.Now.Date || x.Call == null || x.Call.calltime.Date != DateTime.Now.Date || x.Call.TodaysAttempt < 2 || (x.Call.TodaysAttempt < 3 && DateTime.Now.Hour >= 18)) // Term HNI : valid unanswered attempts not to picked (max 3 and 3rd only after 6pm)

                                                          && (x.Call.NANC_Attempts < 4 || (CurrentTime.Subtract(Convert.ToDateTime(x.User.FirstAssignedOn).Date).TotalDays < 1 ? ((x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < 4) : ((Convert.ToInt32(CurrentTime.Subtract(Convert.ToDateTime(x.User.FirstAssignedOn).Date).TotalDays) % 2) == 0 ? (x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < 2 : (x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < 1))) //only first week attempts
                                                          && (!ExcessiveProducts.Contains(_productID) || x.Call.LastNminuteNANCeAttempts < 2 || (CurrentTime.Subtract(x.Call.calltime).TotalMinutes > 120 && x.Call.LastNminuteNANCeAttempts == 2)
                                                                                                  || (CurrentTime.Subtract(x.Call.calltime).TotalMinutes > 180 && x.Call.LastNminuteNANCeAttempts > 2)) //When consecutive 2 unanswered aqttempts                                                          
                                                          && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 1) // bucket utilization
                                                                                                                                                                                                                                                        //&& (!ExcessiveProducts.Contains(_productID) || (x.ProductID == 7 && x.Income < 1500000) || (x.ProductID == 115 && !InvestmentLeadrans.Contains(x.LeadRank)) || (x.ProductID == 2 && !HealthLeadrans.Contains(x.LeadRank)) || x.LeadID%2 != 0 || x.User.FirstAssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 1) //HNI Experiment max 2 calls allowed
                                                          && (!ExcessiveProducts.Contains(_productID) || x.User.FirstAssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 1 || (LeadPrioritizationDLL.IsTermHNIExp(x, _GroupID) && IsWeekend && x.Call.TodaysAttempt == 1 && x.Call.calltime.AddHours(4) < DateTime.Now)) //HNI Experiment max 2 calls allowed
                                                          && (x.CustomerCalltime == DateTime.MinValue || x.CustomerCalltime.Date < DateTime.Now.Date || DateTime.Now.Subtract(x.CustomerCalltime).TotalMinutes > 60) // if Call happens in other product
                                                          && (!LeadPrioritizationDLL.IsTermHNIExp(x, _GroupID) || !IsCallRestrictedToday_TermHNI(x))
                                                          //&& IsGoodTimeToCall(x)
                                                          )
                                                         .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.UnansweredLeads, counter))
                                                         .OrderByDescending(x => x.LeadCreatedOn).ToList();//neverAnsweredCall
                }
            }
            catch (Exception)
            {

            }
            return lstUnansweredLeads;
        }

        public List<PriorityModel>? GetRest_1Leads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID, Int64 UserID = 0)
        {
            List<PriorityModel> lstRestLeads = new List<PriorityModel>();
            DateTime ct = DateTime.Now;
            try
            {
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;
                short Week1MaxAttempts = _LeadPriorityConstants.Week1MaxAttempts;
                short WeekMaxAttempts = _LeadPriorityConstants.WeekMaxAttempts;

                bool IsWeekend = ct.DayOfWeek == DayOfWeek.Saturday || ct.DayOfWeek == DayOfWeek.Sunday;

                List<Int16> ExcessiveProducts = new List<short>() { 2, 7, 115, 117 };
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {

                    // if (_LeadPriorityConstants.restprioritylogicGroups.Contains(_GroupID))
                    // {
                    //     if (_OneLeadScoreMapping == null || _OneLeadScoreMapping.Count == 0)
                    //         _OneLeadScoreMapping = LeadPrioritizationDLL.GetLeadScoreMapping();

                    //     lstRestLeads = lstPriorityModel
                    //                   .Where(x => x.Call != null
                    //                         //&& (AIRestUsers == null || AIRestUsers.Count == 0 || (!AIRestUsers.Contains(UserID))) 
                    //                         && ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap // NOT CALLED IN lAST 12 HRS ( not called toady)
                    //                         && (x.Appointment == null || x.Appointment.AppointmentId == 0 || (AppointmentEnumStatus)x.Appointment.StatusID == AppointmentEnumStatus.Cancelled)
                    //                         && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback                                                                                       
                    //                         && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 1 || (LeadPrioritizationDLL.IsTermHNIExp(x, _GroupID) && IsWeekend && x.Call.TodaysAttempt == 1 && x.Call.calltime.AddHours(4) < DateTime.Now)) // bucket utilization
                    //                         && (!LeadPrioritizationDLL.IsTermHNIExp(x,_GroupID) || !IsCallRestrictedToday_TermHNI(x))
                    //                    ).Select(X => getSelectedDataWithScore(X))
                    //                   .OrderBy(x => x.Call.calltime).Take(5).ToList();

                    //     if (lstRestLeads != null)
                    //         lstRestLeads = lstRestLeads.Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.RestLeads_1, counter))
                    //                             .OrderBy(x => x.Call.TodaysAttempt)
                    //                             .OrderByDescending(x => x.LeadPriorityScore).Take(5).ToList();
                    // }
                    // else
                    // {
                    lstRestLeads = lstPriorityModel
                                   .Where(x => x.Call != null
                                        && ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap // NOT CALLED IN lAST 12 HRS ( not called toady)
                                                                                                            //&& (AIRestUsers == null || AIRestUsers.Count == 0 || (!AIRestUsers.Contains(UserID)))                                                                     //&& LeadPrioritizationDLL.CalculateCallShift(x.Call.calltime) != CurrentCallShift
                                        && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                        && (x.Appointment == null || x.Appointment.AppointmentId == 0 || (AppointmentEnumStatus)x.Appointment.StatusID == AppointmentEnumStatus.Cancelled)
                                        && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 1 || (LeadPrioritizationDLL.IsTermHNIExp(x, _GroupID) && IsWeekend && x.Call.TodaysAttempt == 1 && x.Call.calltime.AddHours(4) < DateTime.Now)) // bucket utilization
                                        && (!LeadPrioritizationDLL.IsTermHNIExp(x, _GroupID) || !IsCallRestrictedToday_TermHNI(x))
                                        && IsGoodTimeToCall(x) 
                                    ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, isCallRecommendedNow(X) ? LeadCategoryEnum.PreferredTime :LeadCategoryEnum.RestLeads_1, counter))
                                   .OrderBy(x => x.Call.calltime).Take(5).ToList();
                    // }
                }

            }
            catch (Exception)
            {

            }
            return lstRestLeads;
        }

        public List<PriorityModel> GetAnsweredLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID = 0)
        {
            List<PriorityModel> lstUnansweredLeads = new List<PriorityModel>();
            try
            {
                DateTime CurrentTime = DateTime.Now;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_Rest;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short Week1MaxAttempts = _LeadPriorityConstants.Week1MaxAttempts;
                List<Int16> extraAttempt = new List<short>() { 7, 115 };
                List<Int16> ExcessiveProducts = new List<short>() { 2, 7, 115, 117 };
                List<Int16> InvestmentLeadrans = new List<short>() { 1, 2, 3, 4 };
                List<Int16> HealthLeadrans = new List<short>() { 231, 232, 233, 234 };
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstUnansweredLeads = lstPriorityModel.Where(x => (x.Call != null)
                                                          && (x.Call.TotalTT > 0)
                                                          && (x.Appointment == null || x.Appointment.AppointmentId == 0 || (AppointmentEnumStatus)x.Appointment.StatusID == AppointmentEnumStatus.Cancelled)
                                                          && (x.Call.calltime.Date != CurrentTime.Date || x.Call.TodayAnswered < (extraAttempt.Contains(x.ProductID) ? 2 : 1))
                                                          && (x.CallBack == null || CurrentTime.Subtract(x.CallBack.CBtime).TotalSeconds > 0)// no future CB ..To handle existing CB Lead with tt =0
                                                          && (CurrentTime.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC) // Not visible if NANC in last 150 mints
                                                          && ((Convert.ToInt32(Convert.ToInt32(CurrentTime.Date.Subtract(Convert.ToDateTime(x.User.FirstAssignedOn).Date).TotalDays) / 7) + 1) == 1) //only allowed in 1st week
                                                          && (x.Call.NANC_Attempts < 4 || (CurrentTime.Subtract(Convert.ToDateTime(x.User.FirstAssignedOn).Date).TotalDays < 1 ? ((x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < 4) : ((Convert.ToInt32(CurrentTime.Subtract(Convert.ToDateTime(x.User.FirstAssignedOn).Date).TotalDays) % 2) == 0 ? (x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < 2 : (x.Call.calltime.Date == DateTime.Now.Date ? x.Call.TodaysAttempt : 0) < 1))) //only first week attempts
                                                          && (!ExcessiveProducts.Contains(_productID) || x.Call.LastNminuteNANCeAttempts < 2 || (CurrentTime.Subtract(x.Call.calltime).TotalMinutes > 120 && x.Call.LastNminuteNANCeAttempts == 2)
                                                                                                  || (CurrentTime.Subtract(x.Call.calltime).TotalMinutes > 180 && x.Call.LastNminuteNANCeAttempts > 2)) // burst calling restriction                                                          
                                                          && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                                                                                                                                                                                                                                                        //&& (!ExcessiveProducts.Contains(_productID) || (x.ProductID == 7 && x.Income < 1500000) || (x.ProductID == 115 && !InvestmentLeadrans.Contains(x.LeadRank)) || (x.ProductID == 2 && !HealthLeadrans.Contains(x.LeadRank)) || x.LeadID % 2 != 0 || x.User.FirstAssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 1) //HNI Experiment max 1 calls allowed
                                                          && (!ExcessiveProducts.Contains(_productID) || x.User.FirstAssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 1) //HNI Experiment max 1 calls allowed
                                                          && (x.CustomerCalltime == DateTime.MinValue || x.CustomerCalltime.Date < DateTime.Now.Date || DateTime.Now.Subtract(x.CustomerCalltime).TotalMinutes > 60)
                                                          && (!LeadPrioritizationDLL.IsTermHNIExp(x, _GroupID) || !IsCallRestrictedToday_TermHNI(x))
                                                          && IsGoodTimeToCall(x)
                                                          )
                                                         .Select(X => LeadPrioritizationDLL.GetSelectedData(X, isCallRecommendedNow(X) ? LeadCategoryEnum.PreferredTime : LeadCategoryEnum.RestLeads_2, counter))
                                                         .OrderByDescending(x => x.LeadCreatedOn).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstUnansweredLeads;
        }

        public List<PriorityModel> GetProposalErrorLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstProposalErrorLeads = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;

                List<PriorityModel> lstAgentLeads = new List<PriorityModel>();
                lstAgentLeads.AddRange(lstPriorityModel);
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstProposalErrorLeads = lstAgentLeads
                                       .Where(x => x.ProposalError > DateTime.MinValue
                                           && (x.Call == null || x.Call.calltime.AddSeconds(TimeDiffDailer) < x.ProposalError))
                                       .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.ProposalError, counter))
                                       .OrderByDescending(x => x.LeadCreatedOn).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstProposalErrorLeads;
        }

        public List<PriorityModel> GetQuoteRevisitLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstActiveRevisitLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstActiveRevisitLeads = lstPriorityModel
                                      .Where(x => (x.Revisit != null)
                                            && (x.Revisit.RevisitType == RevisitType.QuoteShared)
                                            && (x.Call == null || (x.Revisit.ts > x.Call.calltime.AddSeconds(TimeDiffDailer + x.Call.TalkTime))) // No call attempt after revisitTime
                                            && (x.Revisit.ts < DateTime.Now.AddMinutes(-10))
                                            && (x.SkippingTime < x.Revisit.ts))
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.QuoteShared, counter))
                  .OrderByDescending(x => x.Revisit.ts).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstActiveRevisitLeads;
        }

        public List<PriorityModel> GetrestPriorityLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID, Int16 _processId, Int64 UserId = 0)
        {
            List<PriorityModel> lstRestLeads = new List<PriorityModel>();
            DateTime ct = DateTime.Now;
            Int16[] seniorCitizenleads = { 2, 3, 6 };
            try
            {
                //List<Int64> AIRestUsers = _LeadPriorityConstants.AIRestUsers;

                //if (!_LeadPriorityConstants.restprioritylogicGroups.Contains(_GroupID))
                //    return lstRestLeads;
                if (seniorCitizenleads.Contains(_processId))
                    return lstRestLeads;
                if (_OneLeadScoreMapping == null || _OneLeadScoreMapping.Count == 0)
                    _OneLeadScoreMapping = LeadPrioritizationDLL.GetLeadScoreMapping();
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;
                short Week1MaxAttempts = _LeadPriorityConstants.Week1MaxAttempts;
                short WeekMaxAttempts = _LeadPriorityConstants.WeekMaxAttempts;
                List<Int16> ExcessiveProducts = new List<short>() { 2, 7, 115, 117 };

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    var RestPriorityLeads = lstPriorityModel
                                       .Where(x => x.Call != null
                                            && ct.Subtract(x.Call.calltime).TotalMinutes > 180 // NOT CALLED IN lAST 12 HRS ( not called toady)                                                                                                                
                                            && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                            && (x.Appointment == null || x.Appointment.AppointmentId == 0 || (AppointmentEnumStatus)x.Appointment.StatusID == AppointmentEnumStatus.Cancelled)
                                            && (x.Call.Week_Attempt < GetWeekMaxLimit(Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(ct.Date.Subtract(Convert.ToDateTime(x.User.FirstAssignedOn).Date).TotalDays) % 7) + 1), (x.Call.Current_Week == 1 ? Week1MaxAttempts : WeekMaxAttempts), x.LeadID))
                                            && (!ExcessiveProducts.Contains(_productID) || x.Call.TodaysAttempt < 5)
                                            && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 1) // bucket utilization
                                        ).Select(X => getSelectedDataWithScore(X))
                                       .ToList();

                    if (RestPriorityLeads != null)
                        lstRestLeads = RestPriorityLeads.Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.RestPriorityLeads, counter))
                                            .OrderBy(x => x.Call.TodaysAttempt)
                                            .OrderByDescending(x => x.LeadPriorityScore).Take(5).ToList();
                }

            }
            catch (Exception)
            {

            }
            return lstRestLeads;
        }
        private PriorityModel getSelectedDataWithScore(PriorityModel _PriorityModel)
        {
            decimal talktimeScore = 0;
            decimal NANCscore = 0;

            var LeadRankScore = _OneLeadScoreMapping.Where(X => X.Type == "LeadRank" && X.MinValue >= _PriorityModel.LeadRank && X.MaxValue <= _PriorityModel.LeadRank && X.ProductID == _productID)
                                                    .Select(X => X.Score).SingleOrDefault();
            var RevisitScore = _OneLeadScoreMapping.Where(X => X.Type == "Revisit" && X.MinValue >= _PriorityModel.RevisitCount && X.MaxValue <= _PriorityModel.RevisitCount && X.ProductID == _productID)
                                                    .Select(X => X.Score).SingleOrDefault();
            if (_PriorityModel.Call != null)
            {
                talktimeScore = _OneLeadScoreMapping.Where(X => X.Type == "Talktime" && X.MinValue >= _PriorityModel.Call.TalkTime && X.MaxValue <= _PriorityModel.Call.TalkTime && X.ProductID == _productID)
                                                    .Select(X => X.Score).SingleOrDefault();
                NANCscore = _OneLeadScoreMapping.Where(X => X.Type == "Talktime" && X.MinValue >= _PriorityModel.Call.NANC_Attempts && X.MaxValue <= _PriorityModel.Call.NANC_Attempts && X.ProductID == _productID)
                                                    .Select(X => X.Score).SingleOrDefault();
            }
            else
            {
                talktimeScore = _OneLeadScoreMapping.Where(X => X.Type == "Talktime" && X.MinValue >= 0 && X.MaxValue <= 0 && X.ProductID == _productID)
                                                    .Select(X => X.Score).SingleOrDefault();
                NANCscore = _OneLeadScoreMapping.Where(X => X.Type == "Talktime" && X.MinValue >= 0 && X.MaxValue <= 0 && X.ProductID == _productID)
                                                    .Select(X => X.Score).SingleOrDefault();
            }

            _PriorityModel.LeadPriorityScore = talktimeScore + NANCscore + LeadRankScore + RevisitScore;
            return _PriorityModel;
        }

        public List<PriorityModel> GetRevisionRevisitLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstActiveRevisitLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveRevisitShowTime = _LeadPriorityConstants.ActiveRevisitShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstActiveRevisitLeads = lstPriorityModel
                                      .Where(x => (x.Revisit != null)
                                            && (x.Revisit.RevisitType == RevisitType.RevisionShared)
                                            && (x.Call == null || (x.Revisit.ts > x.Call.calltime.AddSeconds(TimeDiffDailer + x.Call.TalkTime))) // No call attempt after revisitTime
                                            && (x.Revisit.ts < DateTime.Now.AddMinutes(-10))
                                            && (x.SkippingTime < x.Revisit.ts))
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.RevisionShared, counter))
                  .OrderByDescending(x => x.Revisit.ts).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstActiveRevisitLeads;
        }

        public static short GetWeekMaxLimit(int day, int WeekLimit, Int64 LeadID)
        {
            double MaxLimit = day * WeekLimit / 7;
            Int16 limit = Convert.ToInt16(Math.Round(MaxLimit) + 1);
            return limit;
        }

        public List<PriorityModel> GetPFFilledLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstPFLeads = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstPFLeads = lstPriorityModel
                                      .Where(x => (x.PFFilledTime.AddHours(1) > DateTime.Now)
                                            && (x.CallBack == null || DateTime.Now.Subtract(x.CallBack.CBtime).TotalSeconds > 0)// no future CB 
                                            && (x.Call == null || (x.PFFilledTime > x.Call.calltime.AddSeconds(TimeDiffDailer + x.Call.TalkTime))) // No call attempt after revisitTime
                                            )
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.PFFilled, counter))
                  .OrderBy(x => x.PFFilledTime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstPFLeads;
        }

        public List<PriorityModel> Get2ndAttemptPFFilledLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lst2ndAttemptActiveNew = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_New;
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptActiveNew = lstPriorityModel
                                                  .Where(x => (x.Call != null && x.Call.calltime.Date == DateTime.Now.Date)
                                                        && (x.PFFilledTime.AddHours(1) > DateTime.Now)
                                                        && (x.Call.ReasonID == (short)LeadCategoryEnum.PFFilled)
                                                        && (x.Call.TalkTime == 0)    // last Attempt NANC
                                                        && (x.Call.NANC_Attempts % 2 == 1)// odd attempts
                                                        && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC call was before 30 mints
                                                        && (x.CallBack == null || DateTime.Now.Subtract(x.CallBack.CBtime).TotalSeconds > 0)// no future CB 
                                                        )
                                                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptPFFilled, counter)).OrderBy(x => x.Call.calltime).ToList();
                }

            }
            catch (Exception)
            {

            }

            return lst2ndAttemptActiveNew;
        }

        public static List<PriorityModel> GetRMLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstRMLeads = new List<PriorityModel>();
            try
            {
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstRMLeads = lstPriorityModel
                        .Where(x => (x.RMCSTime.Date == DateTime.Now.Date)
                                    && (x.Call == null || x.Call.calltime < x.RMCSTime)
                                    && (x.CallBack == null || DateTime.Now.Subtract(x.CallBack.CBtime).TotalSeconds > 0)// no future CB 
                                    )
                                .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.RMLeads, counter)).OrderBy(x => x.RMCSTime).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lstRMLeads;
        }

        public List<PriorityModel> GetAIRestLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID, Int64 UserID)
        {
            List<PriorityModel> lstAILeads = new List<PriorityModel>();
            DateTime ct = DateTime.Now;
            try
            {
                short Week1MaxAttempts = _LeadPriorityConstants.Week1MaxAttempts;
                short WeekMaxAttempts = _LeadPriorityConstants.WeekMaxAttempts;
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstAILeads = lstPriorityModel
                                        .Where(x => x.Call != null
                                            //&& (AIRestUsers != null && AIRestUsers.Count > 0 && AIRestUsers.Contains(UserID))
                                            //&& ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap // NOT CALLED IN lAST 12 HRS ( not called toady)
                                            && (ct.Subtract(x.Call.calltime).TotalMinutes > 180 && (x.Call.calltime.Date != ct.Date || x.Call.TodaysAttempt < 2))
                                            && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                            && (x.Appointment == null || x.Appointment.AppointmentId == 0 || (AppointmentEnumStatus)x.Appointment.StatusID == AppointmentEnumStatus.Cancelled)
                                            && (x.Call.Week_Attempt < GetWeekMaxLimit(Convert.ToInt16(Convert.ToInt16(Convert.ToInt16(ct.Date.Subtract(Convert.ToDateTime(x.User.FirstAssignedOn).Date).TotalDays) % 7) + 1), (x.Call.Current_Week == 1 ? Week1MaxAttempts : WeekMaxAttempts), x.LeadID))
                                            && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 1) // bucket utilization
                                        ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.RestLeads_1, counter))
                                       .OrderByDescending(x => x.LeadPriorityScore)
                                       .OrderByDescending(x => x.ScoreModel)
                                       .Take(5).ToList();

                }

            }
            catch (Exception)
            {

            }
            return lstAILeads;
        }


        public List<PriorityModel> GetSOSLeads(List<PriorityModel>? lstPriorityModel, Int16 counter, Int16 ReasonID, Int16 GroupID)
        {
            List<PriorityModel> lstSOSLeads = new List<PriorityModel>();
            try
            {
                List<Int16> SOSGroups = _LeadPriorityConstants.SOSGroups;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0 && SOSGroups != null && SOSGroups.Contains(GroupID))
                {
                    LeadCategoryEnum type = 0;
                    switch (ReasonID)
                    {
                        case 42:
                            type = LeadCategoryEnum.BookedCB;
                            break;
                        case 54:
                            type = LeadCategoryEnum.SOSDocTickets;
                            break;
                        default:
                            type = LeadCategoryEnum.SOSRest;
                            break;
                    }
                    lstSOSLeads = lstPriorityModel
                                .Where(x => x.ScoreModel == ReasonID)
                                .Select(X => LeadPrioritizationDLL.GetSelectedData(X, type, counter))
                                .Take(5).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lstSOSLeads;
        }

        public List<PriorityModel> GetGraceLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 MinGraceDays, Int16 MaxGraceDays, Boolean IsSecondAttemptOn, Int16 CallingGap, Int16 GroupID = 0)
        {
            List<PriorityModel> lstGraceLeads = new List<PriorityModel>();
            DateTime ct = DateTime.Now;
            var pilotLeads = MasterData.getUnassistedPilotLeads();
            if (HealthrenewalexpGroups.Contains(GroupID))
                return lstGraceLeads;
            try
            {
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.InvisibleTimeNANC_Rest;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstGraceLeads = lstPriorityModel
                                        .Where(x =>
                                                    !IsUnassistedExpLead(pilotLeads, x.LeadID.ToString()) && (x.Country == 392 || x.Country == 0 || x.Country == 91)
                                                && (
                                                        x.Call == null
                                                    || (x.Call.calltime.Date != DateTime.Now.Date && ct.Date.Subtract(x.Call.calltime.Date).TotalDays > CallingGap)
                                                    || (IsSecondAttemptOn == true && x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodayAnswered == 0 && ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap && x.Call.TodaysNANCAttempt < 2)
                                                   )
                                                && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                                && (x.PrevPolicyExpDate > DateTime.MinValue)
                                                && (ct.Date.Subtract(x.PrevPolicyExpDate.Date).TotalDays > MinGraceDays && ct.Date.Subtract(x.PrevPolicyExpDate.Date).TotalDays <= MaxGraceDays)
                                        ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.ExpiryLogicold, counter))
                                        .OrderByDescending(x => x.PrevPolicyExpDate).ToList();
                }
            }


            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("0", GroupID, ex.ToString(), "onelead_GetExpiryLeads_error", "OneleadData", "Automation", "", string.Empty, DateTime.Now, DateTime.Now);
            }
            return lstGraceLeads;
        }

        public List<PriorityModel> GetExpiryLeadsV2(List<PriorityModel> lstPriorityModel, string Process, Int16 _GroupID)
        {
            List<PriorityModel> lstExpiryLeads = new List<PriorityModel>();
            DateTime ct = DateTime.Now;
            short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_Rest;
            Int16 RestLeadCallMintsGap = 180;

            var expiryLogicMasterlst = MasterData.getExpiryPriorityLogicMaster();
            try
            {
                Int16 CallingGap;
                Int32 SameDayAttempts = 0;
                Int32 Priority = 0;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstExpiryLeads = lstPriorityModel
                                        .Where(x =>
                                                     {
                                                         GetExpiryLeadsConfiguration(x, expiryLogicMasterlst, out CallingGap, out SameDayAttempts, Process, Priority, x.Country);
                                                         if (SameDayAttempts > 0)
                                                             return (
                                                                    (x.Country != 392 || Process == "MONTHLYMODE" || Process == "HOME")
                                                                    && (
                                                                               x.Call == null
                                                                           || (x.Call.calltime.Date != DateTime.Now.Date && ct.Date.Subtract(x.Call.calltime.Date).TotalDays >= CallingGap)
                                                                           || (x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysAttempt < SameDayAttempts && ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap && x.Call.TodayAnswered == 0)
                                                                       )
                                                                    && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)
                                                               );
                                                         else return false;
                                                     }

                                        ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.ExpiryLogic, counter))
                                        .OrderBy(x => x.LeadPriorityScore).Take(5).ToList();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("0", 0, ex.ToString(), "GetExpiryLeadsV2", "OneleadData", "Automation", "", string.Empty, DateTime.Now, DateTime.Now);
            }
            return lstExpiryLeads;
        }

        public List<PriorityModel> GetExpiryLeadsV3(List<PriorityModel> lstPriorityModel, string Process, Int16 _GroupID)
        {
            List<PriorityModel> lstExpiryLeads = new List<PriorityModel>();
            DateTime ct = DateTime.Now;
            short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_Rest;
            Int16 RestLeadCallMintsGap = 180;
            string originalProcess = Process;

            var expiryLogicMasterlst = MasterData.getExpiryPriorityLogicMaster();
            var pilotLeads = MasterData.getUnassistedPilotLeads();
            try
            {
                Int16 CallingGap;
                Int32 SameDayAttempts = 0;
                Int32 Priority = 0;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstExpiryLeads = lstPriorityModel
                                        .Where(x =>
                                        {
                                            Process = originalProcess;
                                            if (IsUnassistedExpLead(pilotLeads, x.LeadID.ToString()))
                                            {
                                                Process = "RENEWAL-UNASSISTED";
                                            }
                                            if (IsRenewalV2_AI(x, _GroupID))
                                            {
                                                Process = "RENEWAL-V2";
                                            }
                                            GetExpiryLeadsConfiguration(x, expiryLogicMasterlst, out CallingGap, out SameDayAttempts, Process, Priority, x.Country);
                                            if (SameDayAttempts > 0)
                                                return (
                                                       (HealthrenewalexpGroups.Contains(_GroupID) || Process == "RENEWAL-UNASSISTED" || Process == "RENEWAL-V2")
                                                       && (
                                                                  x.Call == null
                                                              || (x.Call.calltime.Date != DateTime.Now.Date && ct.Date.Subtract(x.Call.calltime.Date).TotalDays >= CallingGap)
                                                              || (x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysAttempt < SameDayAttempts && ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap && x.Call.TodayAnswered == 0)
                                                          )
                                                       && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)
                                                  );
                                            else return false;
                                        }

                                        ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.ExpiryLogic, counter))
                                        .OrderBy(x => x.PrevPolicyExpDate).Take(5).ToList();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("0", 0, ex.ToString(), "GetExpiryLeadsV3", "OneleadData", "Automation", "", string.Empty, DateTime.Now, DateTime.Now);
            }
            return lstExpiryLeads;
        }


        public List<PriorityModel> GetExpiryLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 MinExpiryDays, Int16 MaxExpiryDays, Boolean IsSecondAttemptOn, Int16 CallingGap, Int16 GroupID = 0)
        {
            List<PriorityModel> lstExpiryLeads = new List<PriorityModel>();
            DateTime ct = DateTime.Now;
            short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_Rest;
            Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;
            var pilotLeads = MasterData.getUnassistedPilotLeads();
            if (HealthrenewalexpGroups.Contains(GroupID))
                return lstExpiryLeads;
            try
            {
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstExpiryLeads = lstPriorityModel
                                        .Where(x =>
                                                    !IsUnassistedExpLead(pilotLeads, x.LeadID.ToString())
                                                    && !IsRenewalV2_AI(x, GroupID)
                                                    &&
                                                   (x.Country == 392 || x.Country == 0 || x.Country == 91)
                                                && (
                                                         x.Call == null
                                                     || (x.Call.calltime.Date != DateTime.Now.Date && ct.Date.Subtract(x.Call.calltime.Date).TotalDays > CallingGap)
                                                     || (IsSecondAttemptOn == true && x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodayAnswered == 0 && ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap && x.Call.TodaysNANCAttempt < 2)
                                                   ) // NOT CALLED IN lAST 12 HRS ( not called toady)
                                                && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                                && (x.PrevPolicyExpDate > DateTime.MinValue)
                                                && (x.PrevPolicyExpDate.Date.Subtract(ct.Date).TotalDays > MinExpiryDays && x.PrevPolicyExpDate.Date.Subtract(ct.Date).TotalDays <= MaxExpiryDays)
                                        ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.ExpiryLogicold, counter))
                                        .OrderBy(x => x.PrevPolicyExpDate).ToList();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("0", GroupID, ex.ToString(), "onelead_GetExpiryLeads_error", "OneleadData", "Automation", "", string.Empty, DateTime.Now, DateTime.Now);
            }
            return lstExpiryLeads;
        }

        public List<PriorityModel> GetTodayExpiryLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstTodayExpiry = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;
                Int16 TodyCreatdExprGap = _LeadPriorityConstants.TodayCreatedExprTimeGap;
                Int16 BefTodyCreatdExprGap = _LeadPriorityConstants.BeforeTodayCreatedExprTimeGap;
                byte ExpiryCount = _LeadPriorityConstants.ExpiryLeadsShowCount;
                DateTime ct = DateTime.Now;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_Rest;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstTodayExpiry = lstPriorityModel
                                       .Where(x => (x.Call == null || ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap)
                                            && (x.PrevPolicyExpDate > DateTime.MinValue)
                                            && (x.PrevPolicyExpDate.Date == ct.Date)// Expiring today
                                            && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                           )
                                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.TodayExpiry, counter))
                                        .OrderBy(x => x.Call == null ? x.LeadCreatedOn : x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstTodayExpiry;
        }

        public List<PriorityModel> Get2ndAttemptTodayExpiryLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstTodayExpiry = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                DateTime ct = DateTime.Now;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstTodayExpiry = lstPriorityModel
                                       .Where(x => (x.Call != null && x.Call.calltime.Date == ct.Date)
                                            && (x.Call.TalkTime == 0)    // last Attempt NANC
                                            && (x.PrevPolicyExpDate > DateTime.MinValue && x.PrevPolicyExpDate.Date == ct.Date) // Expiring today
                                            && (x.Call.NANC_Attempts % 2 == 1)// odd attempts 
                                            && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                            && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC call was before 15 mints
                                           )
                                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptTodayExpiry, counter))
                                        .OrderBy(x => x.Call == null ? x.LeadCreatedOn : x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstTodayExpiry;
        }

        public List<PriorityModel> GetServiceCBLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstCBLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveCBShowTime = _LeadPriorityConstants.BMSCBShowTime;
                Int16 ActiveCBEndMints = _LeadPriorityConstants.BMSCBEndTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstCBLeads = lstPriorityModel.Where(x => (x.CallBack != null && x.CallBack.CallBackType == CallBackTypeEnum.BMSBookedCB)
                                                             && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes >= ActiveCBShowTime * -1) //  show before 15 mints till after 15 mints 
                                                             && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes <= ActiveCBEndMints) //  show before 5 mints till after 15 mints 
                                                             && (x.Call == null || DateTime.Now.Subtract(x.Call.calltime.AddMinutes(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// Not visible if NANC in last 30 mints
                                                             && (x.Call == null ||
                                                                    (x.Call.calltime.AddSeconds(TimeDiffDailer) < (x.CallBack.ts > x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1) ?
                                                                    x.CallBack.ts : x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1)))))// no  call after Max(CBSetTime,CBTime-15) 
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.ServiceCB, counter))
                  .OrderByDescending(x => Math.Abs(x.CallBack.CBtime.Subtract(DateTime.Now).Seconds)).ToList();// sorting by nearest callback
                }
            }
            catch (Exception)
            {

            }

            return lstCBLeads;
        }

        public List<PriorityModel> GetTodayAppointmentLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID)
        {
            List<PriorityModel> lstLeads = new List<PriorityModel>();
            DateTime ct = DateTime.Now;
            try
            {
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;
                List<AppointmentEnumStatus> validStatus = new List<AppointmentEnumStatus>() { AppointmentEnumStatus.Booked, AppointmentEnumStatus.Confirmed, AppointmentEnumStatus.ReScheduled };

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstLeads = lstPriorityModel
                        .Where(x => x.Appointment != null
                            && x.Appointment.ScheduledOn.Date == ct.Date
                            && validStatus.Contains((AppointmentEnumStatus)x.Appointment.StatusID)
                            && ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap // NOT CALLED IN lAST 12 HRS ( not called toady)
                            && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                            && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                    ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.VisitToday, counter))
                    .OrderBy(x => x.Appointment != null ? x.Appointment.ScheduledOn : x.LeadCreatedOn).Take(5).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lstLeads;
        }

        public List<PriorityModel> Get2ndAttemptTodayAppointmentLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID)
        {
            List<PriorityModel> lstLeads = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                DateTime ct = DateTime.Now;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                List<AppointmentEnumStatus> validStatus = new List<AppointmentEnumStatus>() { AppointmentEnumStatus.Booked, AppointmentEnumStatus.Confirmed, AppointmentEnumStatus.ReScheduled };

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstLeads = lstPriorityModel
                                       .Where(x =>
                                               (x.Call != null && x.Appointment != null)
                                            && (x.Call.calltime.Date == ct.Date && x.Call.TodaysAttempt == 1)
                                            && (x.Call.ReasonID == (short)LeadCategoryEnum.VisitToday)
                                            && x.Appointment.ScheduledOn.Date == ct.Date
                                            && validStatus.Contains((AppointmentEnumStatus)x.Appointment.StatusID)
                                            && (x.Call.TalkTime == 0)    // last Attempt NANC
                                            && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                            && (x.Call.TodaysNANCAttempt < 2) // Today not answered attempts 
                                            && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC call was before 15 mints
                                            && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                                            )
                                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptVisitToday, counter))
                                        .OrderBy(x => x.Call != null ? x.Call.calltime : x.LeadCreatedOn).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstLeads;
        }

        public List<PriorityModel> GetUnConfirmedLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID)
        {
            List<PriorityModel> lstLeads = new List<PriorityModel>();
            try
            {
                DateTime ct = DateTime.Now;
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;
                List<AppointmentEnumStatus> validStatus = new List<AppointmentEnumStatus>() { AppointmentEnumStatus.Booked, AppointmentEnumStatus.ReScheduled };

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstLeads = lstPriorityModel
                        .Where(x => x.Appointment != null && x.Appointment.ScheduledOn.Date == ct.AddDays(1).Date
                            && validStatus.Contains((AppointmentEnumStatus)x.Appointment.StatusID)
                            && ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap // NOT CALLED IN lAST 12 HRS ( not called toady)
                            && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                            && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                        )
                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.ConfirmVisit, counter))
                        .OrderBy(x => x.Appointment != null ? x.Appointment.ScheduledOn : x.LeadCreatedOn).Take(5).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lstLeads;
        }

        public List<PriorityModel> Get2ndAttemptUnConfirmedLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID)
        {
            List<PriorityModel> lstLeads = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                DateTime ct = DateTime.Now;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                List<AppointmentEnumStatus> validStatus = new List<AppointmentEnumStatus>() { AppointmentEnumStatus.Booked, AppointmentEnumStatus.ReScheduled };

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstLeads = lstPriorityModel
                                       .Where(x => (x.Call != null && x.Appointment != null
                                            && x.Call.calltime.Date == ct.Date && x.Call.TodaysAttempt == 1)
                                            && x.Appointment.ScheduledOn.Date == ct.AddDays(1).Date
                                            && validStatus.Contains((AppointmentEnumStatus)x.Appointment.StatusID)
                                            && (x.Call.TalkTime == 0)    // last Attempt NANC
                                            && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                            && (x.Call.TodaysNANCAttempt < 2) // Today not answered attempts 
                                            && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC call was before 15 mints
                                            && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                                            )
                                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptConfirmVisit, counter))
                                        .OrderBy(x => x.Call != null ? x.Call.calltime : x.LeadCreatedOn).Take(5).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstLeads;
        }

        public List<PriorityModel> GetExpiredAppointmentLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID)
        {
            List<PriorityModel> lstLeads = new List<PriorityModel>();
            List<AppointmentEnumStatus> validStatus = new List<AppointmentEnumStatus>() { AppointmentEnumStatus.Booked, AppointmentEnumStatus.Confirmed, AppointmentEnumStatus.ReScheduled };

            try
            {
                DateTime ct = DateTime.Now;
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstLeads = lstPriorityModel
                        .Where(x => x.Appointment != null
                            && x.Appointment.ScheduledOn < ct
                            && validStatus.Contains((AppointmentEnumStatus)x.Appointment.StatusID)
                            && (x.Call == null || ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap) // NOT CALLED IN lAST 12 HRS ( not called toady)
                            && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                            && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                        )
                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.MissedVisit, counter))
                        .OrderByDescending(x => x.Appointment != null ? x.Appointment.ScheduledOn : x.LeadCreatedOn).Take(5).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lstLeads;
        }

        public List<PriorityModel> Get2ndAttemptExpiredAppointmentLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID)
        {
            List<PriorityModel> lstLeads = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                DateTime ct = DateTime.Now;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                List<AppointmentEnumStatus> validStatus = new List<AppointmentEnumStatus>() { AppointmentEnumStatus.Booked, AppointmentEnumStatus.Confirmed, AppointmentEnumStatus.ReScheduled };

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstLeads = lstPriorityModel
                                       .Where(x => (x.Call != null && x.Appointment != null
                                            && x.Call.calltime.Date == ct.Date && x.Call.TodaysAttempt == 1)
                                            && x.Appointment.ScheduledOn < ct
                                            && validStatus.Contains((AppointmentEnumStatus)x.Appointment.StatusID)
                                            && (x.Call.TalkTime == 0)    // last Attempt NANC
                                            && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                            && (x.Call.TodaysNANCAttempt < 2) // Today not answered attempts 
                                            && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC call was before 15 mints
                                            && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                                            )
                                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptMissedVisit, counter))
                                        .OrderBy(x => x.Call != null ? x.Call.calltime : x.LeadCreatedOn).Take(5).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstLeads;
        }

        public List<PriorityModel> GetProspectAppointmentLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID)
        {
            List<PriorityModel> lstLeads = new List<PriorityModel>();
            List<AppointmentEnumStatus> validStatus = new List<AppointmentEnumStatus>() { AppointmentEnumStatus.Completed, AppointmentEnumStatus.Started };
            try
            {
                DateTime ct = DateTime.Now;
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstLeads = lstPriorityModel
                        .Where(x => x.Appointment != null
                            //&& x.Appointment.ScheduledOn.Date == ct.AddDays(-1).Date
                            && validStatus.Contains((AppointmentEnumStatus)x.Appointment.StatusID)
                            && (x.Call == null || ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap) // NOT CALLED IN lAST 12 HRS ( not called toady)
                            && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                            && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                        )
                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.VisitUpdate, counter))
                        .OrderByDescending(x => x.Appointment != null ? x.Appointment.ScheduledOn : x.LeadCreatedOn).Take(5).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lstLeads;
        }

        public List<PriorityModel> Get2ndAttemptProspectdAppointmentLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID)
        {
            List<PriorityModel> lstLeads = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                DateTime ct = DateTime.Now;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                List<AppointmentEnumStatus> validStatus = new List<AppointmentEnumStatus>() { AppointmentEnumStatus.Completed, AppointmentEnumStatus.Started };

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstLeads = lstPriorityModel
                                       .Where(x => (x.Call != null && x.Appointment != null
                                            && x.Call.calltime.Date == ct.Date && x.Call.TodaysAttempt == 1)
                                            && validStatus.Contains((AppointmentEnumStatus)x.Appointment.StatusID)
                                            && (x.Call.TalkTime == 0)    // last Attempt NANC
                                            && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                            && (x.Call.TodaysNANCAttempt < 2) // Today not answered attempts 
                                            && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC call was before 15 mints
                                            && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                                            )
                                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptVisitUpdate, counter))
                                        .OrderBy(x => x.Call != null ? x.Call.calltime : x.LeadCreatedOn).Take(5).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstLeads;
        }

        public List<PriorityModel> GetFosChurnLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID = 0)
        {
            List<PriorityModel> lstLeads = new List<PriorityModel>();
            try
            {
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;
                List<short> FosChurnIds = "FosChurnIds".AppSettings().Split(',').Select(short.Parse).ToList();
                DateTime ct = DateTime.Now;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstLeads = lstPriorityModel.Where(x => x.User != null && FosChurnIds.Contains(x.User.JobID)
                                                    && ct.Date.Subtract(x.User.AssignedOn.Date).TotalMinutes < RestLeadCallMintsGap //Assigned in last 12 hours
                                                    && x.Call != null
                                                    && ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap // NOT CALLED IN lAST 12 HRS ( not called toady)
                                                    && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                                                    )
                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.FOSChurn, counter))
                        .OrderBy(x => x.Call != null ? x.Call.calltime : x.LeadCreatedOn).Take(5).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lstLeads;
        }

        public List<PriorityModel> Get2ndAttemptFosChurnLeads(List<PriorityModel> lstPriorityModel, Int16 counter, Int16 _GroupID = 0)
        {
            List<PriorityModel> lst2ndAttemptFosChurnLeads = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC_New;
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                List<short> FosChurnIds = "FosChurnIds".AppSettings().Split(',').Select(short.Parse).ToList();
                DateTime ct = DateTime.Now;

                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptFosChurnLeads = lstPriorityModel.Where(x => (x.Call != null && x.Call.TodaysAttempt == 1)
                                                                    && x.User != null && FosChurnIds.Contains(x.User.JobID)
                                                                    && ct.Date.Subtract(x.User.AssignedOn.Date).TotalMinutes < RestLeadCallMintsGap //Assigned in last 12 hours
                                                                    && (x.Call.TalkTime == 0)    // last Attempt NANC
                                                                    && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                                                    && (x.Call.TodaysNANCAttempt < 2) // Today not answered attempts 
                                                                    && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC call was before 15 mints
                                                                    && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                                                                    )

                                                            .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptFOSChurn, counter)).OrderBy(x => x.Call.calltime).Take(5).ToList();
                }

            }
            catch (Exception)
            {

            }

            return lst2ndAttemptFosChurnLeads;
        }
        //Get ALL Customer Scheduled Callback Lead in next 15mints
        public List<PriorityModel> GetCustomerScheduledCallbacks(List<PriorityModel> lstAgentActiveLeads, Int16 counter, Int16 ProductID, Int64 UserID)
        {
            List<PriorityModel> lstScheduledCBLead = new List<PriorityModel>();
            try
            {
                List<PriorityModel> lstAgentLeads = new List<PriorityModel>(lstAgentActiveLeads);
                Int16 PaymentCBShowTime = _LeadPriorityConstants.PaymentCBShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                if (lstAgentActiveLeads != null && lstAgentActiveLeads.Count > 0)
                {
                    lstScheduledCBLead = lstAgentLeads
                                        .Where(x =>
                                                (x.CallBack != null) && (x.CallBack.CallBackType == CallBackTypeEnum.CustRequested)// CustRequested
                                             && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes > PaymentCBShowTime)// -5 mints to Till Time
                                             && (x.Call == null || x.Call.calltime < x.CallBack.CBtime.AddMinutes(-10))
                                             && (x.Call == null || DateTime.Now.Subtract(x.Call.calltime).TotalMinutes > 30)// Not visible if NANC in last 30 mints
                                               /*&& (x.Call == null || (
                                                                          x.Call.calltime -
                                                                                   (
                                                                                      x.CallBack.ts > x.CallBack.CBtime.AddMinutes(10) ? x.CallBack.ts : x.CallBack.CBtime.AddMinutes(10)
                                                                                   )
                                                                      ).TotalMinutes < 0
                                                  )*/
                                               )                                                                                            // 
                                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.CusatomerCB, counter)).OrderByDescending(x => x.CallBack.CBtime)
                                        .ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstScheduledCBLead;
        }

        public static void GetExpiryLeadsConfiguration(PriorityModel x, List<ExpiryPriorityLogic> expiryLogicMasterlst, out Int16 CallingGap, out int SameDayAttempts, string Process, Int32 Priority, Int16 Country)
        {
            try
            {
                DateTime ct = DateTime.Now;
                CallingGap = Int16.MaxValue;
                SameDayAttempts = 0;
                int Taltime = x.Call != null && x.Call.TotalTT > 0 ? x.Call.TotalTT : 0;
                List<ExpiryPriorityLogic> item;
                bool IsNRI = true;

                if (Country == 392 || Country == 91 || x.Country == 0)
                    IsNRI = false;

                if (Process == "MONTHLYMODE")
                {
                    item = expiryLogicMasterlst.Where(expiryLogicMasterlstLogic =>
                                                 expiryLogicMasterlstLogic.Process == Process
                                                 && (
                                                        (expiryLogicMasterlstLogic.FilterDate == "GRACE" && x.GraceEndDate > DateTime.MinValue && x.GraceEndDate?.Date.Subtract(ct.Date).TotalDays >= expiryLogicMasterlstLogic.MinExpDays && x.GraceEndDate?.Date.Subtract(ct.Date).TotalDays <= expiryLogicMasterlstLogic.MaxExpDays)
                                                        ||
                                                        (expiryLogicMasterlstLogic.FilterDate == "ASSIGN" && x.User != null && x.User.AssignedOn > DateTime.MinValue && x.User.AssignedOn.Date.Subtract(ct.Date).TotalDays >= expiryLogicMasterlstLogic.MinExpDays && x.User.AssignedOn.Date.Subtract(ct.Date).TotalDays <= expiryLogicMasterlstLogic.MaxExpDays)
                                                    )

                                            ).OrderBy(x => x.Priority).Take(1).ToList();
                }
                else
                {
                    item = expiryLogicMasterlst.Where(expiryLogicMasterlstLogic =>
                                                   x.PrevPolicyExpDate > DateTime.MinValue
                                                && expiryLogicMasterlstLogic.Process == Process
                                                && x.PrevPolicyExpDate.Date.Subtract(ct.Date).TotalDays >= expiryLogicMasterlstLogic.MinExpDays && x.PrevPolicyExpDate.Date.Subtract(ct.Date).TotalDays <= expiryLogicMasterlstLogic.MaxExpDays
                                                && Taltime >= expiryLogicMasterlstLogic.MinTalkTime && (expiryLogicMasterlstLogic.MinTalkTime == 181 || Taltime <= expiryLogicMasterlstLogic.MaxTalkTime)
                                                && x.RenewalYear >= expiryLogicMasterlstLogic.RYStart && x.RenewalYear <= expiryLogicMasterlstLogic.RYEnd
                                                && expiryLogicMasterlstLogic.IsNRI == IsNRI
                                            ).OrderBy(x => x.Priority).Take(1).ToList();
                }


                if (item != null && item.Count > 0)
                {
                    CallingGap = item[0].CallingGapDays;
                    SameDayAttempts = item[0].Attempts;
                    x.LeadPriorityScore = item[0].Priority;
                }
                else
                {
                    CallingGap = 0;
                    SameDayAttempts = 0;
                    Priority = 0;
                }

            }
            catch (Exception ex)
            {
                CallingGap = 0;
                SameDayAttempts = 0;
            }
        }

        public List<PriorityModel> GetPaymentCBLead(List<PriorityModel> lstAgentActiveLeads, Int16 counter)
        {
            List<PriorityModel> lstPaymentCBLead = new List<PriorityModel>();
            try
            {
                List<PriorityModel> lstAgentLeads = new List<PriorityModel>(lstAgentActiveLeads);
                Int16 PaymentCBShowTime = _LeadPriorityConstants.PaymentCBShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstAgentActiveLeads != null && lstAgentActiveLeads.Count > 0)
                {
                    lstPaymentCBLead = lstAgentLeads
                                       .Where(x => (x.CallBack != null) && (x.CallBack.IsPaymentCB == true)// PaymentCallBack
                                             && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes > PaymentCBShowTime)// -15 mints to Till Time
                                           && (x.Call == null || x.Call.TalkTime > 0 || DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// Not visible if NANC in last 30 mints
                                             && (x.Call == null || (
                                                                         (x.Call.calltime.AddSeconds(TimeDiffDailer) - (x.CallBack.ts > x.CallBack.CBtime.AddMinutes(PaymentCBShowTime) ?
                                                                                x.CallBack.ts : x.CallBack.CBtime.AddMinutes(PaymentCBShowTime))).TotalMinutes < 0)))// no  call after Max(CBSetTime,CBTime-15)
                                    .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.PaymentCB, counter)).OrderByDescending(x => x.CallBack.CBtime)
                                       .ToList();

                }
            }
            catch (Exception)
            {

            }
            return lstPaymentCBLead;
        }
        public List<PriorityModel> Get2ndAttemptPCBLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lst2ndAttemptPCBLeads = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short PCBTimeRange = _LeadPriorityConstants.PaymentCBShowTime;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptPCBLeads = lstPriorityModel.Where(x =>
                                                                      (x.CallBack != null)
                                                                   && (x.CallBack.IsPaymentCB == true)
                                                                   && (x.Call != null && x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysAttempt == 1)
                                                                   && (x.Call.TalkTime == 0 && x.Call.Duration > 0)
                                                                   && (x.Call.ReasonID == (short)LeadCategoryEnum.PaymentCB)
                                                                   && (x.Call.NANC_Attempts % 2 == 1)// odd attempts
                                                                   && (x.IsLastBucketRest == false) // lastly not in RestBucket
                                                                   && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// lst NANC call was before 30 mints
                                                                   && (x.CallBack.ts < x.Call.calltime.AddSeconds(TimeDiffDailer))// NANC Attempt done after the the setting time of PCB
                                                                   && x.Call.calltime > x.CallBack.CBtime.AddMinutes(PCBTimeRange))// last attempt was done after PCB time -15 mints time
                                                                                                                                   // .Select(X => LeadPrioritizationDLL.getSelectedData(X, LeadCategoryEnum.SecondAttemptPCB)).OrderByDescending(x => x.CallBack.ts).ToList();
                                                                   .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptPCB, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lst2ndAttemptPCBLeads;
        }
        public List<PriorityModel> GetActiveCBLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstActiveCBLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                Int16 ActiveCBEndMints = _LeadPriorityConstants.ActiveCBEndTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstActiveCBLeads = lstPriorityModel.Where(x => (x.CallBack != null)
                                                             && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes >= ActiveCBShowTime * -1) //  show before 5 mints till after 15 mints 
                                                             && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes <= ActiveCBEndMints) //  show before 5 mints till after 15 mints 
                                                             && (x.Call == null || x.Call.TalkTime != 0 || DateTime.Now.Subtract(x.Call.calltime.AddMinutes(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// Not visible if NANC in last 30 mints
                                                             && (x.Call == null ||
                                                                    (x.Call.calltime.AddSeconds(TimeDiffDailer) < (x.CallBack.ts > x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1) ?
                                                                    x.CallBack.ts : x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1)))))// no  call after Max(CBSetTime,CBTime-15) 
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.ActiveCB, counter))
                  .OrderByDescending(x => Math.Abs(x.CallBack.CBtime.Subtract(DateTime.Now).Seconds)).ToList();// sorting by nearest callback
                }
            }
            catch (Exception)
            {

            }

            return lstActiveCBLeads;
        }

        // GetPassiveCB
        public List<PriorityModel> GetPassiveCBLead(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstPassiveCBLead = new List<PriorityModel>();
            try
            {
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                Int16 ActiveCBEndTime = _LeadPriorityConstants.ActiveCBEndTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short PassivEnd = _LeadPriorityConstants.PassiveCBEndMints;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstPassiveCBLead = lstPriorityModel.Where(x => (x.CallBack != null)
                                                              && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes > ActiveCBEndTime) //  will be visible after 15mints of CB
                                                              && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes < PassivEnd) //  will be visible between 15mints  to 24hrs of CB
                                                              && (x.Call == null || (x.Call.calltime.AddSeconds(TimeDiffDailer) < (x.CallBack.ts > x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1) ?
                                                                                   x.CallBack.ts : x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1))))// no call Attempt after Max(CBSetTime,CBTime-15) 
                                                              && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                                                                             )
                                                    .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.PassiveCB, counter))
                                                    .OrderByDescending(x => x.CallBack.CBtime).ToList();// sorting by nearest callback
                }
            }
            catch (Exception)
            {

            }
            return lstPassiveCBLead;
        }

        // Get ActiveCB for 2nd Attempt
        public List<PriorityModel> Get2ndAttemptActiveCB(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lst2ndAttemptActiveCB = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                Int16 ActiveCBEndTime = _LeadPriorityConstants.ActiveCBEndTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptActiveCB = lstPriorityModel.Where(x =>
                                                                       (x.Call != null && x.CallBack != null && x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysAttempt == 1)
                                                                    && (x.Call.TalkTime == 0)
                                                                    && (x.Call.ReasonID == (short)LeadCategoryEnum.ActiveCB)
                                                                    && (x.Call.NANC_Attempts % 2 == 1)// odd attempts
                                                                    && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC call was before 30 mints  
                                                                    && (x.Call.calltime.AddSeconds(TimeDiffDailer).Subtract(x.CallBack.CBtime).TotalMinutes <= ActiveCBEndTime)
                                                                    && (x.Call.calltime.AddSeconds(TimeDiffDailer).Subtract(x.CallBack.CBtime).TotalMinutes >= ActiveCBShowTime * -1)
                                                                    && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                                                                    )//last NANC Attempt was before or after 15 mints from CB time
                                                           .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptActvCB, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }

            return lst2ndAttemptActiveCB;
        }


        //Get Passive CB for 2nd Attempt
        public List<PriorityModel> Get2ndAttemptPassiveCB(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lst2ndAttemptPassiveCB = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                Int16 ActvCBEndTime = _LeadPriorityConstants.ActiveCBEndTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short PassivEnd = _LeadPriorityConstants.PassiveCBEndMints;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptPassiveCB = lstPriorityModel.Where(x =>
                                                                        (x.Call != null && x.CallBack != null && x.Call.calltime.Date == DateTime.Now.Date && x.Call.TodaysAttempt == 1)
                                                                     && (x.Call.TalkTime == 0)
                                                                     && (x.Call.ReasonID == (short)LeadCategoryEnum.PassiveCB)
                                                                     && (x.IsLastBucketRest == false) // lastly not in RestBucket
                                                                     && (x.Call.NANC_Attempts % 2 == 1)// so that lead do not keep on coming in the same bucket for ever
                                                                     && (x.CallBack.CBtime.AddMinutes(ActvCBEndTime) <= x.Call.calltime.AddSeconds(TimeDiffDailer))  // last Attempt was done after 15 MIN of CB TIME
                                                                     && (x.CallBack.CBtime.AddMinutes(PassivEnd) >= x.Call.calltime.AddSeconds(TimeDiffDailer))  // last Attempt was done till  24 hrs MIN of CB TIME
                                                                     && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC Was before 30 mints (means moving out of bucket)
                                                                     && (!bucketutilizationProducts.Contains(_productID) || x.User.AssignedOn.Date == DateTime.Now.Date || x.Call == null || x.Call.calltime.Date < DateTime.Now.Date || x.Call.TodaysAttempt < 2) // bucket utilization
                                                                     )
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptPasvCB, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lst2ndAttemptPassiveCB;
        }

        private static bool IsUnassistedExpLead(List<UnassistedPilotLead> pilotLeads, string leadId)
        {
            return pilotLeads.Any(p => p.LeadId == leadId);
        }

        private static bool IsRenewalV2_AI(PriorityModel lead, short _GroupID)
        {
            List<string> NewFreqProcesses = new() { "ISSUE", "NEAR_EXPIRY", "CALLBACK", "WHATSAPP_EMAIL" };
            if (NewFreqProcesses.Contains(lead.CallFreqProcess))
            {
                return true;
            }
            return false;
        }

        public static bool IsCallRestrictedToday_TermHNI(PriorityModel lead)
        {
            if (lead == null || lead.LeadCreatedOn == DateTime.MinValue)
                return false; // If lead data is invalid, don't restrict calling


            DayOfWeek creationDay = lead.LeadCreatedOn.DayOfWeek;
            DayOfWeek today = DateTime.Now.DayOfWeek;

            // Check if today is a restricted day based on lead creation day
            bool restricted = creationDay switch
            {
                DayOfWeek.Monday => today == DayOfWeek.Thursday,
                DayOfWeek.Tuesday => today == DayOfWeek.Friday,
                DayOfWeek.Wednesday => today == DayOfWeek.Monday,
                DayOfWeek.Thursday => today == DayOfWeek.Tuesday,
                DayOfWeek.Friday => today == DayOfWeek.Wednesday,
                DayOfWeek.Saturday => today == DayOfWeek.Thursday,
                DayOfWeek.Sunday => today == DayOfWeek.Friday,
                _ => false,
            };

            if (restricted) {
                // if call was not made yesterday, allow on restricted day
                if(lead.Call==null || lead.Call.calltime < DateTime.Now.Date.AddDays(-1)) {
                    restricted = false;
                }

            }
            return restricted;
        }

        private static bool IsGoodTimeToCall(PriorityModel lead)
        {
            try
            {
                if (lead.ProductID == 131) return true;

                if (lead == null || lead.NBT == null || lead.NBT.Count == 0)
                    return true; // If no NBT data, assume it's okay to call

                // Get current day of week and hour
                string currentDay = DateTime.Now.DayOfWeek.ToString();
                int currentHour = DateTime.Now.Hour;

                // Check if we have recommendations for today
                
                if (!lead.NBT.ContainsKey(currentDay))
                    return true; // No recommendations for today, assume it's okay to call

                var todayRecommendations = lead.NBT.GetValueOrDefault(currentDay);

                if (todayRecommendations == null || todayRecommendations.Count == 0) return true;

                if(isCallRecommendedNow(lead)) {
                    return true;
                }

                //// Check if there are any "Highly Recommended" or "Recommended" hours for today
                //bool hasPreferredHours = todayRecommendations.Any(r =>
                //    (r.HourOfDay > currentHour)
                //    && (r.Recommendation == "Highly Recommended" || r.Recommendation == "Recommended"));

                //// If we have preferred hours, check if current hour is one of them
                //if (hasPreferredHours)
                //{
                //    // Only call during "Highly Recommended" or "Recommended" hours
                //    return todayRecommendations.Any(r =>
                //        r.HourOfDay == currentHour &&
                //        (r.Recommendation == "Highly Recommended" || r.Recommendation == "Recommended"));
                //}

                // If no preferred hours, check if current hour is "Not Recommended"
                var currentHourRec = todayRecommendations.FirstOrDefault(r => r.HourOfDay == currentHour);
                if (currentHourRec != null && currentHourRec.Recommendation == "Not Recommended")
                    return false; // Don't call during "Not Recommended" hours

                // If current hour is "Consider" or not specified, it's okay to call
                return true;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("0", lead.LeadID, ex.ToString(), "IsGoodTimeToCall", "OneleadData", "OneleadPriority", "", string.Empty, DateTime.Now, DateTime.Now);
                return true;
            }
        }

        private static bool isCallRecommendedNow(PriorityModel lead)
        {
            try
            {
                if (lead == null || lead.NBT == null || lead.NBT.Count == 0)
                    return false; // If no NBT data


                string currentDay = DateTime.Now.DayOfWeek.ToString();
                int currentHour = DateTime.Now.Hour;

                // Check if we have recommendations for today
                if (!lead.NBT.ContainsKey(currentDay))
                    return false;

                var todayRecommendations = lead.NBT.GetValueOrDefault(currentDay);

                if (todayRecommendations == null) return false;

                // Check if there are any "Highly Recommended" or "Recommended" hours for today
                bool hasPreferredHours = todayRecommendations.Any(r =>
                    (r.HourOfDay >= currentHour)
                    && (r.Recommendation == "Highly Recommended" || r.Recommendation == "Recommended"));

                // If we have preferred hours, check if current hour is one of them
                if (hasPreferredHours)
                {
                    // Only call during "Highly Recommended" or "Recommended" hours
                    return todayRecommendations.Any(r =>
                        r.HourOfDay == currentHour &&
                        (r.Recommendation == "Highly Recommended" || r.Recommendation == "Recommended"));
                }
                return false;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("0", lead.LeadID, ex.ToString(), "isCallRecommendedNow", "OneleadData", "OneleadPriority", "", string.Empty, DateTime.Now, DateTime.Now);
                return false;
            }
        }
    }
}
