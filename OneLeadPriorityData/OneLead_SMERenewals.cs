﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PropertyLayers;
using Helper;
using DataAccessLibrary;
using DataAccessLayer;

namespace OneLeadPriorityData
{
    public class OneLead_SMERenewals : IOneLead
    {
        readonly productPriorityconstant _LeadPriorityConstants;
        readonly Int16 _productID;
        readonly OneLeadData _OneLeadData;
        Int16 counter = 0;
        readonly Int16 _GroupID = 0;
        readonly OneLeadParams _oneLeadParams;
        public OneLead_SMERenewals(short productID, Int16 GroupID, OneLeadParams oneLeadParams)
        {
            this._productID = productID;
            this._GroupID = GroupID;
            _LeadPriorityConstants = getPriorityConfigByProduct(productID);
            _OneLeadData = new OneLeadData(productID, _LeadPriorityConstants, counter);
            _oneLeadParams = oneLeadParams;
        }

        public List<PriorityModel> getOneLead(Int16 ProductId, Int64 UserId, Int16 priorityleadCount, bool productCheck = false)
        {
            List<PriorityModel> lstPriorityModel = new List<PriorityModel>();
            List<PriorityModel> lstAgentAssignedLeads = LeadPrioritizationDLL.GetAgentActiveLeads(UserId, ProductId, false, _oneLeadParams, productCheck, _GroupID);// getting Agent all Active Leads
            List<PriorityModel> lstRest1stAttemptLeads = new List<PriorityModel>();

            lstPriorityModel = getPriorityQueue(lstPriorityModel, lstAgentAssignedLeads, lstRest1stAttemptLeads, priorityleadCount);
            return lstPriorityModel;

        }
        public List<PriorityModel> getBookedLead(Int16 ProductId, Int64 UserId, Int16 priorityleadCount)
        {
            return new List<PriorityModel>();
        }
        // get agent priority queue
        public List<PriorityModel> getPriorityQueue(List<PriorityModel> lstPriorityModel, List<PriorityModel> lstAgentAssignedLeads, List<PriorityModel> lstRest1stAttemptLeads, Int16 PriorityQueueSize, Int16 ProductID = 0, Int64 UserID = 0)
        {
            try
            {
                foreach (var prioritySequence in _LeadPriorityConstants.prioritySequence)
                {
                    List<PriorityModel>? _filteredleads = null;
                    switch (prioritySequence.ToUpper())
                    {
                        case "ACTIVEREVISIT":
                            _filteredleads = _OneLeadData.GetActiveRevisitLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTACTIVEREVISIT":
                            _filteredleads = _OneLeadData.Get2ndAttemptActiveRevisit(lstAgentAssignedLeads, counter); // 2nd attempt act revisit 
                            break;
                        case "ACTIVENEW":
                            _filteredleads = _OneLeadData.GetActiveNewLead(lstAgentAssignedLeads, counter);  // active new
                            break;
                        case "2NDATTEMPTACTIVENEW":
                            _filteredleads = _OneLeadData.Get2ndAttemptActiveNew(lstAgentAssignedLeads, counter);  // 2nd attempt active new
                            break;
                        case "PAYMENTCB":
                            _filteredleads = GetPaymentCBLead(lstAgentAssignedLeads, counter);  // payment cb
                            break;
                        case "2NDATTEMPTPCB":
                            _filteredleads = Get2ndAttemptPCBLeads(lstAgentAssignedLeads, counter);  // 2nd attempt payment cb
                            break;
                        case "EMAILREVISIT":
                            _filteredleads = _OneLeadData.GetEmailRevisitLeads(lstAgentAssignedLeads, counter);  // Email Revisit
                            break;
                        case "2NDATTEMPTEMAILREVISIT":
                            _filteredleads = _OneLeadData.Get2ndAttemptEmailRevisit(lstAgentAssignedLeads, counter);  // 2nd attempt Email Revisit
                            break;
                        case "PAYMENTFAILURE":
                            _filteredleads = OneLeadData.GetPaymentFailureLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTPAYMENTFAILURE":
                            _filteredleads = _OneLeadData.Get2ndAttemptPaymentFailureLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "ACTIVECB":
                            _filteredleads = GetActiveCBLeads(lstAgentAssignedLeads, counter);  // active call back
                            break;
                        case "2NDATTEMPTACTIVECB":
                            _filteredleads = Get2ndAttemptActiveCB(lstAgentAssignedLeads, counter);  // 2nd atmpactive call back
                            break;
                        case "PASSIVENEW":
                            _filteredleads = _OneLeadData.GetPassiveNewLead(lstAgentAssignedLeads, counter); // passive new
                            break;
                        case "2NDATTEMPTPASSIVENEW":
                            _filteredleads = _OneLeadData.Get2ndAttemptPassiveNew(lstAgentAssignedLeads, counter); // 2nd atmp passive new
                            break;
                        case "PASSIVEREVISIT":
                            _filteredleads = _OneLeadData.GetPassiveRevisitLead(lstAgentAssignedLeads, counter); // passive revisit
                            break;
                        case "2NDATTEMPTPASSIVEREVISIT":
                            _filteredleads = _OneLeadData.Get2ndAttemptPassiveRevisit(lstAgentAssignedLeads, counter); // 2nd atmp passive new
                            break;
                        case "PASSIVECB":
                            _filteredleads = GetPassiveCBLead(lstAgentAssignedLeads, counter); // passive CALL BACK)
                            break;
                        case "2NDATTEMPTPASSIVECB":
                            _filteredleads = Get2ndAttemptPassiveCB(lstAgentAssignedLeads, counter); // 2nd atmp passive CALL BACK)
                            break;
                        case "EXPIRY":
                            _filteredleads = GetExpiryDateLeads(lstAgentAssignedLeads, counter);// expiryLeads
                            break;
                        case "CALLRELEASE":
                            _filteredleads = CallReleaseLeads(lstAgentAssignedLeads, counter);   // call released
                            break;
                        case "UNANSWERED":
                            _filteredleads = _OneLeadData.GetUnansweredLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "REVISITCTC":
                            _filteredleads = _OneLeadData.GetCTCRevisitLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTREVISITCTC":
                            _filteredleads = _OneLeadData.Get2ndAttemptCTCRevisit(lstAgentAssignedLeads, counter);
                            break;
                        case "ANSWERED":
                            _filteredleads = _OneLeadData.GetAnsweredLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "PROPOSALERROR":
                            _filteredleads = _OneLeadData.GetProposalErrorLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "QUOTEREVISIT":
                            _filteredleads = _OneLeadData.GetQuoteRevisitLeads(lstAgentAssignedLeads, counter);  // Email Revisit
                            break;
                        case "TODAYEXPIRY":
                            _filteredleads = _OneLeadData.GetTodayExpiryLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "2NDATTEMPTTODAYEXPIRY":
                            _filteredleads = _OneLeadData.Get2ndAttemptTodayExpiryLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "EXPIRY0107DAYS":
                            _filteredleads = _OneLeadData.GetExpiryLeads(lstAgentAssignedLeads, counter, 0, 7, true, 0);
                            break;
                        case "EXPIRY0815DAYS":
                            _filteredleads = _OneLeadData.GetExpiryLeads(lstAgentAssignedLeads, counter, 7, 15, false, 0);
                            break;
                        case "EXPIRY1630DAYS":
                            _filteredleads = _OneLeadData.GetExpiryLeads(lstAgentAssignedLeads, counter, 15, 30, false, 0);
                            break;
                        case "EXPIRY3145DAYS":
                            _filteredleads = _OneLeadData.GetExpiryLeads(lstAgentAssignedLeads, counter, 30, 45, false, 0);
                            break;
                        case "GRACE0107DAYS":
                            _filteredleads = _OneLeadData.GetGraceLeads(lstAgentAssignedLeads, counter, 0, 7, true, 0);
                            break;
                        case "GRACE0890DAYS":
                            _filteredleads = _OneLeadData.GetGraceLeads(lstAgentAssignedLeads, counter, 7, 90, false, 0);
                            break;
                        case "SERVICECB":
                            _filteredleads = _OneLeadData.GetServiceCBLeads(lstAgentAssignedLeads, counter);
                            break;
                        case "REST":
                            _filteredleads = _OneLeadData.GetRest_1Leads(lstAgentAssignedLeads, counter, _GroupID);
                            if (_filteredleads != null)
                                lstRest1stAttemptLeads.AddRange(_filteredleads.Where(p => !lstPriorityModel.Exists(X => X.LeadID == p.LeadID)));
                            break;
                    }

                    if (_filteredleads != null && _filteredleads.Count > 0)
                        lstPriorityModel.AddRange(_filteredleads.Where(p => !lstPriorityModel.Exists(X => X.LeadID == p.LeadID)));
                    if (lstPriorityModel.Count >= PriorityQueueSize)
                        return lstPriorityModel;

                    counter += 1;
                }
            }
            catch (Exception)
            {

            }

            finally
            {
                LeadPrioritizationDLL.UpdateRestFlag(lstRest1stAttemptLeads);
            }

            return lstPriorityModel;
        }


        //Get ALL PaymentCB Lead in next 15mints
        private List<PriorityModel> GetPaymentCBLead(List<PriorityModel> lstAgentActiveLeads, Int16 counter)
        {
            List<PriorityModel> lstPaymentCBLead = new List<PriorityModel>();
            try
            {
                List<PriorityModel> lstAgentLeads = new List<PriorityModel>(lstAgentActiveLeads);
                Int16 PaymentCBShowTime = _LeadPriorityConstants.PaymentCBShowTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstAgentActiveLeads != null && lstAgentActiveLeads.Count > 0)
                {
                    lstPaymentCBLead = lstAgentLeads
                                       .Where(x => (x.CallBack != null) && (x.CallBack.IsPaymentCB == true)// PaymentCallBack
                                             && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes > PaymentCBShowTime)// -15 mints to Till Time
                                           && (x.Call == null || x.Call.TalkTime > 0 || DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// Not visible if NANC in last 30 mints
                                             && (x.Call == null || (
                                                                         (x.Call.calltime.AddSeconds(TimeDiffDailer) - (x.CallBack.ts > x.CallBack.CBtime.AddMinutes(PaymentCBShowTime) ?
                                                                                x.CallBack.ts : x.CallBack.CBtime.AddMinutes(PaymentCBShowTime))).TotalMinutes < 0)))// no  call after Max(CBSetTime,CBTime-15)
                                    .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.PaymentCB, counter)).OrderByDescending(x => x.CallBack.CBtime)
                                       .ToList();

                }
            }
            catch (Exception)
            {

            }
            return lstPaymentCBLead;
        }


        //Get ALL active callBackLeads in next and before 15 mints apart from PaymentCB
        private List<PriorityModel> GetActiveCBLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstActiveCBLeads = new List<PriorityModel>();
            try
            {
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                Int16 ActiveCBEndMints = _LeadPriorityConstants.ActiveCBEndTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstActiveCBLeads = lstPriorityModel.Where(x => (x.CallBack != null)
                                                             && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes >= ActiveCBShowTime * -1) //  show before 5 mints till after 15 mints 
                                                             && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes <= ActiveCBEndMints) //  show before 5 mints till after 15 mints 
                                                             && (x.Call == null || x.Call.TalkTime != 0 || DateTime.Now.Subtract(x.Call.calltime.AddMinutes(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// Not visible if NANC in last 30 mints
                                                             && (x.Call == null ||
                                                                    (x.Call.calltime.AddSeconds(TimeDiffDailer) < (x.CallBack.ts > x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1) ?
                                                                    x.CallBack.ts : x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1)))))// no  call after Max(CBSetTime,CBTime-15) 
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.ActiveCB, counter))
                  .OrderByDescending(x => Math.Abs(x.CallBack.CBtime.Subtract(DateTime.Now).Seconds)).ToList();// sorting by nearest callback
                }
            }
            catch (Exception)
            {

            }

            return lstActiveCBLeads;
        }


        // GetPassiveCB
        private List<PriorityModel> GetPassiveCBLead(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstPassiveCBLead = new List<PriorityModel>();
            try
            {
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                Int16 ActiveCBEndTime = _LeadPriorityConstants.ActiveCBEndTime;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short PassivEnd = _LeadPriorityConstants.PassiveCBEndMints;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstPassiveCBLead = lstPriorityModel.Where(x => (x.CallBack != null)
                                                              && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes > ActiveCBEndTime) //  will be visible after 15mints of CB
                                                              && (DateTime.Now.Subtract(x.CallBack.CBtime).TotalMinutes < PassivEnd) //  will be visible between 15mints  to 24hrs of CB
                                                              && (x.Call == null || (x.Call.calltime.AddSeconds(TimeDiffDailer) < (x.CallBack.ts > x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1) ?
                                                                                   x.CallBack.ts : x.CallBack.CBtime.AddMinutes(ActiveCBShowTime * -1)))
                                                                                   ))// no call Attempt after Max(CBSetTime,CBTime-15) 
                                                    .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.PassiveCB, counter))
                                                    .OrderByDescending(x => x.CallBack.CBtime).ToList();// sorting by nearest callback
                }
            }
            catch (Exception)
            {

            }
            return lstPassiveCBLead;
        }


        //Get Payment Call Leadsfor2ndAttempt whose first attempt was NANC
        private List<PriorityModel> Get2ndAttemptPCBLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lst2ndAttemptPCBLeads = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short PCBTimeRange = _LeadPriorityConstants.PaymentCBShowTime;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptPCBLeads = lstPriorityModel.Where(x => (x.CallBack != null) && (x.CallBack.IsPaymentCB == true)   // checking for paymentCB
                                                                   && (x.Call != null) && (x.Call.TalkTime == 0 && x.Call.Duration > 0)// last call NANC
                                                                                                                                       //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt
                                                                   && (x.Call.NANC_Attempts % 2 == 1)// odd attempts
                                                                   && (x.IsLastBucketRest == false) // lastly not in RestBucket
                                                                   && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// lst NANC call was before 30 mints
                                                                   && (x.CallBack.ts < x.Call.calltime.AddSeconds(TimeDiffDailer))// NANC Attempt done after the the setting time of PCB
                                                                   && x.Call.calltime > x.CallBack.CBtime.AddMinutes(PCBTimeRange))// last attempt was done after PCB time -15 mints time
                                                                   .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptPCB, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lst2ndAttemptPCBLeads;
        }


        // Get ActiveCB for 2nd Attempt
        private List<PriorityModel> Get2ndAttemptActiveCB(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lst2ndAttemptActiveCB = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                Int16 ActiveCBEndTime = _LeadPriorityConstants.ActiveCBEndTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptActiveCB = lstPriorityModel.Where(x => (x.Call != null && x.CallBack != null) && (x.Call.TalkTime == 0)    // last Attempt NANC
                                                                                                                                            //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt   
                                                                    && (x.Call.NANC_Attempts % 2 == 1)// odd attempts
                                                                    && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC)// last NANC call was before 30 mints  
                                                                    && (x.Call.calltime.AddSeconds(TimeDiffDailer).Subtract(x.CallBack.CBtime).TotalMinutes <= ActiveCBEndTime)
                                                                    && (x.Call.calltime.AddSeconds(TimeDiffDailer).Subtract(x.CallBack.CBtime).TotalMinutes >= ActiveCBShowTime * -1)
                                                                    )//last NANC Attempt was before or after 15 mints from CB time
                                                           .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptActvCB, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lst2ndAttemptActiveCB;
        }


        //Get Passive CB for 2nd Attempt
        private List<PriorityModel> Get2ndAttemptPassiveCB(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lst2ndAttemptPassiveCB = new List<PriorityModel>();
            try
            {
                Int16 InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                Int16 ActiveCBShowTime = _LeadPriorityConstants.ActiveCBShowTime;
                Int16 ActvCBEndTime = _LeadPriorityConstants.ActiveCBEndTime;
                Int16 PntStndrdVal = _LeadPriorityConstants.PntStndrdVal;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                short PassivEnd = _LeadPriorityConstants.PassiveCBEndMints;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lst2ndAttemptPassiveCB = lstPriorityModel.Where(x => (x.Call != null && x.CallBack != null) && (x.Call.TalkTime == 0)   // last Attempt NANC
                                                                                                                                            //&& (x.LeadPoints >= PntStndrdVal) // pnt greater than 12 eligble for 2nd attempt
                                                                     && (x.IsLastBucketRest == false) // lastly not in RestBucket
                                                                     && (x.Call.NANC_Attempts % 2 == 1)// so that lead do not keep on coming in the same bucket for ever
                                                                     && (x.CallBack.CBtime.AddMinutes(ActvCBEndTime) <= x.Call.calltime.AddSeconds(TimeDiffDailer))  // last Attempt was done after 15 MIN of CB TIME
                                                                     && (x.CallBack.CBtime.AddMinutes(PassivEnd) >= x.Call.calltime.AddSeconds(TimeDiffDailer))  // last Attempt was done till  24 hrs MIN of CB TIME
                                                                     && (DateTime.Now.Subtract(x.Call.calltime.AddSeconds(TimeDiffDailer)).TotalMinutes > InvisibleTimeNANC))// last NANC Was before 30 mints (means moving out of bucket)
                  .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.SecondAttemptPasvCB, counter)).OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lst2ndAttemptPassiveCB;
        }


        //GetRestLeads
        private List<PriorityModel> GetRest_1Leads(List<PriorityModel> lstPriorityModel, Int16 counter)// eventually means  Leads on which no work is done today
        {
            List<PriorityModel> lstRestLeads = new List<PriorityModel>();
            DateTime ct = DateTime.Now;
            try
            {
                Int16 ActivNewLeadShowTime = _LeadPriorityConstants.ActiveNewLeadShowTime;
                Int16 RestLeadCallMintsGap = _LeadPriorityConstants.RestLeadCallMintsGap;
                byte RestLeadLastCall = _LeadPriorityConstants.RestLeadLastCall;
                byte NANCMaxAttemInDiffShifts = _LeadPriorityConstants.NANCMaxAttemInDiffShifts;
                byte NANCLastCallDayGap = _LeadPriorityConstants.NANCLastCallDayGap;
                byte NANCMax1DayAttempt = _LeadPriorityConstants.NANCMax1DayAttempt;
                short InvisibleTimeNANC = _LeadPriorityConstants.InvisibleTimeNANC;
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(DateTime.Now);
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {

                    lstRestLeads = lstPriorityModel
                                        .Where(x =>
                                                ((x.Call == null || ct.Subtract(x.Call.calltime).TotalMinutes > RestLeadCallMintsGap) // NOT CALLED IN lAST 12 HRS ( not called toady)
                                                && (x.CallBack == null || ct.Subtract(x.CallBack.CBtime).TotalSeconds >= 0)// no future callback
                                                && (x.PrevPolicyExpDate > DateTime.MinValue)
                                                && (
                                                    // expiry day
                                                    (
                                                        x.PrevPolicyExpDate.Date == ct.Date
                                                    // && (x.Call == null || ct.Date.Subtract(x.Call.calltime.Date).TotalDays > 0)
                                                    )
                                                    ||
                                                    //before expiry
                                                    (
                                                        x.PrevPolicyExpDate.Date.Subtract(ct.Date).TotalDays > 0 && x.PrevPolicyExpDate.Date.Subtract(ct.Date).TotalDays <= 45
                                                    //&& (x.Call == null || ct.Date.Subtract(x.Call.calltime.Date).TotalDays > 0)
                                                    )
                                                    ||
                                                    //After expiry date
                                                    (ct.Date.Subtract(x.PrevPolicyExpDate.Date).TotalDays > 0 && ct.Date.Subtract(x.PrevPolicyExpDate.Date).TotalDays <= 45
                                                     //&& (x.Call == null || ct.Date.Subtract(x.Call.calltime.Date).TotalDays > 1)
                                                     )

                                                )
                                            )
                                        ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.RestLeads_1, counter))
                                        .OrderBy(x => x.Call == null ? x.LeadCreatedOn : x.Call.calltime).ToList();

                }

            }
            catch (Exception)
            {

            }
            return lstRestLeads;
        }


        public List<PriorityModel> GetExpiryDateLeads(List<PriorityModel> lstPriorityModel, Int16 counter)
        {
            List<PriorityModel> lstTodayExpiry = new List<PriorityModel>();
            try
            {
                short TimeDiffDailer = _LeadPriorityConstants.TimeDifferenceDailerNTata;
                Int16 TodyCreatdExprGap = _LeadPriorityConstants.TodayCreatedExprTimeGap;
                Int16 BefTodyCreatdExprGap = _LeadPriorityConstants.BeforeTodayCreatedExprTimeGap;
                byte ExpiryCount = _LeadPriorityConstants.ExpiryLeadsShowCount;
                string CurrentCallShift = LeadPrioritizationDLL.CalculateCallShift(DateTime.Now);
                DateTime ct = DateTime.Now;
                byte RecentExpiryDayGap = _LeadPriorityConstants.RecentExpiryGap;
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                {
                    lstTodayExpiry = lstPriorityModel
                                       .Where(x => //(Math.Abs(ct.Subtract(x.PrevPolicyExpDate).TotalDays)  <= RecentExpiryDayGap)
                                           (x.PrevPolicyExpDate.Date == ct.Date || x.PrevPolicyExpDate.Date == ct.AddDays(1).Date || x.PrevPolicyExpDate.Date == ct.AddDays(-1).Date)// Expiring today or tomorrow or expired one day before
                                          && (x.CallBack == null || x.CallBack.CBtime < ct) // no future call back
                                           && (x.Call != null && ct.Subtract(x.Call.calltime).TotalMinutes > TodyCreatdExprGap) // for today created check                                           
                                           )
                                        .Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.RecentExpiry, counter))
                                        .OrderBy(x => x.Call.calltime).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstTodayExpiry;
        }

        public productPriorityconstant getPriorityConfigByProduct(Int16 productID)
        {
            LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
            var obj = _LeadPriorityConstants.PriorityConstant.Where(p => (p.productID == productID) && (p.subProduct == "RENEWAL")).SingleOrDefault();
            if (obj == null)
                throw new NotImplementedException("getPriorityConfigByProduct config is empty");
            return obj;
        }

        public static List<PriorityModel> CallReleaseLeads(List<PriorityModel> lstAgentActiveLeads, Int16 counter)
        {
            List<PriorityModel> lstReleasedCallleads = new List<PriorityModel>();
            try
            {
                DateTime ct = DateTime.Now;
                List<PriorityModel> lstAgentLeads = new List<PriorityModel>(lstAgentActiveLeads);
                LeadPriorityConstants _LeadPriorityConstants = PriorityConfig.getConfig();
                if (lstAgentActiveLeads != null && lstAgentActiveLeads.Count > 0)
                {
                    lstReleasedCallleads = lstAgentLeads
                                       .Where(x => (x.CallReleaseCount > 0)
                                                    // && (x.Call != null && DateTime.Now.Subtract(x.Call.calltime).TotalMinutes < _LeadPriorityConstants.Releaseleadsshowtime)
                                                    && (x.SkippingTime < x.CallReleaseTime) //  so that if release after skipping then it should come in priority
                                              ).Select(X => LeadPrioritizationDLL.GetSelectedData(X, LeadCategoryEnum.CallReleasedLeads, counter)).ToList();
                }
            }
            catch (Exception)
            {

            }
            return lstReleasedCallleads;
        }

    }
}
