﻿using DataAccessLayer;
using DataAccessLibrary;
using DataHelper;
using Helper;
using Microsoft.Extensions.Options;
using Microsoft.VisualBasic;
using MongoDB.Driver.Builders;
using PropertyLayers;
using ReadXmlProject;
using System;
using System.Data;
using System.Dynamic;

namespace DialerDataUpdate
{
    public class InsertUpdateCallData
    {
        public static Int64 GetUserId(string EmpCode)
        {
            Int64 UserID = 0;
            if (!string.IsNullOrEmpty(EmpCode))
            {
                var _masterData = MasterData.EmployeeMaster();
                if (_masterData != null && _masterData.ContainsKey(EmpCode))
                {
                    var userData = _masterData[EmpCode];
                    UserID = Convert.ToInt64(userData.Split("-")[0]);
                }

                if (UserID == 0)
                    UserID = PredictiveAgentStatusRedis.GetUserIdByRedis(EmpCode);
            }
            return UserID;
        }
        public static int UpdateDataonAgentAnswered(DialerDispDetails _DialerDispositionUpdate, ref string strResponse)
        {         
            Int64 userid = GetUserId(_DialerDispositionUpdate.AgentCode);
            UpdateRetainerCallStatus(_DialerDispositionUpdate, "UpdateDataonAgentAnswered", userid);
            LeadAssignmnet(_DialerDispositionUpdate);//Lead assignment to current agent based on call type
            LeadDetailResponse _LeadDetailResponse = new LeadDetailResponse();
            
            if (userid > 0 && !_DialerDispositionUpdate.AgentCode.ToUpper().StartsWith("B") && !string.IsNullOrEmpty(_DialerDispositionUpdate.CallType) && (_DialerDispositionUpdate.CallType == "IB" || _DialerDispositionUpdate.CallType == "PDOB" || _DialerDispositionUpdate.CallType == "CTCOB"))
            {
                _LeadDetailResponse = ChatData.getLeadDetailsforChat(_DialerDispositionUpdate.LeadID);
                string Reason = "New Lead";
                Byte ReasonID = 3;
                string Disposition = "Call Initiated";
                if (_DialerDispositionUpdate.CallType == "IB")
                {
                    Reason = "Inbound";
                    ReasonID = 35;
                    Disposition = "Connected";
                }
                if (_DialerDispositionUpdate.CallType == "PDOB")
                {
                    Reason = "Predective Inbound";
                    ReasonID = 37;
                    Disposition = "Connected";
                }

                UserNext5Leads _UserNext5Leads = new UserNext5Leads();
                _UserNext5Leads.UserId = userid;

                Next5WidgetLead oNext5WidgetLead = new Next5WidgetLead();
                oNext5WidgetLead.LeadId = _DialerDispositionUpdate.LeadID;
                oNext5WidgetLead.Reason = Reason;
                oNext5WidgetLead.ReasonId = ReasonID;
                oNext5WidgetLead.ts = DateTime.Now;
                oNext5WidgetLead.CallStatus = Disposition;
                if (_LeadDetailResponse != null && _LeadDetailResponse.LeadData != null)
                {
                    oNext5WidgetLead.Name = _LeadDetailResponse.LeadData.CustomerName;
                    oNext5WidgetLead.CustomerId = _LeadDetailResponse.LeadData.CustID;
                    oNext5WidgetLead.ProductId = _LeadDetailResponse.LeadData.ProductID;
                }
                _UserNext5Leads.Leads = new List<Next5WidgetLead> { oNext5WidgetLead };
                LeadAllocationData.AddLeadToPriorityQueue(_UserNext5Leads);
            }
            else if (userid > 0)
                LeadAllocationData.UpdateCallStatusOnCallInitiate(_DialerDispositionUpdate.LeadID, "Connected", userid);

            if (!string.IsNullOrEmpty(_DialerDispositionUpdate.AgentCode) && !_DialerDispositionUpdate.AgentCode.ToUpper().StartsWith("B") && _DialerDispositionUpdate.CallType == "C2C") // only for sales calls
            {
                if (_LeadDetailResponse == null || _LeadDetailResponse.LeadData == null)
                    _LeadDetailResponse = ChatData.getLeadDetailsforChat(_DialerDispositionUpdate.LeadID);//Get Lead and customer data

                if (_LeadDetailResponse != null && _LeadDetailResponse.LeadData != null && _LeadDetailResponse.LeadData.CustID > 0 && _LeadDetailResponse.LeadData.ProductID == 7 && !string.IsNullOrEmpty(_DialerDispositionUpdate.AgentCode)) // notify term api for agenr answered
                {
                    UpdateTermEvents(_LeadDetailResponse.LeadData.CustID, _LeadDetailResponse.LeadData.ParentLeadID, _DialerDispositionUpdate.AgentCode, _DialerDispositionUpdate.CallType, "CTC_CALL_CONNECTED");
                }
            }

            if (!string.IsNullOrEmpty(_DialerDispositionUpdate.CallId) && _DialerDispositionUpdate.CallId.StartsWith("0"))
                LeadAllocationData.AddAutoComments(_DialerDispositionUpdate.LeadID, userid);
            return 1;
        }
        private static void LeadAssignmnet(DialerDispDetails dialerDispositionUpdate)
        {
                long leadId = dialerDispositionUpdate.LeadID;
                if (dialerDispositionUpdate.NewLeadID > 0) // changes for retainers new lead creation
                    leadId = dialerDispositionUpdate.NewLeadID;
                string agentCode = dialerDispositionUpdate.AgentCode;
                if (dialerDispositionUpdate.CallType.ToUpper() == "PDOB" && leadId > 0 && !string.IsNullOrEmpty(agentCode))
                {
                    LeadAllocationData.AssignLead(leadId, agentCode, 0);
                }                
        }
        public static int HangUpOBCall(DialerDispDetails _DialerDispositionUpdate)
        {

            Int64 userid = GetUserId(_DialerDispositionUpdate.AgentCode);
            UpdateRetainerCallStatus(_DialerDispositionUpdate, "HangUpOBCall", userid);
            if (_DialerDispositionUpdate.CallType?.ToUpper() != "SCREENSHARE" && userid > 0)
                LeadPrioritizationDLL.UpdateCallStatusOnCallInitiate(_DialerDispositionUpdate.LeadID, _DialerDispositionUpdate.talktime > 0 ? "Answered" : "NotAnswered", userid);
            return 1;
        }
        private static void UpdateRetainerCallStatus(DialerDispDetails objDialerDisp, string type, Int64 UserID)
        {            
            if (objDialerDisp == null)
                return;
            //MongoHelper chatDb = new MongoHelper(SingletonClass.ChatDB());
            MongoHelper oneleaddb = new MongoHelper(SingletonClass.OneLeadDB());
            UpdateBuilder<RetainerPredictiveDialing> update = null;
            int minPointDeductTime = Convert.ToInt16("minPointDeductTime".AppSettings());

            if (objDialerDisp.LeadID > 0 && !string.IsNullOrEmpty(objDialerDisp.AgentCode) && type.Equals("UpdateDataonAgentAnswered"))
            {
                PredictiveAgentStatus _PredictiveAgentStatus = new PropertyLayers.PredictiveAgentStatus();

                if (!string.IsNullOrEmpty(objDialerDisp.CallType))
                    _PredictiveAgentStatus.CallType = objDialerDisp?.CallType?.ToUpper();
                _PredictiveAgentStatus.status = "BUSY";
                _PredictiveAgentStatus.LeadId = Convert.ToString(objDialerDisp?.LeadID);
                _PredictiveAgentStatus.AgentCode = objDialerDisp.AgentCode;
                _PredictiveAgentStatus._updatedAt = DateTime.Now;
                _PredictiveAgentStatus.IsCustAnswered = false;
                _PredictiveAgentStatus.CallId = objDialerDisp?.CallId;

                //updateAgentStatus(_PredictiveAgentStatus);                                  
            }
            else if (type.Equals("HangUpOBCall"))
            {
                /*update predective lead status*/
                if ((objDialerDisp.CallType.ToUpper() == "PDOB" && objDialerDisp.talktime > 0)
                    || (!string.IsNullOrEmpty(objDialerDisp.Disposition) && objDialerDisp.Disposition.ToUpper() == "ABANDON"))
                {
                    update = Update<RetainerPredictiveDialing>.Set(p => p.Status, "Answered");
                    var query = Query<RetainerPredictiveDialing>.EQ(p => p.LeadID, objDialerDisp.LeadID);
                    oneleaddb.UpdateDocument(query, update, MongoCollection.RetainerPredictive());
                }
                /*update predective lead status*/

                string AgentData = "HANGUP";//PredictiveAgentStatusRedis.GetAgentStatus(UserID);

                PredictiveAgentStatus _PredictiveAgentStatus = new PropertyLayers.PredictiveAgentStatus();
                _PredictiveAgentStatus.AgentCode = objDialerDisp.AgentCode;
                _PredictiveAgentStatus.CallType = objDialerDisp?.CallType?.ToUpper();
                _PredictiveAgentStatus.status = AgentData;
                _PredictiveAgentStatus.Document = new RetainerPredictiveDialing();
                _PredictiveAgentStatus.TotalCalls = objDialerDisp.Duration > minPointDeductTime && objDialerDisp.Context != "twowaycall" ? 1 : 0;
                _PredictiveAgentStatus.opensv = 0;
                _PredictiveAgentStatus.TotalTalkTime = objDialerDisp.talktime;
                _PredictiveAgentStatus.LeadId = Convert.ToString(objDialerDisp?.LeadID);
                _PredictiveAgentStatus._updatedAt = DateTime.Now;
                _PredictiveAgentStatus.IsCustAnswered = false;
                _PredictiveAgentStatus.CallId = objDialerDisp?.CallId;

                if (objDialerDisp.LeadID > 0)
                {
                    PriorityModel LpDatadoc = LeadPrioritizationDLL.GetLeadPriorityModel(Convert.ToInt64(objDialerDisp.LeadID));
                    if (LpDatadoc != null && objDialerDisp.Duration > minPointDeductTime && objDialerDisp.Context != "twowaycall"
                        && (
                            LpDatadoc.Call == null
                            || (LpDatadoc.Call != null && LpDatadoc.Call.calltime.Date != DateTime.Now.Date)
                            || (LpDatadoc.Call != null && LpDatadoc.Call.calltime.Date == DateTime.Now.Date && LpDatadoc.Call.TodaysAttempt < 1 && LpDatadoc.Call.IsProcessed == false)
                            )
                    )
                    {
                        _PredictiveAgentStatus.TotalUniqueCalls = 1;
                    }
                }
                _PredictiveAgentStatus.UserId = Convert.ToString(UserID);
                if (!string.IsNullOrEmpty(_PredictiveAgentStatus.AgentCode) && !string.IsNullOrEmpty(_PredictiveAgentStatus.UserId))
                    PredictiveAgentStatusRedis.updateAgentStatusByRedis(_PredictiveAgentStatus, false);
            }
                      
        }
        public static void AddRecordingToQueue(DialerDispDetails _DispositionUpdate)
        {
            RecordingSQS _RecordingSQS = null;
            AmazonSqs amazonSqs = new AmazonSqs();
            string SQSMsg = string.Empty;
            List<string> Contexts = new List<string>() { "ASWAT", "ICALLOB", "ASWATINDIA" };
            string exMsg = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(_DispositionUpdate.Context) && Contexts.Contains(_DispositionUpdate.Context.ToUpper()) && _DispositionUpdate.talktime > 0)
                {
                    string URL = "RecordingSqsURL".AppSettings();
                    _RecordingSQS = new RecordingSQS()
                    {
                        CallDataID = _DispositionUpdate.CallTrackingID,
                        CallDate = _DispositionUpdate.callDate,
                        CallID = _DispositionUpdate.CallId,
                        LeadID = Convert.ToString(_DispositionUpdate.LeadID),
                        company = _DispositionUpdate.Context.ToUpper() == "ASWATINDIA" ? "ASWATINDIA" : "Aswat",
                        is_internal = false
                    };

                    SQSMsg = Newtonsoft.Json.JsonConvert.SerializeObject(_RecordingSQS);
                    amazonSqs.SQSSendMessage(URL, _RecordingSQS, 5);
                }
            }
            catch (Exception Ex)
            {
                exMsg = Ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(_DispositionUpdate.LeadID.ToString(), _DispositionUpdate.LeadID, exMsg, "AddRecordingToQueue", "InsertUpdateCallData", "OneLead", SQSMsg, string.Empty, DateTime.Now, DateTime.Now);
            }
        }
        public static void PushDatatoBMSSQS(DialerDispDetails _DispositionUpdate)
        {
            AmazonSqs amazonSqs = new AmazonSqs();
            string SQSMsg = string.Empty;
            string exMsg = string.Empty;
            DateTime requestTime = DateTime.Now;
            string Disposition = string.Empty;
            string C2CSource = string.Empty;
            if (_DispositionUpdate.Status == "98")
                Disposition = "COMPLETEAGENT";
            else if (_DispositionUpdate.Status == "99")
                Disposition = "COMPLETECALLER";
            try
            {
                if (!string.IsNullOrEmpty(_DispositionUpdate.CallType) && _DispositionUpdate.CallType.ToUpper() == "C2C" && _DispositionUpdate.C2CID > 0)
                {
                    DataSet ds = LeadAllocationData.GetC2CDetails(_DispositionUpdate.C2CID, _DispositionUpdate.LeadID);
                    if(ds!=null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        if (ds.Tables[0].Rows[0]["RequestSource"] == null || ds.Tables[0].Rows[0]["RequestSource"] == DBNull.Value)
                        {
                            C2CSource = "";
                        }
                        else
                        {
                            C2CSource = Convert.ToString(ds.Tables[0].Rows[0]["RequestSource"]);
                        }
                    }
                }
                //string URL = "BMSSqsURL".AppSettings();
                dynamic obj = new
                {
                    LeadID = _DispositionUpdate.LeadID,
                    CallId = _DispositionUpdate.CallId,
                    CallingNo = _DispositionUpdate.CallingNo,
                    EmployeeId = _DispositionUpdate.AgentCode,
                    CallDate = _DispositionUpdate.callDate,
                    CallType = _DispositionUpdate.CallType,
                    Duration = _DispositionUpdate.Duration,
                    Talktime = _DispositionUpdate.talktime,
                    Disposition = Disposition == string.Empty?_DispositionUpdate.Disposition: Disposition,
                    Context = _DispositionUpdate.Context,
                    CallTrackingID = _DispositionUpdate.CallTrackingID,
                    C2CID = _DispositionUpdate.C2CID,
                    AsteriskIP = _DispositionUpdate.AsteriskIP,
                    RecFileName = _DispositionUpdate.recfile,
                    TransferType = _DispositionUpdate.t_type,
                    Phone = string.IsNullOrEmpty(_DispositionUpdate.dst) ? _DispositionUpdate.MobileNo : _DispositionUpdate.dst,
                    ProductID = _DispositionUpdate.ProductID,
                    C2CSource= C2CSource,
                    IsLastTransfer= _DispositionUpdate.Status=="500"?true:false,
                    EventName ="calldataevent"
                };

                //amazonSqs.SQSSendMessage(URL, obj,0,2000);                                
                KafkaWrapper.PushToKafka(obj, "bms-calldata-topic");
                SQSMsg = Newtonsoft.Json.JsonConvert.SerializeObject(obj);
            }
            catch (Exception Ex)
            {
                exMsg = Ex.ToString();
            }

            finally
            {                
                LoggingHelper.LoggingHelper.AddloginQueue(_DispositionUpdate.LeadID.ToString(), _DispositionUpdate.LeadID, exMsg, "PushDatatoBMSSQS", "InsertUpdateCallData", "OneLead", SQSMsg, string.Empty, requestTime, DateTime.Now);
            }
        }
        public static void UpdateTermEvents(long customerId, long leadId, string EmpID,string CallType,string eventName,int Talktime=0)
        {
            try
            {
                string SQSMsg = string.Empty;

                AmazonSqs amazonSqs = new AmazonSqs();

                string AgentName = string.Empty;
                if (!string.IsNullOrEmpty(EmpID))
                {
                    var _masterData = MasterData.EmployeeMaster();
                    if (_masterData.ContainsKey(EmpID))
                    {
                        var userData = _masterData[EmpID];
                        AgentName = Convert.ToString(userData.Split("-")[1]);
                    }
                }
                //string EventUrl = "termevent".AppSettings();
                dynamic obj = new
                {
                    time = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    customerId = customerId,
                    leadId = leadId,
                    eventName = eventName,
                    agentName = AgentName,
                    agentCode = EmpID,
                    collection = "MATRIX_OPERATION",
                    jobid = CallType == "C2C" ? 2 : 1,
                    talktime = Talktime
                };
                string URL = "TermEventSqsURL".AppSettings();

                amazonSqs.SQSSendMessage(URL, obj, 0, 2000);
                //CommonAPICall.PostAPICall(EventUrl, 1000, Newtonsoft.Json.JsonConvert.SerializeObject(obj), string.Empty, null);
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.ToString(), "UpdateTermEvents", "InsertUpdateCallData", "OneLead", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
        }
    }
}