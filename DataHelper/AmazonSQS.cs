﻿using Amazon.SQS;
using Amazon.SQS.Model;
using System;
using System.Threading;

namespace DataHelper
{
    public class AmazonSqs
    {
        public void SQSSendMessage<T>(string Url, T MsgObj, int DelaySeconds = 0,double Timeout=500)
        {
            //Console.WriteLine("Enter SQSSendMessage.");
            try
            {
                string SqsMsg = Newtonsoft.Json.JsonConvert.SerializeObject(MsgObj);
                using (var client = new AmazonSQSClient(Amazon.RegionEndpoint.APSouth1))
                {
                    var request = new SendMessageRequest()
                    {
                        QueueUrl = Url,
                        MessageBody = SqsMsg,
                        DelaySeconds = DelaySeconds,
                    };

                    //Need To verfiy  TODO List
                    //SendMessageResponse response = client.SendMessage(request);
                    //var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromMilliseconds(Timeout));
                    //var response = client.SendMessageAsync(request, cancellationTokenSource.Token);
                    var response = client.SendMessageAsync(request);
                    /*
                    if (response!=null && response.Exception!=null && !string.IsNullOrEmpty(response.Exception.Message))
                    {
                        Console.WriteLine("ResponseExceptionInSQSSendMessageMethod." + response.Exception.Message.ToString());
                    }*/
                }
            }
            catch(Exception ex)
            {
                Console.WriteLine("ExceptionInSQSSendMessageMethod." + ex.ToString());
            }
        }
    }
}
