<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Platforms>AnyCPU;x86</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Amazon.Sqs" Version="0.20.4" />
    <PackageReference Include="AWSSDK.Core" Version="3.7.107.9" />
    <PackageReference Include="AWSSDK.SQS" Version="3.7.0.30" />
    <PackageReference Include="Experimental.System.Messaging" Version="1.1.0" />
    <PackageReference Include="Google.Apis" Version="1.51.0" />
    <PackageReference Include="Google.Apis.Auth" Version="1.51.0" />
    <PackageReference Include="Google.Apis.Sheets.v4" Version="1.51.0.2338" />
    <PackageReference Include="mongocsharpdriver" Version="2.12.3" />
    <PackageReference Include="MongoDB.Bson" Version="2.12.3" />
    <PackageReference Include="MongoDB.Driver" Version="2.12.3" />
    <PackageReference Include="MongoDB.Driver.GridFS" Version="2.12.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.9" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MongoConfigProject\MongoConfigProj.csproj" />
    <ProjectReference Include="..\ReadXmlProject\ReadXmlProject.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
  </ItemGroup>
</Project>
