﻿using Helper;
using Microsoft.Extensions.Configuration;
using MongoDB.Driver;
using MongoDB.Driver.Builders;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Caching;
using System.Text;
using System.Threading.Tasks;

namespace MongoConfigProject
{
    public static class MongoConfigProj
    {
        static MongoDatabase _LoggingDB;
        static MongoDatabase _OneLeadDB;

        public static string AppSettings(this string AppKey)
        {
            string KeyName = string.Empty;

            try
            {
                IConfiguration con = Custom.ConfigurationManager.AppSetting;
                string collection = con.GetSection("Communication").GetSection("ConfigKey").Value.ToString();

                if (!string.IsNullOrEmpty(collection))
                {
                    List<ConfigData> configValue = GetConfigFromMongo(collection);//for fos project

                    if (configValue.Count > 0)
                        KeyName = configValue.Where(x => x.key.Contains(AppKey) && x.key == AppKey).Select(x => x.value).FirstOrDefault();
                }
            }
            catch (Exception)
            {
                return KeyName;
            }
            return KeyName;
        }


        public static List<ConfigData> GetConfigFromMongo(string Collection)
        {
            List<ConfigData> sysConfigs = new();
            try
            {
                if (MemoryCache.Default[Collection] != null)
                    sysConfigs = (List<ConfigData>)MemoryCache.Default.Get(Collection);
                else
                {

                    IMongoFields Field = Fields.Include("_id", "key", "value");
                    MongoConfigHelper objCommDB = new MongoConfigHelper(OneLeadDB());
                    sysConfigs = objCommDB.GetDocuments<ConfigData>(null, Field, Collection);

                    if (sysConfigs.Count > 0)
                        CommonCache.GetOrInsertIntoCache(sysConfigs, Collection, 8 * 60);

                }
            }
            catch (Exception)
            {
                return null;
            }
            return sysConfigs;
        }

        public static MongoDatabase OneLeadDB()
        {
            string Enviornment = string.Empty;
            try
            {
                if (_OneLeadDB != null)
                {
                    return _OneLeadDB;
                }
                else
                {

                    IConfiguration con = Custom.ConfigurationManager.AppSetting;
                    Enviornment = CoreCommonMethods.GetEnvironmentVar();
                    string OneLeadDB = con.GetSection("Communication").GetSection("OneLeadDB").Value.ToString();
                    var connectionString = con.GetSection("Communication").GetSection("OneLeadDBConnection").GetSection(Enviornment).Value.ToString();

                    var client = new MongoClient(connectionString);
                    var server = client.GetServer();
                    _OneLeadDB = server.GetDatabase(OneLeadDB);
                    return _OneLeadDB;

                }
            }
            catch (Exception)
            {
                throw;
            }
        }
    }
}
