﻿using MongoDB.Bson;
using MongoDB.Driver.Core.Connections;
using MongoDB.Driver.GridFS;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MongoConfigProject
{
    public class MongoConfigHelper
    {

        MongoDatabase MongoDB;
        int _mongotimeout = 0;
        public ConnectionId ConnectionId { get; }
        public MongoConfigHelper(MongoDatabase db, int mongotimeout = 0)
        {
            MongoDB = db;
            this._mongotimeout = mongotimeout;
            if (_mongotimeout == 0)
                _mongotimeout = Convert.ToInt32(7000);

        }
        

        public List<T> GetDocuments<T>(IMongoQuery query, IMongoFields IncludeField, string CollectionTable) where T : class
        {
            List<T> objlist = new List<T>();
            try
            {
                objlist = MongoDB.GetCollection<T>(CollectionTable).Find(query).SetFields(IncludeField).ToList();
            }
            catch (Exception ex)
            {

            }



            //Thread t = new Thread(new ThreadStart(
            //() =>
            //{
            //try
            //{
            //    objlist= MongoDB.GetCollection<T>(CollectionTable).Find(query).SetFields(IncludeField).ToList();
            //}          
            //catch (Exception ex)
            //{

            //}
            //  }));
            //t.Start();
            //t.Join(_mongotimeout);

            //if (t.IsAlive)
            //{
            //try
            //{
            //    t.Interrupt();
            //}
            //catch (ThreadAbortException e)
            //{
            //}
            return objlist;
            //}
            //else
            //{
            //  return objlist;
            //}                                    
        }
        
    }
}